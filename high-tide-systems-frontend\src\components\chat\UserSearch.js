'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Search, X, Users, Building2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { ModuleSelect, ModuleInput } from '@/components/ui';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

const UserSearch = ({ onSelectUser, onClose }) => {
  const { user } = useAuth();
  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  // Obter o token diretamente do localStorage para garantir que esteja disponível
  const token = typeof localStorage !== 'undefined' ? localStorage.getItem('token') : null;

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCompany, setSelectedCompany] = useState('');
  const [selectedRole, setSelectedRole] = useState('');
  const [professions, setProfessions] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Carregar empresas (apenas para system_admin)
  useEffect(() => {
    if (!isSystemAdmin) return;

    // Tentar obter o token novamente caso não esteja disponível
    const currentToken = token || (typeof localStorage !== 'undefined' ? localStorage.getItem('token') : null);
    if (!currentToken) return;

    const fetchCompanies = async () => {
      try {
        const response = await fetch(`${API_URL}/companies/select`, {
          headers: { Authorization: `Bearer ${currentToken}` }
        });

        const data = await response.json();
        console.log('Resposta do endpoint companies:', data);
        if (data.success) {
          setCompanies(data.data);
        } else if (data.companies) {
          setCompanies(data.companies);
        }
      } catch (error) {
        console.error('Erro ao carregar empresas:', error);
      }
    };

    fetchCompanies();
  }, [isSystemAdmin]);

  // Carregar profissões
  useEffect(() => {
    if (!isSystemAdmin) return;

    const currentToken = token || (typeof localStorage !== 'undefined' ? localStorage.getItem('token') : null);
    if (!currentToken) return;

    const fetchProfessions = async () => {
      try {
        const response = await fetch(`${API_URL}/professions`, {
          headers: { Authorization: `Bearer ${currentToken}` }
        });

        const data = await response.json();
        console.log('Resposta do endpoint professions:', data);
        if (Array.isArray(data)) {
          setProfessions(data);
        } else if (data.success) {
          setProfessions(data.data);
        } else if (data.professions) {
          setProfessions(data.professions);
        }
      } catch (error) {
        console.error('Erro ao carregar profissões:', error);
      }
    };

    fetchProfessions();
  }, [isSystemAdmin]);

  // Buscar usuários com base nos filtros - otimizado para evitar sobrecarga
  const searchUsers = useCallback(async (page = 1) => {
    // Garantir que a página seja um número válido
    const pageNumber = parseInt(page, 10) || 1;
    // Verificar se já está carregando
    if (loading) return;

    // Tentar obter o token novamente caso não esteja disponível
    const currentToken = token || (typeof localStorage !== 'undefined' ? localStorage.getItem('token') : null);

    if (!currentToken) {
      console.error('Token não disponível ao buscar usuários');
      return;
    }

    // Se for a primeira página, limpar os resultados anteriores
    if (page === 1) {
      setUsers([]);
    }

    setLoading(true);

    try {
      // Construir parâmetros de busca
      const params = new URLSearchParams();

      // Adicionar parâmetros de busca apenas se houver algum filtro
      if (searchQuery) {
        params.append('search', searchQuery);
      }

      // Para buscar todos os usuários sem filtros específicos
      if (!searchQuery && !selectedCompany && !selectedRole) {
        params.append('all', 'true');
      }
      
      // Se não há filtros mas é system_admin, buscar todos
      if (isSystemAdmin && !searchQuery && !selectedCompany && !selectedRole) {
        params.append('all', 'true');
      }
      
      // Se é cliente, sempre buscar profissionais relacionados
      if (user?.isClient || user?.role === 'CLIENT') {
        params.append('all', 'true');
      }

      if (isSystemAdmin && selectedCompany) {
        params.append('companyId', selectedCompany);
      }

      if (selectedRole) {
        if (selectedRole === 'CLIENT') {
          params.append('role', 'CLIENT');
        } else {
          params.append('professionId', selectedRole);
        }
      }

      // Limitar a quantidade de usuários retornados para evitar sobrecarga
      // Definir apenas uma vez o limite
      const limit = 10;
      params.append('limit', limit.toString());

      // Garantir que a página seja um número válido
      const pageNum = parseInt(page, 10) || 1;
      params.append('page', pageNum.toString());

      // Excluir system_admin da lista de usuários
      params.append('excludeSystemAdmin', 'true');
      
      // Incluir clientes na busca
      params.append('includeClients', 'true');

      // Atualizar a página atual
      setCurrentPage(pageNumber);

      // A mesma rota funciona para todos os tipos de usuários
      const endpoint = '/users';
      const url = `${API_URL}${endpoint}?${params.toString()}`;

      console.log('Buscando usuários:', url);
      console.log('Filtros aplicados:', { searchQuery, selectedCompany, selectedRole, isSystemAdmin });
      console.log('Companies carregadas:', companies);

      try {
        const response = await fetch(url, {
          headers: { Authorization: `Bearer ${currentToken}` }
        });

        // Verificar se a resposta foi bem-sucedida
        if (!response.ok) {
          console.error(`Erro na resposta: ${response.status}`, await response.text());
          throw new Error(`Erro na resposta: ${response.status}`);
        }

        const data = await response.json();
        console.log('Resposta da API:', data);
        let newUsers = [];
        let totalPagesCount = 1;

        // Processar diferentes formatos de resposta
        if (data.users) {
          // Formato { users: [...], total: X, pages: Y }
          newUsers = data.users;
          totalPagesCount = data.pages || 1;
        } else if (data.success && data.data) {
          // Formato { success: true, data: [...] }
          newUsers = data.data;
        } else if (Array.isArray(data)) {
          // Formato array direto
          newUsers = data;
        } else {
          throw new Error('Formato de resposta inesperado');
        }

        // Filtrar o próprio usuário da lista
        newUsers = newUsers.filter(u => u.id !== user.id);

        console.log('Usuários processados:', newUsers);
        
        // Atualizar estado
        if (pageNumber > 1) {
          // Verificar se há usuários duplicados
          const existingIds = new Set(users.map(u => u.id));
          const uniqueNewUsers = newUsers.filter(u => !existingIds.has(u.id));
          setUsers(prevUsers => [...prevUsers, ...uniqueNewUsers]);
        } else {
          setUsers(newUsers);
        }

        // Atualizar total de páginas
        setTotalPages(totalPagesCount);
      } catch (apiError) {
        console.error('Erro na chamada da API:', apiError);
        if (pageNumber === 1) {
          setUsers([]);
        }
      }
    } catch (error) {
      console.error('Erro ao buscar usuários:', error);
      if (pageNumber === 1) {
        setUsers([]);
      }
    } finally {
      setLoading(false);
    }
  }, [API_URL, isSystemAdmin, searchQuery, selectedCompany, selectedRole, token, user?.id]);

  // Buscar quando os filtros mudarem - apenas para filtros, não para busca
  useEffect(() => {
    if (selectedCompany || selectedRole) {
      searchUsers(1);
    }
  }, [selectedCompany, selectedRole, searchUsers]);
  
  // Buscar usuários iniciais quando não há filtros
  useEffect(() => {
    if ((isSystemAdmin || user?.isClient || user?.role === 'CLIENT') && !selectedCompany && !selectedRole && !searchQuery && users.length === 0) {
      searchUsers(1);
    }
  }, [isSystemAdmin, selectedCompany, selectedRole, searchQuery, users.length, searchUsers, user?.isClient, user?.role]);

  // Obter iniciais para avatar
  const getInitials = (name) => {
    if (!name) return 'U';

    const names = name.split(' ');
    if (names.length === 1) return names[0].charAt(0);

    return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-cyan-200 dark:border-cyan-700">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-medium text-cyan-800 dark:text-cyan-200">
            Nova Conversa
          </h3>
          <button
            onClick={onClose}
            className="p-1.5 text-cyan-500 hover:text-cyan-700 dark:text-cyan-400 dark:hover:text-cyan-300 rounded-full hover:bg-cyan-50 dark:hover:bg-cyan-900/20 transition-colors"
            aria-label="Fechar"
          >
            <X size={20} />
          </button>
        </div>

        <div className="space-y-3">
          {/* Campo de busca */}
          <div className="relative">
            <form onSubmit={(e) => {
              e.preventDefault();
              searchUsers();
            }}>
              <div className="flex">
                <ModuleInput
                  type="text"
                  placeholder="Buscar por nome ou email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  moduleColor="people"
                  className="pl-10 py-2 flex-1"
                />
                <button
                  type="submit"
                  className="ml-2 px-3 py-2 bg-gradient-to-r from-cyan-500 to-cyan-700 hover:from-cyan-600 hover:to-cyan-800 dark:from-cyan-600 dark:to-cyan-800 dark:hover:from-cyan-700 dark:hover:to-cyan-900 text-white rounded-md transition-colors shadow-sm"
                >
                  Buscar
                </button>
                {searchQuery && (
                  <button
                    type="button"
                    onClick={() => {
                      setSearchQuery('');
                      setSelectedCompany('');
                      setSelectedRole('');
                    }}
                    className="ml-2 px-3 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors shadow-sm"
                  >
                    Limpar
                  </button>
                )}
              </div>
              <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-cyan-500 dark:text-cyan-400" />
            </form>
          </div>

          {/* Filtros adicionais para system_admin */}
          {isSystemAdmin && (
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Empresa
                </label>
                <ModuleSelect
                  value={selectedCompany}
                  onChange={(e) => setSelectedCompany(e.target.value)}
                  placeholder="Todas as empresas"
                  moduleColor="people"
                >
                  <option value="">Todas as empresas</option>
                  {companies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </ModuleSelect>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Função
                </label>
                <ModuleSelect
                  value={selectedRole}
                  onChange={(e) => setSelectedRole(e.target.value)}
                  placeholder="Todas as funções"
                  moduleColor="people"
                >
                  <option value="">Todas as funções</option>
                  <option value="CLIENT">Cliente</option>
                  {professions.map(profession => (
                    <option key={profession.id} value={profession.id}>
                      {profession.name}
                    </option>
                  ))}
                </ModuleSelect>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Lista de usuários */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"></div>
          </div>
        ) : users.length > 0 ? (
          <div className="flex flex-col">
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {users.map(user => (
                <button
                  key={user.id}
                  onClick={() => {
                    // Garantir que o objeto usuário tenha todos os campos necessários

                    
                    // Usar o nome do paciente titular se disponível para clientes
                    let displayName = user.fullName || user.login || 'Usuário';
                    if (user.role === 'CLIENT' || user.isClient) {
                      const clientPersonName = user.clientPersons?.[0]?.person?.fullName;
                      if (clientPersonName && clientPersonName.trim() !== '') {
                        displayName = clientPersonName;
                      }
                    }
                    
                    const userToSelect = {
                      ...user,
                      id: user.id || `temp-${Date.now()}`,
                      fullName: displayName,
                      email: user.email || '<EMAIL>'
                    };
                    onSelectUser(userToSelect);
                  }}
                  className="w-full flex items-center gap-3 p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors text-left"
                >
                {/* Avatar */}
                <div className="relative flex-shrink-0">
                  {user.profileImageUrl ? (
                    <img
                      src={user.profileImageUrl}
                      alt={user.fullName}
                      className="h-10 w-10 rounded-full object-cover"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.style.display = 'none';
                        e.target.parentNode.innerHTML = `<div class=\"h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium\">${getInitials(user.fullName)}</div>`;
                      }}
                    />
                  ) : (
                    <div className="h-10 w-10 rounded-full bg-cyan-100 dark:bg-cyan-900/30 flex items-center justify-center text-cyan-600 dark:text-cyan-300 font-medium">
                      {getInitials((() => {
                        // Usar o nome do paciente titular se disponível para clientes
                        if (user.role === 'CLIENT' || user.isClient) {
                          const clientPersonName = user.clientPersons?.[0]?.person?.fullName;
                          if (clientPersonName && clientPersonName.trim() !== '') {
                            return clientPersonName;
                          }
                        }
                        return (user.fullName && user.fullName.trim() !== '') ? user.fullName : user.login;
                      })())}
                    </div>
                  )}
                </div>

                {/* Informações */}
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                    {(() => {
                      // Usar o nome do paciente titular se disponível para clientes
                      if (user.role === 'CLIENT' || user.isClient) {
                        const clientPersonName = user.clientPersons?.[0]?.person?.fullName;
                        if (clientPersonName && clientPersonName.trim() !== '') {
                          return clientPersonName;
                        }
                      }
                      return (user.fullName && user.fullName.trim() !== '') ? user.fullName : (user.login || 'Usuário');
                    })()}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                    {user.email}
                  </p>
                </div>

                {/* Informações adicionais para system_admin */}
                {isSystemAdmin && (
                  <div className="flex flex-col items-end text-xs text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <Building2 size={12} />
                      <span>{user.company?.name || 'Sem empresa'}</span>
                    </div>
                    <div className="flex items-center gap-1 mt-1">
                      <Users size={12} />
                      <span>
                        {user.professionObj?.name || 
                         (user.role === 'SYSTEM_ADMIN' ? 'Admin do Sistema' :
                          user.role === 'COMPANY_ADMIN' ? 'Admin da Empresa' :
                          user.role === 'EMPLOYEE' ? 'Funcionário' :
                          user.role === 'CLIENT' ? 'Cliente' : user.role || 'Usuário')}
                      </span>
                    </div>
                  </div>
                )}
              </button>
              ))}
            </div>

            {/* Botão para carregar mais usuários */}
            {currentPage < totalPages && (
              <div className="p-3 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => searchUsers(parseInt(currentPage, 10) + 1)}
                  className="w-full py-2 text-cyan-500 hover:text-cyan-600 hover:bg-cyan-50 dark:hover:bg-cyan-900/20 rounded-md transition-colors"
                >
                  Carregar mais usuários
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full p-4 text-gray-500 dark:text-gray-400">
            <Users size={48} className="mb-2 text-cyan-400 dark:text-cyan-600" />
            <p className="text-center font-medium mb-1">
              {searchQuery
                ? `Nenhum usuário encontrado com "${searchQuery}"`
                : selectedCompany || selectedRole
                  ? 'Nenhum usuário encontrado com os filtros selecionados'
                  : 'Digite para buscar usuários'}
            </p>
            {searchQuery && (
              <div className="text-center">
                <p className="text-sm text-center text-gray-400 dark:text-gray-500 mb-3">
                  Tente outro termo de busca ou ajuste os filtros
                </p>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCompany('');
                    setSelectedRole('');
                  }}
                  className="px-3 py-2 bg-gradient-to-r from-cyan-500 to-cyan-700 hover:from-cyan-600 hover:to-cyan-800 dark:from-cyan-600 dark:to-cyan-800 dark:hover:from-cyan-700 dark:hover:to-cyan-900 text-white rounded-md transition-colors shadow-sm text-sm"
                >
                  Limpar Filtros
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default UserSearch;
