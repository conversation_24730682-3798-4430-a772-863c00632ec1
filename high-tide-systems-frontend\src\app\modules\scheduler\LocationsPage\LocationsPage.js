"use client";

import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleSelect, ModuleTable, ModuleCheckbox } from "@/components/ui";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Power,
  MapPin,
  Phone,
  Building,
  CheckCircle,
  XCircle,
  Share2
} from "lucide-react";
import { locationService } from "@/app/modules/scheduler/services/locationService";
import { branchService } from "@/app/modules/admin/services/branchService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import LocationFormModal from "@/components/people/LocationFormModal";
import ExportMenu from "@/components/ui/ExportMenu";
import { useToast } from "@/contexts/ToastContext";
import MultiSelect from "@/components/ui/multi-select";
import ShareButton from "@/components/common/ShareButton";
import { LocationsFilters } from "@/components/scheduler/LocationsFilters";

// Tutorial steps para a página de localizações
const locationsTutorialSteps = [
  {
    title: "Localizações",
    content: "Esta tela permite gerenciar as localizações disponíveis para agendamentos no sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Nova Localização",
    content: "Clique aqui para adicionar uma nova localização.",
    selector: "button:has(span:contains('Nova Localização'))",
    position: "left"
  },
  {
    title: "Filtrar Localizações",
    content: "Use esta barra de pesquisa para encontrar localizações específicas pelo nome ou endereço.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Filtrar por Status",
    content: "Filtre as localizações por status (ativas ou inativas).",
    selector: "select:first-of-type",
    position: "bottom"
  },
  {
    title: "Filtrar por Unidade",
    content: "Filtre as localizações por unidade.",
    selector: "select:nth-of-type(2)",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de localizações em diferentes formatos usando este botão.",
    selector: ".export-button",
    position: "left"
  },
  {
    title: "Gerenciar Localizações",
    content: "Edite, ative/desative ou exclua localizações existentes usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const LocationsPage = () => {
  const { user } = useAuth();
  const searchParams = useSearchParams();
  const [locations, setLocations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalLocations, setTotalLocations] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    search: "",
    status: "",
    branches: [],
    locations: [],
    companies: []
  });
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [locationFormOpen, setLocationFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedIds(locations.map(l => l.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectOne = (id, checked) => {
    setSelectedIds(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
  };
  const { toast_error } = useToast()
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Constants
  const ITEMS_PER_PAGE = 10;
  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  const loadLocations = async (
    page = currentPage,
    currentFilters = filters,
    sortF = sortField,
    sortD = sortDirection,
    perPage = itemsPerPage
  ) => {
    setIsLoading(true);
    try {
      const params = {
        page,
        limit: perPage,
        search: currentFilters.search || undefined,
        active: currentFilters.status === "" ? undefined : currentFilters.status === "active",
        branchId: currentFilters.branches.length > 0 ? currentFilters.branches : undefined,
        companyId: !isSystemAdmin ? user?.companyId : 
          currentFilters.companies.length > 0 ? currentFilters.companies : undefined,
        locationIds: currentFilters.locations.length > 0 ? currentFilters.locations : undefined,
        sortField: sortF,
        sortDirection: sortD
      };

      const response = await locationService.getLocations(params);

      setLocations(response.locations || []);
      setTotalLocations(response.total || 0);
      setTotalPages(response.pages || 1);
      setCurrentPage(response.currentPage || page);
      setSortField(response.sortField || sortF);
      setSortDirection(response.sortDirection || sortD);
    } catch (error) {
      console.error("Erro ao carregar localizações:", error);
      setLocations([]);
      setTotalLocations(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadLocations();
  }, []);

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
  };

  const handleSearch = (searchFilters) => {
    setCurrentPage(1);
    loadLocations(1, searchFilters, sortField, sortDirection);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    loadLocations(page, filters, sortField, sortDirection);
  };

  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);
    setCurrentPage(1);
    loadLocations(1, filters, field, direction);
  };

  const handleEditLocation = (location) => {
    setSelectedLocation(location);
    setLocationFormOpen(true);
  };

  const handleToggleStatus = (location) => {
    setSelectedLocation(location);
    setActionToConfirm({
      type: "toggle-status",
      message: `${location.active ? "Desativar" : "Ativar"} a localização ${location.name
        }?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleDeleteLocation = (location) => {
    setSelectedLocation(location);
    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente a localização ${location.name}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Exportar usando os mesmos filtros da tabela atual
      await locationService.exportLocations({
        search: filters.search || undefined,
        active: filters.status === "" ? undefined : filters.status === "active",
        branchId: filters.branches.length > 0 ? filters.branches : undefined,
        companyId: !isSystemAdmin ? user?.companyId : 
          filters.companies.length > 0 ? filters.companies : undefined,
        locationIds: filters.locations.length > 0 ? filters.locations : undefined
      }, format);
    } catch (error) {
      console.error("Erro ao exportar localizações:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const confirmAction = async () => {
    if (actionToConfirm.type === "toggle-status") {
      try {
        await locationService.toggleLocationStatus(selectedLocation.id);
        loadLocations();
      } catch (error) {
        console.error("Erro ao alterar status da localização:", error);
      }
    } else if (actionToConfirm.type === "delete") {
      try {
        await locationService.deleteLocation(selectedLocation.id);
        loadLocations();
      } catch (error) {
        console.error("Erro ao excluir localização:", error);
      }
    }
    setConfirmationDialogOpen(false);
  };

  // Efeito para abrir modal quando há locationId na URL (vindo do chat)
  useEffect(() => {
    const locationId = searchParams.get('locationId');
    const openModal = searchParams.get('openModal');
    const mode = searchParams.get('mode');
    
    if (locationId && openModal === 'true' && mode === 'edit') {
      // Para itens compartilhados do chat, abrir modal de edição diretamente
      const loadLocationForEdit = async () => {
        try {
          const location = await locationService.getLocation(locationId);
          if (location) {
            setSelectedLocation(location);
            setLocationFormOpen(true);
          }
        } catch (error) {
          console.error('Erro ao carregar localização para edição:', error);
        }
      };
      
      loadLocationForEdit();
    }
  }, [searchParams]);

  return (
    <div className="space-y-6">
      {/* Título e botões de ação */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <MapPin size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Localizações
        </h1>

        <div className="flex items-center gap-2">
          {selectedIds.length > 0 && (
            <button
              onClick={() => console.log('Excluir localizações selecionadas:', selectedIds)}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all"
              title="Excluir selecionados"
            >
              <Trash size={18} />
              <span className="font-medium">Excluir Selecionados ({selectedIds.length})</span>
            </button>
          )}
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || locations.length === 0}
            className="text-purple-600 dark:text-purple-400"
          />

          <button
            onClick={() => {
              setSelectedLocation(null);
              setLocationFormOpen(true);
            }}
            className="add-button flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white rounded-lg hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700 shadow-md transition-all"
            title="Nova Localização"
          >
            <Plus size={18} />
            <span className="font-medium">Nova Localização</span>
          </button>
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />}
        description="Gerencie as localizações disponíveis para agendamentos no sistema."
        tutorialSteps={locationsTutorialSteps}
        tutorialName="locations-overview"
        moduleColor="scheduler"
        filters={
          <LocationsFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onSearch={handleSearch}
            isLoading={isLoading}
          />
        }
      />

      {/* Tabela de Localizações */}
      <ModuleTable
        moduleColor="scheduler"
        title="Lista de Localizações"
        headerContent={
          <button
            onClick={() => loadLocations()}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-module-scheduler-primary dark:hover:text-module-scheduler-primary-dark transition-colors"
            title="Atualizar lista"
          >
            <RefreshCw size={18} />
          </button>
        }
        columns={[
          { header: '', field: 'select', width: '50px', sortable: false },
          { header: 'Nome', field: 'name', width: '25%' },
          { header: 'Endereço', field: 'address', width: '30%' },
          { header: 'Unidade', field: 'branch', width: '20%' },
          ...(isSystemAdmin ? [{ header: 'Empresa', field: 'company', width: '15%' }] : []),
          { header: 'Status', field: 'active', width: '10%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '20%', sortable: false }
        ]}
        data={locations}
        isLoading={isLoading}
        emptyMessage="Nenhuma localização encontrada"
        emptyIcon={<MapPin size={24} />}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalLocations}
        onPageChange={handlePageChange}
        showPagination={true}
        tableId="scheduler-locations-table"
        enableColumnToggle={true}
        defaultSortField="name"
        defaultSortDirection="asc"
        onSort={handleSort}
        sortField={sortField}
        sortDirection={sortDirection}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={(newItemsPerPage) => {
          setItemsPerPage(newItemsPerPage);
          loadLocations(1, filters, sortField, sortDirection, newItemsPerPage);
        }}
        selectedIds={selectedIds}
        onSelectAll={handleSelectAll}
        renderRow={(location, index, moduleColors, visibleColumns) => (
          <tr key={location.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('select') && (
              <td className="px-6 py-4 text-center">
                <ModuleCheckbox
                  moduleColor="scheduler"
                  checked={selectedIds.includes(location.id)}
                  onChange={(e) => handleSelectOne(location.id, e.target.checked)}
                  name={`select-location-${location.id}`}
                />
              </td>
            )}
            {visibleColumns.includes('name') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400">
                    <MapPin size={18} />
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                      {location.name}
                    </div>
                    {location.phone && (
                      <div className="text-xs text-neutral-500 dark:text-neutral-400 flex items-center">
                        <Phone size={12} className="mr-1" />
                        {location.phone}
                      </div>
                    )}
                  </div>
                </div>
              </td>
            )}
            {visibleColumns.includes('address') && (
              <td className="px-6 py-4">
                <div className="text-sm text-neutral-600 dark:text-neutral-300">
                  <div className="flex items-center">
                    <MapPin size={14} className="text-neutral-400 dark:text-neutral-500 mr-1" />
                    {location.address}
                  </div>
                </div>
              </td>
            )}
            {visibleColumns.includes('branch') && (
              <td className="px-6 py-4 whitespace-nowrap">
                {location.branch ? (
                  <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                    {location.branch.name}
                    {location.branch.code && (
                      <span className="text-xs ml-1 text-neutral-500 dark:text-neutral-400">
                        ({location.branch.code})
                      </span>
                    )}
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 text-sm">-</span>
                )}
              </td>
            )}
            {isSystemAdmin && visibleColumns.includes('company') && (
              <td className="px-6 py-4 whitespace-nowrap">
                {location.company ? (
                  <div className="flex items-center">
                    <Building className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1" />
                    <span className="text-sm text-neutral-600 dark:text-neutral-300">
                      {location.company.name}
                    </span>
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 text-sm">-</span>
                )}
              </td>
            )}
            {visibleColumns.includes('active') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <span
                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${location.active
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                    }`}
                >
                  {location.active ? (
                    <>
                      <CheckCircle size={12} />
                      <span>Ativo</span>
                    </>
                  ) : (
                    <>
                      <XCircle size={12} />
                      <span>Inativo</span>
                    </>
                  )}
                </span>
              </td>
            )}
            {visibleColumns.includes('actions') && (
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end gap-2">
                <ShareButton
                  itemType="location"
                  itemId={location.id}
                  itemTitle={location.name}
                  size="xs"
                  variant="ghost"
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors"
                />
                <button
                  onClick={() => handleEditLocation(location)}
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                  title="Editar"
                >
                  <Edit size={18} />
                </button>

                <button
                  onClick={() => handleToggleStatus(location)}
                  className={`p-1 transition-colors ${location.active
                      ? "text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400"
                      : "text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400"
                    }`}
                  title={location.active ? "Desativar" : "Ativar"}
                >
                  <Power size={18} />
                </button>

                <button
                  onClick={() => handleDeleteLocation(location)}
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  title="Excluir"
                >
                  <Trash size={18} />
                </button>
              </div>
            </td>
            )}
          </tr>
        )}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
        variant={actionToConfirm?.type === "delete" ? "danger" : "warning"}
      />

      {/* Location Form Modal */}
      {locationFormOpen && (
        <LocationFormModal
          isOpen={locationFormOpen}
          onClose={() => setLocationFormOpen(false)}
          location={selectedLocation}
          onSuccess={() => {
            setLocationFormOpen(false);
            loadLocations();
          }}
        />
      )}

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default LocationsPage;