const cron = require('node-cron');
const prisma = require('../utils/prisma');
const emailService = require('./emailService');

class ReminderService {
  constructor() {
    // Agenda a tarefa para rodar todos os dias às 9h da manhã
    this.job = cron.schedule('0 9 * * *', this.sendReminders.bind(this), {
      scheduled: false
    });
  }

  /**
   * Inicia o agendador de lembretes
   */
  start() {
    this.job.start();
    console.log('Agendador de lembretes iniciado');
  }

  /**
   * Para o agendador de lembretes
   */
  stop() {
    this.job.stop();
    console.log('Agendador de lembretes parado');
  }

  /**
   * Obtém agendamentos do dia seguinte e envia lembretes por email
   */
  async sendReminders() {
    try {
      console.log('Iniciando envio de lembretes para agendamentos de amanhã...');
      
      // Calcula as datas de início e fim de amanhã
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      
      const tomorrowEnd = new Date(tomorrow);
      tomorrowEnd.setHours(23, 59, 59, 999);
      
      // Busca todos os agendamentos de amanhã que estão confirmados ou pendentes
      const schedulings = await prisma.scheduling.findMany({
        where: {
          startDate: {
            gte: tomorrow,
            lte: tomorrowEnd
          },
          status: {
            in: ['PENDING', 'CONFIRMED']
          }
        },
        include: {
          Person: {
            include: {
              clientPersons: {
                include: {
                  client: true
                }
              }
            }
          },
          provider: true,
          serviceType: true,
          location: true
        }
      });
      
      console.log(`Encontrados ${schedulings.length} agendamentos para amanhã`);
      
      // Envia emails de lembrete para cada agendamento
      const results = await Promise.all(
        schedulings.map(async (scheduling) => {
          try {
            // Get the person and their client, if available
            const person = scheduling.Person && scheduling.Person.length > 0 ? scheduling.Person[0] : null;
            const clientPerson = person && person.clientPersons && person.clientPersons.length > 0 ? person.clientPersons[0] : null;
            const client = clientPerson ? clientPerson.client : null;
            
            if (!person || !client) {
              console.error(`Pessoa ou cliente não encontrado para agendamento ${scheduling.id}`);
              return {
                schedulingId: scheduling.id,
                success: false,
                error: 'Pessoa ou cliente não encontrado'
              };
            }
            
            const result = await emailService.sendReminderEmail(
              scheduling,
              client,
              scheduling.provider,
              scheduling.serviceType,
              scheduling.location,
              scheduling.branch,
              scheduling.companyId
            );
            
            return {
              schedulingId: scheduling.id,
              clientEmail: client.email,
              success: result.success
            };
          } catch (error) {
            console.error(`Erro ao enviar lembrete para agendamento ${scheduling.id}:`, error);
            return {
              schedulingId: scheduling.id,
              success: false,
              error: error.message
            };
          }
        })
      );
      
      const successful = results.filter(r => r.success).length;
      console.log(`Lembretes enviados: ${successful}/${schedulings.length}`);
      
      return results;
    } catch (error) {
      console.error('Erro ao processar lembretes:', error);
    }
  }

  /**
   * Envia um lembrete para um agendamento específico
   * @param {string} schedulingId - ID do agendamento
   */
  async sendReminderForScheduling(schedulingId) {
    try {
      const scheduling = await prisma.scheduling.findUnique({
        where: {
          id: schedulingId
        },
        include: {
          Person: {
            include: {
              clientPersons: {
                include: {
                  client: true
                }
              }
            }
          },
          provider: true,
          serviceType: true,
          location: true
        }
      });
      
      if (!scheduling) {
        throw new Error('Agendamento não encontrado');
      }
      
      // Get the person and their client, if available
      const person = scheduling.Person && scheduling.Person.length > 0 ? scheduling.Person[0] : null;
      const clientPerson = person && person.clientPersons && person.clientPersons.length > 0 ? person.clientPersons[0] : null;
      const client = clientPerson ? clientPerson.client : null;
      
      if (!person || !client) {
        throw new Error('Pessoa ou cliente não encontrado para este agendamento');
      }
      
      const result = await emailService.sendReminderEmail(
        scheduling,
        client,
        scheduling.provider,
        scheduling.serviceType,
        scheduling.location,
        scheduling.branch,
        scheduling.companyId
      );
      
      return result;
    } catch (error) {
      console.error(`Erro ao enviar lembrete para agendamento ${schedulingId}:`, error);
      throw error;
    }
  }
}

module.exports = new ReminderService();