import { useDataPrivacy } from '@/hooks/useDataPrivacy';

/**
 * Utilitários para aplicar máscaras de privacidade em exports e relatórios
 */

/**
 * Aplica máscaras de privacidade em dados antes de exportar
 * @param {string} entityType - T<PERSON><PERSON> da entidade (user, client, patient)
 * @param {array} data - Dados a serem exportados
 * @param {object} preferences - Preferências de privacidade (opcional, será buscado se não fornecido)
 * @returns {array} - Dados com máscaras aplicadas
 */
export const applyPrivacyMasksForExport = (entityType, data, preferences = null) => {
  // Se não há dados, retorna vazio
  if (!Array.isArray(data) || data.length === 0) {
    return data;
  }

  // Hook de privacidade não pode ser usado aqui pois não estamos em componente React
  // Então vamos importar os utilitários diretamente
  const { DataPrivacyUtils, FIELD_MASK_MAPPING } = require('./dataPrivacy');
  
  // Se preferências não foram fornecidas, precisa ser buscado externamente
  if (!preferences) {
    console.warn('Preferências de privacidade não fornecidas para export');
    return data;
  }

  const privacySection = `${entityType}Privacy`;
  const privacySettings = preferences[privacySection];

  // Se não há configurações de privacidade, retorna dados originais
  if (!privacySettings || Object.keys(privacySettings).length === 0) {
    return data;
  }

  // Mapeamento de campos para cada tipo de entidade
  const fieldMappings = {
    user: {
      hideUserCpf: ['cpf'],
      hideUserCnpj: ['cnpj'],
      hideUserEmail: ['email'],
      hideUserPhone: ['phone'],
      hideUserAddress: ['address', 'city', 'state', 'postalCode', 'neighborhood'],
      hideUserBirthDate: ['birthDate'],
      hideUserLastLoginIp: ['lastLoginIp']
    },
    client: {
      hideClientEmail: ['email'],
      hideClientFullName: ['fullName', 'name']
    },
    patient: {
      hidePatientCpf: ['cpf'],
      hidePatientEmail: ['email'],
      hidePatientPhone: ['phone'],
      hidePatientAddress: ['address', 'city', 'state', 'postalCode', 'neighborhood'],
      hidePatientBirthDate: ['birthDate'],
      hidePatientNotes: ['notes', 'observations'],
      hidePatientProfileImage: ['profileImageUrl', 'profileImageFullUrl']
    }
  };

  const entityMappings = fieldMappings[entityType];
  if (!entityMappings) {
    console.warn(`Mapeamento não encontrado para entidade: ${entityType}`);
    return data;
  }

  // Aplica máscaras nos dados
  return data.map(item => {
    const maskedItem = { ...item };

    // Aplica máscaras para cada configuração ativa
    Object.keys(privacySettings).forEach(privacyKey => {
      if (privacySettings[privacyKey] === true && entityMappings[privacyKey]) {
        const fieldsToMask = entityMappings[privacyKey];
        const maskType = FIELD_MASK_MAPPING[privacyKey];

        fieldsToMask.forEach(fieldName => {
          if (maskedItem[fieldName] !== undefined && maskedItem[fieldName] !== null) {
            if (maskType === 'hide') {
              maskedItem[fieldName] = '[DADOS SENSÍVEIS REMOVIDOS]';
            } else if (maskType) {
              maskedItem[fieldName] = DataPrivacyUtils.maskValue(maskedItem[fieldName], maskType);
            } else {
              maskedItem[fieldName] = '[OCULTO]';
            }
          }
        });
      }
    });

    return maskedItem;
  });
};

/**
 * Aplica máscaras em dados de usuários para export
 * @param {array} users - Lista de usuários
 * @param {object} preferences - Preferências de privacidade
 * @returns {array} - Usuários com máscaras aplicadas
 */
export const applyUserPrivacyMasksForExport = (users, preferences) => {
  return applyPrivacyMasksForExport('user', users, preferences);
};

/**
 * Aplica máscaras em dados de clientes para export
 * @param {array} clients - Lista de clientes
 * @param {object} preferences - Preferências de privacidade
 * @returns {array} - Clientes com máscaras aplicadas
 */
export const applyClientPrivacyMasksForExport = (clients, preferences) => {
  return applyPrivacyMasksForExport('client', clients, preferences);
};

/**
 * Aplica máscaras em dados de pacientes para export
 * @param {array} patients - Lista de pacientes
 * @param {object} preferences - Preferências de privacidade
 * @returns {array} - Pacientes com máscaras aplicadas
 */
export const applyPatientPrivacyMasksForExport = (patients, preferences) => {
  return applyPrivacyMasksForExport('patient', patients, preferences);
};

/**
 * Hook para uso em componentes React - aplica máscaras usando o contexto de preferências
 * @param {string} entityType - Tipo da entidade
 * @returns {function} - Função para aplicar máscaras
 */
export const useExportPrivacyMasks = (entityType) => {
  const { applyListPrivacyMasks, preferences } = useDataPrivacy();

  return {
    /**
     * Aplica máscaras nos dados para export
     * @param {array} data - Dados a serem mascarados
     * @returns {array} - Dados mascarados
     */
    applyMasksForExport: (data) => {
      return applyListPrivacyMasks(entityType, data, { hideCompletely: false });
    },

    /**
     * Verifica se há configurações de privacidade ativas
     * @returns {boolean}
     */
    hasActiveMasks: () => {
      const privacySection = `${entityType}Privacy`;
      const privacySettings = preferences[privacySection];
      return privacySettings && Object.values(privacySettings).some(value => value === true);
    },

    /**
     * Obtém lista de campos que serão mascarados
     * @returns {array}
     */
    getMaskedFields: () => {
      const privacySection = `${entityType}Privacy`;
      const privacySettings = preferences[privacySection];
      if (!privacySettings) return [];
      
      return Object.keys(privacySettings).filter(key => privacySettings[key] === true);
    }
  };
};

/**
 * Adiciona aviso sobre dados mascarados ao cabeçalho de exports
 * @param {string} entityType - Tipo da entidade
 * @param {object} preferences - Preferências de privacidade
 * @returns {string} - Texto de aviso
 */
export const getPrivacyWarningForExport = (entityType, preferences) => {
  const privacySection = `${entityType}Privacy`;
  const privacySettings = preferences[privacySection];
  
  if (!privacySettings || !Object.values(privacySettings).some(value => value === true)) {
    return '';
  }

  const maskedFields = Object.keys(privacySettings).filter(key => privacySettings[key] === true);
  const fieldCount = maskedFields.length;
  
  const entityNames = {
    user: 'usuários',
    client: 'clientes', 
    patient: 'pacientes'
  };

  const entityName = entityNames[entityType] || 'dados';

  return `⚠️ AVISO: Este relatório contém dados mascarados de ${entityName}. ${fieldCount} tipo(s) de campo(s) sensível(eis) foram ocultados conforme configurações de privacidade da empresa. Os dados originais permanecem seguros no sistema.`;
};

export default {
  applyPrivacyMasksForExport,
  applyUserPrivacyMasksForExport,
  applyClientPrivacyMasksForExport, 
  applyPatientPrivacyMasksForExport,
  useExportPrivacyMasks,
  getPrivacyWarningForExport
};