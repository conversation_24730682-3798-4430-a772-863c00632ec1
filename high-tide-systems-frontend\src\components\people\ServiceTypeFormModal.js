"use client";

import React, { useState, useEffect } from "react";
import { Tag, DollarSign, Building, Loader2, AlertCircle } from "lucide-react";
import { serviceTypeService } from "@/app/modules/scheduler/services/serviceTypeService";
import { useAuth } from "@/contexts/AuthContext";
import {
  ModuleInput,
  ModuleSelect,
  ModuleFormGroup
} from "@/components/ui";
import ShareButton from "@/components/common/ShareButton";

const ServiceTypeFormModal = ({ isOpen, onClose, serviceType = null, onSuccess, companies = [] }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    name: "",
    value: "",
    companyId: ""
  });
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  // Load data when the modal opens
  useEffect(() => {
    if (isOpen) {
      // Set company ID for non-admin users
      if (!isSystemAdmin && user?.companyId) {
        setFormData(prev => ({ ...prev, companyId: user.companyId }));
      }

      if (serviceType) {
        setFormData({
          name: serviceType.name || "",
          value: serviceType.value ? serviceType.value.toString() : "",
          // If not admin, always use the user's company
          companyId: !isSystemAdmin ? user?.companyId : (serviceType.companyId || "")
        });
      } else {
        // Reset form for new service type
        resetForm();
      }
    }
  }, [isOpen, serviceType, user]);

  const resetForm = () => {
    setFormData({
      name: "",
      value: "",
      // If not admin, always use the user's company
      companyId: !isSystemAdmin ? user?.companyId : ""
    });
    setError(null);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // For the value field, ensure it's a valid number
    if (name === "value") {
      // Allow only numbers, comma and period
      const sanitizedValue = value.replace(/[^\d.,]/g, "");
      // Replace comma with period
      const normalizedValue = sanitizedValue.replace(",", ".");

      setFormData(prev => ({
        ...prev,
        [name]: normalizedValue
      }));
      return;
    }

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    // Basic validation
    if (!formData.name) return "Nome do serviço é obrigatório";
    if (!formData.value) return "Valor do serviço é obrigatório";

    // Validate if value is a number
    const numericValue = parseFloat(formData.value);
    if (isNaN(numericValue) || numericValue < 0) return "Valor deve ser um número positivo";

    if (!formData.companyId) return "Empresa é obrigatória";

    return null; // No errors
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Prepare data
      const payload = {
        name: formData.name,
        value: parseFloat(formData.value),
        // Ensure there's always a company
        companyId: formData.companyId || user?.companyId
      };

      if (serviceType) {
        // Update existing service type
        await serviceTypeService.updateServiceType(serviceType.id, payload);
      } else {
        // Create new service type
        await serviceTypeService.createServiceType(payload);
      }

      onSuccess();
      onClose();
    } catch (err) {
      console.error("Erro ao salvar tipo de serviço:", err);
      setError(err.response?.data?.message || err.message || "Ocorreu um erro ao salvar o tipo de serviço.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Módulo a ser usado nos componentes
  const moduleColor = "scheduler";

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo escuro */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="fixed left-[50%] top-[50%] z-[12050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-purple-300 dark:border-purple-600 bg-background shadow-lg duration-200 rounded-xl max-w-2xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="pb-4 border-b-2 border-purple-400 dark:border-purple-500 flex-shrink-0 px-6 pt-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-600 to-violet-400 rounded-lg text-white">
              <Tag className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-purple-800 dark:text-white border-l-4 border-purple-400 dark:border-purple-500 pl-3">
                {serviceType ? 'Editar Tipo de Serviço' : 'Novo Tipo de Serviço'}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                {serviceType ? 'Modifique as informações do tipo de serviço' : 'Preencha as informações para criar um novo tipo de serviço'}
              </p>
            </div>
            {serviceType && (
              <div className="ml-auto">
                <ShareButton
                  itemType="serviceType"
                  itemId={serviceType.id}
                  itemTitle={serviceType.name}
                  size="sm"
                  variant="ghost"
                />
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(90vh-120px)]">
          {/* Form Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <form id="service-type-form" onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2">
                  <AlertCircle size={16} />
                  <span>{error}</span>
                </div>
              )}

              <ModuleFormGroup
                moduleColor="scheduler"
                label="Nome do Serviço *"
                htmlFor="name"
                icon={<Tag size={16} />}
                required
              >
                <ModuleInput
                  moduleColor="scheduler"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Ex: Consulta Padrão, Avaliação, etc."
                  required
                  disabled={isSubmitting}
                />
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="scheduler"
                label="Valor (R$) *"
                htmlFor="value"
                icon={<DollarSign size={16} />}
                required
                helpText="Use ponto ou vírgula para separar os centavos (Ex: 150,00 ou 150.00)"
              >
                <ModuleInput
                  moduleColor="scheduler"
                  id="value"
                  name="value"
                  value={formData.value}
                  onChange={handleChange}
                  placeholder="0,00"
                  required
                  disabled={isSubmitting}
                />
              </ModuleFormGroup>

              {/* Company Dropdown for System Admin */}
              {isSystemAdmin ? (
                <ModuleFormGroup
                  moduleColor="scheduler"
                  label="Empresa *"
                  htmlFor="companyId"
                  icon={<Building size={16} />}
                  required
                >
                  <ModuleSelect
                    moduleColor="scheduler"
                    id="companyId"
                    name="companyId"
                    value={formData.companyId}
                    onChange={handleChange}
                    required
                    placeholder="Selecione uma empresa"
                    disabled={isSubmitting}
                  >
                    <option value="">Selecione uma empresa</option>
                    {companies.map(company => (
                      <option key={company.id} value={company.id}>
                        {company.name}
                      </option>
                    ))}
                  </ModuleSelect>
                </ModuleFormGroup>
              ) : (
                <ModuleFormGroup
                  moduleColor="scheduler"
                  label="Empresa"
                  icon={<Building size={16} />}
                  helpText="O tipo de serviço será associado à sua empresa"
                >
                  <div className="px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg bg-neutral-50 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 flex items-center">
                    <Building className="h-5 w-5 text-gray-400 dark:text-gray-500 mr-2" />
                    {user?.companyName || "Sua empresa"}
                  </div>
                  <input type="hidden" name="companyId" value={user?.companyId || ""} />
                </ModuleFormGroup>
              )}
            </form>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 border-t-2 border-gray-300 dark:border-gray-600 pt-4 flex-shrink-0 px-6 pb-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              Cancelar
            </button>
            <button
              type="submit"
              form="service-type-form"
              className="px-4 py-2 bg-purple-600 dark:bg-purple-700 text-white rounded-lg hover:bg-purple-700 dark:hover:bg-purple-800 transition-colors flex items-center gap-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  <Tag size={16} />
                  <span>{serviceType ? "Atualizar" : "Salvar"}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceTypeFormModal;