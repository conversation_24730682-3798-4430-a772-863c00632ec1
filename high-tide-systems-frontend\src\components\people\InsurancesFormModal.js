"use client";

import React, { useState, useEffect } from "react";
import { AlertCircle, CreditCard, Building, Loader2 } from "lucide-react";
import { insurancesService } from "@/app/modules/people/services/insurancesService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import { ModuleSelect, ModuleInput, ModuleFormGroup } from "@/components/ui";
import ShareButton from "@/components/common/ShareButton";

const InsuranceFormModal = ({ isOpen, onClose, insurance = null, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: "",
    companyId: "",
  });
  const [companies, setCompanies] = useState([]);
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);

  const { user, isSystemAdmin } = useAuth();

  // Se não for system admin, deve usar a empresa do usuário
  useEffect(() => {
    if (!isSystemAdmin && user?.companyId) {
      setFormData(prev => ({
        ...prev,
        companyId: user.companyId
      }));
    }
  }, [isSystemAdmin, user?.companyId]);

  // Carregar empresas (se você tiver esse serviço)
  useEffect(() => {
    const loadCompanies = async () => {
      // Se não for system admin, não precisa carregar empresas
      if (!isSystemAdmin) return;

      setIsLoadingCompanies(true);
      try {
        const response = await companyService.getCompaniesForSelect();
        setCompanies(response || []);
      } catch (error) {
        console.error("Erro ao carregar empresas:", error);
        setError("Não foi possível carregar a lista de empresas.");
      } finally {
        setIsLoadingCompanies(false);
      }
    };

    if (isOpen) {
      loadCompanies();
    }
  }, [isOpen, isSystemAdmin]);

  // Preencher o formulário se estivermos editando
  useEffect(() => {
    if (insurance) {
      // Ao editar, carregar os dados do convênio
      setFormData({
        name: insurance.name || "",
        // Se não for system admin, forçar a empresa do usuário
        companyId: isSystemAdmin ? (insurance.companyId || "") : user?.companyId || "",
      });
    } else {
      // Reset do formulário para novo convênio
      setFormData({
        name: "",
        // Se não for system admin, definir a empresa do usuário
        companyId: isSystemAdmin ? "" : user?.companyId || "",
      });
    }
  }, [insurance, isSystemAdmin, user?.companyId]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Garantir que companyId esteja definido para não-admin
      const dataToSubmit = {
        ...formData,
        // Se não for system admin, garantir que use a empresa do usuário
        companyId: isSystemAdmin ? formData.companyId : user?.companyId
      };

      if (insurance) {
        // Editar convênio existente
        await insurancesService.updateInsurance(insurance.id, dataToSubmit);
      } else {
        // Criar novo convênio
        await insurancesService.createInsurance(dataToSubmit);
      }

      onSuccess();
      onClose();
    } catch (err) {
      console.error("Erro ao salvar convênio:", err);
      setError(err.response?.data?.message || "Ocorreu um erro ao salvar o convênio.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo escuro */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="fixed left-[50%] top-[50%] z-[12050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-orange-300 dark:border-orange-600 bg-background shadow-lg duration-200 rounded-xl max-w-2xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="pb-4 border-b-2 border-orange-400 dark:border-orange-500 flex-shrink-0 px-6 pt-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg text-white">
              <CreditCard className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-orange-800 dark:text-white border-l-4 border-orange-400 dark:border-orange-500 pl-3">
                {insurance ? 'Editar Convênio' : 'Novo Convênio'}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                {insurance ? 'Modifique as informações do convênio' : 'Preencha as informações para criar um novo convênio'}
              </p>
            </div>
            {insurance && (
              <div className="ml-auto">
                <ShareButton
                  itemType="insurance"
                  itemId={insurance.id}
                  itemTitle={insurance.name}
                  size="sm"
                  variant="ghost"
                />
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(90vh-120px)]">
          {/* Form Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <form id="insurance-form" onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2">
                  <AlertCircle size={16} />
                  <span>{error}</span>
                </div>
              )}

              <ModuleFormGroup
                moduleColor="people"
                label="Nome do Convênio *"
                htmlFor="name"
                icon={<CreditCard size={16} />}
                required
              >
                <ModuleInput
                  moduleColor="people"
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  placeholder="Digite o nome do convênio"
                  disabled={isSubmitting}
                />
              </ModuleFormGroup>

              {/* Campo de empresa só visível para admin */}
              {isSystemAdmin && (
                <ModuleFormGroup
                  moduleColor="people"
                  label="Empresa (opcional)"
                  htmlFor="companyId"
                  icon={<Building size={16} />}
                  helpText="Se não selecionado, o convênio estará disponível para toda a plataforma."
                >
                  <ModuleSelect
                    moduleColor="people"
                    id="companyId"
                    name="companyId"
                    value={formData.companyId}
                    onChange={handleChange}
                    placeholder="Selecione uma empresa (Opcional)"
                    disabled={isSubmitting}
                  >
                    {isLoadingCompanies ? (
                      <option disabled>Carregando empresas...</option>
                    ) : (
                      companies.map(company => (
                        <option key={company.id} value={company.id}>
                          {company.name}
                        </option>
                      ))
                    )}
                  </ModuleSelect>
                </ModuleFormGroup>
              )}

              {/* Para não-admin, mostrar a empresa que será usada */}
              {!isSystemAdmin && user?.companyId && (
                <ModuleFormGroup
                  moduleColor="people"
                  label="Empresa"
                  icon={<Building size={16} />}
                  helpText="O convênio será associado automaticamente à sua empresa."
                >
                  <div className="px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg bg-neutral-50 dark:bg-gray-700 text-neutral-700 dark:text-gray-300">
                    {user.companyName || "Sua empresa"}
                  </div>
                </ModuleFormGroup>
              )}
            </form>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 border-t-2 border-gray-300 dark:border-gray-600 pt-4 flex-shrink-0 px-6 pb-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              Cancelar
            </button>
            <button
              type="submit"
              form="insurance-form"
              className="px-4 py-2 bg-orange-500 dark:bg-orange-600 text-white rounded-lg hover:bg-orange-600 dark:hover:bg-orange-700 transition-colors flex items-center gap-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  <CreditCard size={16} />
                  <span>{insurance ? "Salvar Alterações" : "Salvar"}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsuranceFormModal;