"use client";

import React, { useState, useEffect, useRef } from "react";
import { Calendar, Filter, Plus } from "lucide-react";
import { addDays, addMonths } from "date-fns";
import { CalendarFilters } from "@/components/calendar/CalendarFilters";
import { ClientCalendarFilters } from "@/components/calendar/ClientCalendarFilters";
import { AppointmentModal } from "@/components/calendar/AppointmentModal";
import { usePermissions } from "@/hooks/usePermissions";
import { useAuth } from "@/contexts/AuthContext";
import { appointmentService } from "@/app/modules/scheduler/services/appointmentService";
import ModuleHeader from "@/components/ui/ModuleHeader";
import html2canvas from "html2canvas";
import { saveAs } from "file-saver";
import { jsPDF } from "jspdf";
import { useSearchParams, useRouter } from 'next/navigation';
import { useToast } from "@/contexts/ToastContext";
import { LoadingSpinner } from '../../../../components/LoadingSpinner';
import { processAppointments } from "@/utils/apiResponseAdapter";

// Componentes refatorados
import CustomMonthGrid from "./components/CustomMonthGrid";
import CustomWeekGrid from "./components/CustomWeekGrid";
import CustomDayGrid from "./components/CustomDayGrid";
import AppointmentEventContent from "./components/AppointmentEventContent";
import SequentialGroupEventContent from "./components/SequentialGroupEventContent";
import CalendarExportButton from "./components/CalendarExportButton";
import MultipleEventsModal from "./components/MultipleEventsModal";
import ExportPreviewModal from "./components/ExportPreviewModal";
import SequentialAppointmentsModal from "./components/SequentialAppointmentsModal";

// Hooks personalizados
import useAppointmentCalendar from "./hooks/useAppointmentCalendar";
import useAppointmentAvailability from "./hooks/useAppointmentAvailability";
import useCalendarBusinessHours from "./hooks/useCalendarBusinessHours";
import { useSchedulingPreferences } from "@/hooks/useSchedulingPreferences";

// Componentes do tutorial
import TutorialManager from "@/components/tutorial/TutorialManager";

const calendarTutorialSteps = [
  {
    title: "Bem-vindo ao Calendário",
    content: "Este é o calendário de agendamentos. Aqui você pode visualizar, criar e gerenciar todos os seus compromissos.",
    selector: ".fc-view-harness", // Seletor para o componente do calendário
    position: "top"
  },
  {
    title: "Filtros de Calendário",
    content: "Use estes filtros para encontrar agendamentos específicos. Você pode filtrar por profissionais, pacientes, tipos de serviço e locais.",
    selector: ".filter-section", // Adicione esta classe ao componente CalendarFilters
    position: "bottom"
  },
  {
    title: "Criar Agendamento",
    content: "Para criar um novo agendamento, basta clicar em qualquer espaço vazio no calendário e preencher as informações necessárias.",
    selector: ".fc-daygrid-day:not(.fc-day-other):nth-child(3)", // Seleciona um dia do calendário para exemplo
    position: "bottom"
  },
  {
    title: "Visualizações do Calendário",
    content: "Alterne entre diferentes visualizações: mês, semana ou dia para ver seus agendamentos com mais detalhes.",
    selector: ".fc-toolbar-chunk:last-child", // Seletor para os botões de visualização
    position: "bottom"
  },
  {
    title: "Exportar Agendamentos",
    content: "Você pode exportar seus agendamentos em diferentes formatos usando este botão.",
    selector: ".export-button", // Adicione esta classe ao componente de exportação
    position: "right"
  }
];

const AppointmentCalendar = () => {
  // Obter parâmetros da URL
  const searchParams = useSearchParams();
  const router = useRouter();
  
  // Estado para os filtros do calendário
  const [filters, setFilters] = useState({
    providers: [],
    persons: [],
    serviceTypes: [],
    locations: [],
  });

  // Log de debug para os filtros
  useEffect(() => {
    console.log("[AppointmentCalendar] Filtros atualizados:", filters);
  }, [filters]);

  // Estado para exportação
  const [isExporting, setIsExporting] = useState(false);
  const [showExportPreview, setShowExportPreview] = useState(false);
  const [exportPreviewData, setExportPreviewData] = useState(null);
  const [selectedExportFormat, setSelectedExportFormat] = useState(null);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);

  // Estado para o modal de múltiplos eventos
  const [isMultipleEventsModalOpen, setIsMultipleEventsModalOpen] = useState(false);
  const [multipleEvents, setMultipleEvents] = useState([]);
  const [multipleEventsDate, setMultipleEventsDate] = useState(null);

  // Estado para o modal de grupos sequenciais
  const [isSequentialModalOpen, setIsSequentialModalOpen] = useState(false);
  const [selectedSequentialGroup, setSelectedSequentialGroup] = useState(null);

  // Ref para evitar loops infinitos nos parâmetros da URL
  const processedParamsRef = useRef({
    view: null,
    date: null,
    highlightAppointment: null
  });

  // Detecta modo escuro
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Hooks de permissões
  const { can, isClient } = usePermissions();
  const { user } = useAuth();

  // Hook de preferências de agendamento
  const { preferences, isLoading: preferencesLoading } = useSchedulingPreferences();

  // Hook de toast
  const { toast_error, toast_success } = useToast();

  // Log para debug
  console.log("[DEBUG] User object:", user);
  console.log("[DEBUG] isClient result:", isClient());
  console.log("[DEBUG] Scheduling preferences:", preferences);

  const permissions = {
    canViewCalendar: can('scheduling.calendar.view') || can('scheduler.calendar.view'),
    canCreateAppointment: can('scheduling.calendar.create'),
    canEditAppointment: can('scheduling.calendar.edit'),
    canDeleteAppointment: can('scheduling.calendar.delete')
  };

  // Detectar dark mode
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const darkModeActive =
        document.documentElement.classList.contains('dark') ||
        window.matchMedia('(prefers-color-scheme: dark)').matches;

      setIsDarkMode(darkModeActive);

      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.attributeName === 'class') {
            setIsDarkMode(document.documentElement.classList.contains('dark'));
          }
        });
      });

      observer.observe(document.documentElement, { attributes: true });
      return () => observer.disconnect();
    }
  }, []);

  // Hook principal do calendário
  const {
    calendarRef,
    isModalOpen,
    setIsModalOpen,
    selectedDate,
    setSelectedDate,
    viewDate,
    setViewDate,
    selectedAppointment,
    setSelectedAppointment,
    appointments,
    isLoading,
    currentView,
    handleSlotClassNames,
    handleDateSelect: baseHandleDateSelect,
    handleEventClick: baseHandleEventClick,
    handleDatesSet,
    handleSearch,
    loadAppointments
  } = useAppointmentCalendar(filters, isDarkMode, permissions);

  // Log de debug para selectedDate (movido para depois da declaração do hook)
  useEffect(() => {
    console.log("[AppointmentCalendar] selectedDate atualizado:", selectedDate);
  }, [selectedDate]);

  // Log de debug para viewDate
  useEffect(() => {
    console.log("[AppointmentCalendar] viewDate atualizada:", viewDate);
  }, [viewDate]);

  // Processar parâmetros da URL para navegação específica
  useEffect(() => {
    const viewParam = searchParams.get('view');
    const dateParam = searchParams.get('date');
    const highlightAppointment = searchParams.get('highlightAppointment');
    
    console.log('[AppointmentCalendar] Parâmetros da URL:', { viewParam, dateParam, highlightAppointment });
    
    // Verificar se os parâmetros realmente mudaram
    const currentParams = { viewParam, dateParam, highlightAppointment };
    const previousParams = processedParamsRef.current;
    
    if (JSON.stringify(currentParams) === JSON.stringify(previousParams)) {
      console.log('[AppointmentCalendar] Parâmetros não mudaram, ignorando...');
      return;
    }
    
    console.log('[AppointmentCalendar] Parâmetros mudaram, processando...');
    
    // Configurar visualização se especificada e mudou
    if (viewParam && viewParam !== previousParams.view) {
      const viewMap = {
        'month': 'dayGridMonth',
        'week': 'timeGridWeek',
        'day': 'timeGridDay'
      };
      const targetView = viewMap[viewParam] || 'timeGridWeek';
      console.log('[AppointmentCalendar] Configurando visualização para:', targetView);
      handleDatesSet({ view: targetView });
      processedParamsRef.current.view = viewParam;
    }
    
    // Configurar data se especificada e mudou
    if (dateParam && dateParam !== previousParams.date) {
      const targetDate = new Date(dateParam);
      if (!isNaN(targetDate.getTime())) {
        console.log('[AppointmentCalendar] Configurando data para:', targetDate);
        handleDatesSet({ start: targetDate });
        processedParamsRef.current.date = dateParam;
      }
    }
    
    // Abrir modal do agendamento se especificado e mudou
    if (highlightAppointment && highlightAppointment !== previousParams.highlightAppointment) {
      console.log('[AppointmentCalendar] highlightAppointment detectado:', highlightAppointment);
      processedParamsRef.current.highlightAppointment = highlightAppointment;
      
      setTimeout(() => {
        // Tentar encontrar o agendamento já carregado
        const appointmentFromList = appointments.find(a => a.id === highlightAppointment);
        console.log('[AppointmentCalendar] appointmentFromList:', appointmentFromList);
        if (appointmentFromList) {
          setSelectedAppointment(appointmentFromList);
          setIsModalOpen(true);
        } else {
          // NOVO: Verificar se veio do chat (sessionStorage)
          const shared = sessionStorage.getItem('shared_appointment');
          if (shared) {
            try {
              const parsed = JSON.parse(shared);
              if (parsed && parsed.id === highlightAppointment) {
                console.log('[AppointmentCalendar] Usando agendamento do sessionStorage:', parsed);
                setSelectedAppointment(parsed);
                setIsModalOpen(true);
                sessionStorage.removeItem('shared_appointment');
                return;
              }
            } catch (e) {
              console.error('[AppointmentCalendar] Erro ao ler shared_appointment do sessionStorage:', e);
              sessionStorage.removeItem('shared_appointment');
            }
          }
          appointmentService.getAppointmentById(highlightAppointment)
            .then(appointment => {
              console.log('[AppointmentCalendar] Dados brutos do getAppointmentById:', appointment);
              const [processed] = processAppointments([appointment]);
              console.log('[AppointmentCalendar] Dados após processAppointments:', processed);
              setSelectedAppointment(processed);
              setIsModalOpen(true);
            })
            .catch(error => {
              console.error('[AppointmentCalendar] Erro ao buscar agendamento:', error);
            });
        }
      }, 1500);
    }
  }, [searchParams, handleDatesSet, appointments]);

  // Inicializar viewDate se estiver null (movido para depois da declaração do hook)
  useEffect(() => {
    if (!viewDate || (viewDate instanceof Date && isNaN(viewDate.getTime()))) {
      console.log("[AppointmentCalendar] Inicializando viewDate com data atual");
      handleDatesSet({ start: new Date() });
    }
  }, [viewDate, handleDatesSet]);

  // Monitorar quando o modal é fechado para garantir que viewDate seja preservada (movido para depois da declaração do hook)
  useEffect(() => {
    if (!isModalOpen) {
      console.log("[AppointmentCalendar] Modal fechado, viewDate atual:", viewDate);
      // Se a viewDate foi resetada ou é inválida, restaurar com a data atual
      if (!viewDate || (viewDate instanceof Date && isNaN(viewDate.getTime()))) {
        console.log("[AppointmentCalendar] viewDate foi resetada ou é inválida, restaurando...");
        handleDatesSet({ start: new Date() });
      }
    }
  }, [isModalOpen, viewDate, handleDatesSet]);

  // Monitorar quando selectedAppointment é resetado (movido para depois da declaração do hook)
  useEffect(() => {
    if (!selectedAppointment && isModalOpen) {
      console.log("[AppointmentCalendar] selectedAppointment foi resetado enquanto modal estava aberto");
    }
  }, [selectedAppointment, isModalOpen]);



  // Hook de disponibilidade de horários
  const {
    providersAvailability,
    errorMessage,
    setErrorMessage,
    checkSingleProviderAvailability,
    checkProvidersAvailability,
    validateAppointment
  } = useAppointmentAvailability(filters, currentView);

  // Hook de horários comerciais (já integrado com preferências)
  const { businessHours, calendarTimeRange, allowedHours } = useCalendarBusinessHours(filters, providersAvailability);

  // Funções que combinam o core com a verificação de disponibilidade
  const handleDateSelect = (selectInfo) => {
    // Para clientes, não permitir criação de novos agendamentos
    if (isClient()) {
      console.log('[AppointmentCalendar] Cliente detectado, bloqueando criação de agendamento');
      return;
    }
    
    // Verificar se o horário selecionado está permitido
    const selectedHour = selectInfo.start.getHours();
    const selectedDay = selectInfo.start.getDay();
    
    // Verificar se o dia da semana está permitido
    const isDayAllowed = preferences?.selectedWeekDays?.includes(selectedDay) ?? true;
    
    // Verificar se o horário está permitido
    const isHourAllowed = allowedHours?.[selectedHour] ?? true;
    
    if (!isDayAllowed) {
      const dayNames = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];
      toast_error(`Agendamento não permitido em ${dayNames[selectedDay]}`);
      return;
    }
    
    if (!isHourAllowed) {
      toast_error(`Horário ${String(selectedHour).padStart(2, '0')}:00 indisponível para agendamento`);
      return;
    }
    
    // Se passou pelas validações, prosseguir com o agendamento
    baseHandleDateSelect(selectInfo, checkProvidersAvailability, setErrorMessage);
  };

  const handleEventClick = (clickInfo) => {
    console.log('[AppointmentCalendar] handleEventClick chamado com:', clickInfo);
    
    // Para clientes, não permitir edição de agendamentos
    if (isClient()) {
      console.log('[AppointmentCalendar] Cliente detectado, bloqueando edição de agendamento');
      return;
    }
    
    // Normalizar o evento para o formato esperado pelo baseHandleEventClick
    let normalizedEvent;
    
    if (clickInfo.event) {
      // Evento vindo dos componentes customizados
      const event = clickInfo.event;
      console.log('[AppointmentCalendar] Evento dos componentes customizados:', event);
      

      
      // Verificar se é um grupo sequencial
      if (event.extendedProps?.isSequentialGroup && event.extendedProps?.sequentialGroup) {
        console.log('[AppointmentCalendar] Grupo sequencial detectado, abrindo modal');
        setSelectedSequentialGroup(event.extendedProps.sequentialGroup);
        setIsSequentialModalOpen(true);
        return;
      }
      
      // Garantir que temos objetos Date válidos
      const startDate = event.startDate || event.start;
      const endDate = event.endDate || event.end;
      
      console.log('[AppointmentCalendar] startDate:', startDate, 'tipo:', typeof startDate);
      console.log('[AppointmentCalendar] endDate:', endDate, 'tipo:', typeof endDate);
      
      normalizedEvent = {
        id: event.id,
        title: event.title,
        start: startDate instanceof Date ? startDate : new Date(startDate),
        end: endDate instanceof Date ? endDate : new Date(endDate),
        extendedProps: {
          personfullName: event.personfullName,
          providerfullName: event.providerfullName,
          status: event.status,
          description: event.description,
          providerId: event.providerId,
          personId: event.personId,
          locationId: event.locationId,
          serviceTypeId: event.serviceTypeId,
          insuranceId: event.insuranceId,
          ...event.extendedProps
        }
      };
      
      console.log('[AppointmentCalendar] Evento normalizado:', normalizedEvent);
    } else {
      // Evento vindo do react-big-calendar (formato original)
      normalizedEvent = clickInfo;
    }
    
    baseHandleEventClick({ event: normalizedEvent }, checkSingleProviderAvailability, setErrorMessage);
  };

  // Função para abrir o modal de múltiplos eventos já está implementada inline no CalendarWrapper

  // Função para lidar com a seleção de agendamentos no modal sequencial
  const handleSequentialAppointmentSelect = (appointment) => {
    console.log('[AppointmentCalendar] Agendamento sequencial selecionado:', appointment);
    
    // Fechar o modal sequencial
    setIsSequentialModalOpen(false);
    setSelectedSequentialGroup(null);
    
    // Normalizar o agendamento para o formato esperado pelo modal
    const normalizedAppointment = {
      id: appointment.id,
      title: appointment.title || "",
      description: appointment.extendedProps?.description || appointment.description || "",
      providerId: appointment.extendedProps?.providerId || appointment.providerId || "",
      personId: appointment.extendedProps?.personId || appointment.personId || "",
      locationId: appointment.extendedProps?.locationId || appointment.locationId || "",
      serviceTypeId: appointment.extendedProps?.serviceTypeId || appointment.serviceTypeId || "",
      insuranceId: appointment.extendedProps?.insuranceId || appointment.insuranceId || "",
      startDate: appointment.start ? (appointment.start instanceof Date ? appointment.start.toISOString() : new Date(appointment.start).toISOString()) : new Date().toISOString(),
      endDate: appointment.end ? (appointment.end instanceof Date ? appointment.end.toISOString() : new Date(appointment.end).toISOString()) : new Date().toISOString(),
      status: appointment.extendedProps?.status || appointment.status || "PENDING",
      companyId: appointment.extendedProps?.companyId || appointment.companyId || null,
      // Adicionar objetos completos para uso no modal
      insurance: appointment.extendedProps?.insurance || appointment.insurance || null,
      serviceType: appointment.extendedProps?.serviceType || appointment.serviceType || null,
      person: appointment.extendedProps?.person || appointment.person || null,
      location: appointment.extendedProps?.location || appointment.location || null,
      // Adicionar extendedProps completo para debug
      extendedProps: appointment.extendedProps || {}
    };
    
    console.log('[AppointmentCalendar] Agendamento normalizado:', normalizedAppointment);
    
    // Abrir o modal de edição com o agendamento normalizado
    setSelectedAppointment(normalizedAppointment);
    setIsModalOpen(true);
  };

  // Função para alterar status de todos os agendamentos sequenciais
  const handleBulkStatusChange = async (appointments, newStatus) => {
    console.log('[AppointmentCalendar] Alterando status em massa:', { appointments: appointments.length, newStatus });
    
    try {
      // Verificar permissões
      if (!permissions.canEditAppointment) {
        toast_error('Você não tem permissão para editar agendamentos');
        return;
      }

      // Novo: Atualização em massa via backend
      const ids = appointments.map(a => a.id);
      await appointmentService.bulkUpdateStatus(ids, newStatus);

      // Recarregar agendamentos
      await loadAppointments();
      
      // Fechar o modal sequencial
      setIsSequentialModalOpen(false);
      setSelectedSequentialGroup(null);
      
      toast_success(`Status de ${appointments.length} agendamento(s) alterado(s) com sucesso!`);
      
    } catch (error) {
      console.error('[AppointmentCalendar] Erro ao alterar status em massa:', error);
      toast_error('Erro ao alterar status dos agendamentos. Tente novamente.');
      throw error;
    }
  };

  // Função para mudar a visualização do calendário
  const handleViewChange = (newView) => {
    console.log('[AppointmentCalendar] handleViewChange chamado com:', newView);
    handleDatesSet({ view: newView });
  };

  // Função helper para extrair a data correta da viewDate
  const getCurrentDate = () => {
    if (viewDate instanceof Date) {
      return viewDate;
    } else if (viewDate && viewDate.start) {
      const date = viewDate.start instanceof Date ? viewDate.start : new Date(viewDate.start);
      return isNaN(date.getTime()) ? new Date() : date;
    }
    return new Date();
  };

  // Função para navegar no calendário
  const handleNavigate = (action) => {
    console.log('[AppointmentCalendar] handleNavigate chamado com:', action);
    console.log('[AppointmentCalendar] viewDate atual:', viewDate);
    
    // Fechar o modal primeiro (sem resetar selectedAppointment)
    if (isModalOpen) {
      console.log('[AppointmentCalendar] Fechando modal antes da navegação');
      setIsModalOpen(false);
    }
    
    // Calcular a nova data baseada na ação
    let newDate;
    const currentDate = getCurrentDate();
    
    console.log('[AppointmentCalendar] Data atual extraída:', currentDate);
    
    if (action === 'PREV') {
      if (currentView === 'timeGridWeek' || currentView === 'week') {
        newDate = addDays(currentDate, -7);
      } else if (currentView === 'timeGridDay' || currentView === 'day') {
        newDate = addDays(currentDate, -1);
      } else {
        newDate = addMonths(currentDate, -1);
      }
    } else if (action === 'NEXT') {
      if (currentView === 'timeGridWeek' || currentView === 'week') {
        newDate = addDays(currentDate, 7);
      } else if (currentView === 'timeGridDay' || currentView === 'day') {
        newDate = addDays(currentDate, 1);
      } else {
        newDate = addMonths(currentDate, 1);
      }
    } else if (action === 'TODAY') {
      newDate = new Date();
    }
    
    console.log('[AppointmentCalendar] Nova data calculada:', newDate);
    
    // Verificar se a nova data é válida
    if (isNaN(newDate.getTime())) {
      console.error('[AppointmentCalendar] Data inválida calculada, usando data atual');
      newDate = new Date();
    }
    
    // Atualizar a data usando handleDatesSet
    handleDatesSet({ start: newDate });
  };

  // Renderização de conteúdo de eventos
  const renderEventContent = (eventInfo) => {
    // Verificar se é um grupo sequencial
    if (eventInfo.event.extendedProps?.isSequentialGroup) {
      return <SequentialGroupEventContent eventInfo={eventInfo} isDarkMode={isDarkMode} />;
    }
    
    return <AppointmentEventContent eventInfo={eventInfo} isDarkMode={isDarkMode} isHighlighted={false} />;
  };

  // Configurações do calendário baseadas nas preferências
  const getCalendarConfig = () => {
    if (!preferences) return {};

    // Calcular dias ocultos baseados nos dias da semana selecionados
    const allDays = [0, 1, 2, 3, 4, 5, 6]; // Domingo a Sábado
    const selectedDays = preferences.selectedWeekDays || [1, 2, 3, 4, 5];
    const hiddenDays = allDays.filter(day => !selectedDays.includes(day));

    return {
      slotMinTime: calendarTimeRange.slotMinTime || "08:00:00",
      slotMaxTime: calendarTimeRange.slotMaxTime || "18:00:00",
      hiddenDays: hiddenDays,
      // Outras configurações podem ser adicionadas aqui conforme necessário
    };
  };

  // Função para gerar preview da exportação
  const generateExportPreview = async (format) => {
    setIsGeneratingPreview(true);
    try {
      if (format === 'image' || format === 'pdf') {
        const calendarElement = document.querySelector('.modern-calendar-container');
        if (!calendarElement) {
          console.error("Elemento do calendário não encontrado");
          return;
        }

        // Salvar estilos originais
        const originalOverflow = calendarElement.style.overflow;
        const originalHeight = calendarElement.style.height;
        calendarElement.style.overflow = 'visible';
        calendarElement.style.height = 'auto';

        // 1. Esconder o dropdown de exportação
        const existingDropdown = document.getElementById('calendar-export-dropdown-container');
        if (existingDropdown) {
          existingDropdown.style.display = 'none';
        }

        const looseDropdown = document.getElementById('calendar-export-dropdown');
        if (looseDropdown) {
          looseDropdown.style.display = 'none';
        }

        // 2. Esconder botões de exportação no header do calendário
        const exportButtons = calendarElement.querySelectorAll('.export-btn');
        exportButtons.forEach(button => {
          button.style.display = 'none';
        });

        // 3. Aplicar estilos temporários para garantir que o texto dos eventos seja renderizado corretamente
        const eventElements = calendarElement.querySelectorAll('.calendar-event-container');
        const eventStyles = [];

        eventElements.forEach(event => {
          // Salvar estilos originais
          const originalStyles = {
            overflow: event.style.overflow,
            textOverflow: event.style.textOverflow,
            whiteSpace: event.style.whiteSpace
          };

          eventStyles.push({ element: event, styles: originalStyles });

          // Aplicar estilos para garantir que o texto seja renderizado corretamente
          event.style.overflow = 'visible';
          event.style.textOverflow = 'clip';
          event.style.whiteSpace = 'normal';

          // Processar todos os elementos de texto dentro do evento
          const textElements = event.querySelectorAll('div');
          textElements.forEach(textEl => {
            // Salvar estilos originais
            const originalTextStyles = {
              overflow: textEl.style.overflow,
              textOverflow: textEl.style.textOverflow,
              whiteSpace: textEl.style.whiteSpace,
              fontFamily: textEl.style.fontFamily,
              fontSize: textEl.style.fontSize,
              lineHeight: textEl.style.lineHeight
            };

            // Adicionar ao array de estilos para restauração
            eventStyles.push({
              element: textEl,
              styles: originalTextStyles,
              isTextElement: true
            });

            // Garantir que o texto seja renderizado corretamente
            textEl.style.overflow = 'visible';
            textEl.style.textOverflow = 'clip';
            textEl.style.whiteSpace = 'normal';
            textEl.style.fontFamily = 'Arial, sans-serif';

            // Se for um texto muito pequeno, aumentar o tamanho para a exportação
            if (textEl.classList.contains('text-xs') ||
                textEl.classList.contains('text-[10px]') ||
                textEl.classList.contains('text-[9px]')) {
              textEl.style.fontSize = '12px';
              textEl.style.lineHeight = '1.2';
            }
          });
        });

        // 4. Aplicar estilos para garantir que os botões de visualização sejam renderizados corretamente
        const viewButtons = calendarElement.querySelectorAll('.view-btn, .nav-btn, .today-btn');
        const buttonStyles = [];

        viewButtons.forEach(button => {
          // Salvar estilos originais
          const originalStyles = {
            fontFamily: button.style.fontFamily,
            fontSize: button.style.fontSize,
            fontWeight: button.style.fontWeight,
            textTransform: button.style.textTransform,
            letterSpacing: button.style.letterSpacing
          };

          buttonStyles.push({ element: button, styles: originalStyles });

          // Aplicar estilos para garantir que o texto seja renderizado corretamente
          button.style.fontFamily = 'Arial, sans-serif';
          button.style.fontSize = '14px';
          button.style.fontWeight = 'bold';
          button.style.textTransform = 'none';
          button.style.letterSpacing = 'normal';

          // Garantir que o texto dentro do botão seja renderizado corretamente
          // Em vez de modificar o innerHTML, vamos apenas aplicar estilos
          // Isso evita problemas de duplicação de texto após a restauração
          const buttonSpans = button.querySelectorAll('span');
          if (buttonSpans.length > 0) {
            // Se o botão já tem spans internos, aplicar estilos a eles
            buttonSpans.forEach(span => {
              // Salvar estilos originais
              const originalSpanStyles = {
                fontFamily: span.style.fontFamily,
                fontSize: span.style.fontSize,
                fontWeight: span.style.fontWeight,
                textTransform: span.style.textTransform,
                letterSpacing: span.style.letterSpacing
              };

              // Adicionar ao array de estilos para restauração
              buttonStyles.push({
                element: span,
                styles: originalSpanStyles,
                isSpan: true
              });

              // Aplicar novos estilos
              span.style.fontFamily = 'Arial, sans-serif';
              span.style.fontSize = '14px';
              span.style.fontWeight = 'bold';
              span.style.textTransform = 'none';
              span.style.letterSpacing = 'normal';
            });
          }
        });

        // Configurações para melhorar a qualidade da imagem
        const options = {
          scale: 3,
          useCORS: true,
          allowTaint: true,
          backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
          logging: false,
          letterRendering: true,
          foreignObjectRendering: false,
          removeContainer: false,
          width: calendarElement.scrollWidth,
          height: calendarElement.scrollHeight,
          ignoreElements: (element) => {
            return element.classList && (
              element.classList.contains('export-btn') ||
              element.id === 'calendar-export-dropdown' ||
              element.id === 'calendar-export-dropdown-container'
            );
          },
          onclone: (clonedDoc) => {
            // Forçar títulos a ficarem visíveis e com fonte correta
            const titles = clonedDoc.querySelectorAll('.period-title, .current-period h2');
            titles.forEach(title => {
              title.style.overflow = 'visible';
              title.style.textOverflow = 'clip';
              title.style.whiteSpace = 'normal';
              title.style.fontFamily = 'Arial, sans-serif';
              title.style.fontSize = '32px';
              title.style.fontWeight = 'bold';
              title.style.color = '#fff';
              title.style.background = 'none';
            });
            // Forçar subtítulos
            const subtitles = clonedDoc.querySelectorAll('.period-date');
            subtitles.forEach(sub => {
              sub.style.fontFamily = 'Arial, sans-serif';
              sub.style.fontSize = '18px';
              sub.style.color = '#fff';
              sub.style.background = 'none';
            });
            // Forçar botões
            const allBtns = clonedDoc.querySelectorAll('button');
            allBtns.forEach(btn => {
              btn.style.fontFamily = 'Arial, sans-serif';
              btn.style.fontSize = '14px';
              btn.style.background = 'none';
              btn.style.color = '#fff';
            });
            // Forçar textos dos eventos
            const eventTitles = clonedDoc.querySelectorAll('.calendar-event-text');
            eventTitles.forEach(title => {
              title.style.overflow = 'visible';
              title.style.textOverflow = 'clip';
              title.style.whiteSpace = 'normal';
              title.style.fontFamily = 'Arial, sans-serif';
              title.style.fontSize = '14px';
              title.style.lineHeight = '1.2';
              title.style.color = '#fff';
            });
          }
        };

        const canvas = await html2canvas(calendarElement, options);

        // Restaurar estilos originais
        calendarElement.style.overflow = originalOverflow;
        calendarElement.style.height = originalHeight;

        // Restaurar estilos originais dos eventos
        eventStyles.forEach(item => {
          Object.assign(item.element.style, item.styles);
        });

        // Restaurar estilos originais dos botões e spans
        buttonStyles.forEach(item => {
          Object.assign(item.element.style, item.styles);
        });

        return canvas.toDataURL('image/png');
      } else {
        return null;
      }
    } catch (error) {
      console.error("Erro ao gerar preview:", error);
      toast_error('Erro ao gerar preview. Tente novamente.');
      return null;
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  // Função para exportar agendamentos
  const handleExport = async (format) => {
    // Se for formato de imagem ou PDF, mostrar preview primeiro
    if (format === 'image' || format === 'pdf') {
      setSelectedExportFormat(format);
      setShowExportPreview(true);
      
      // Gerar preview
      const previewData = await generateExportPreview(format);
      setExportPreviewData(previewData);
      return;
    }

    // Para Excel, exportar diretamente
    setIsExporting(true);
    try {
      await appointmentService.exportAppointments({
        providers: filters.providers || [],
        persons: filters.persons || [],
        serviceTypes: filters.serviceTypes || [],
        locations: filters.locations || []
      }, format);
      toast_success('Agendamentos exportados em Excel com sucesso!');
    } catch (error) {
      console.error("Erro ao exportar agendamentos:", error);
      toast_error('Erro ao exportar calendário. Tente novamente.');
    } finally {
      setIsExporting(false);
    }
  };

  // Função para confirmar exportação após preview
  const handleConfirmExport = async () => {
    setIsExporting(true);
    try {
      if (selectedExportFormat === 'image') {
        // Converter data URL para blob e salvar
        const response = await fetch(exportPreviewData);
        const blob = await response.blob();
        saveAs(blob, `calendario-${new Date().toISOString().split('T')[0]}.png`);
        toast_success('Calendário exportado como imagem com sucesso!');
      } else if (selectedExportFormat === 'pdf') {
        // Gerar PDF com o preview
        const imgData = exportPreviewData;
        
        // Obter dimensões do canvas (aproximadas)
        const canvasWidth = 1200; // Largura aproximada
        const canvasHeight = 800; // Altura aproximada
        
        // Calcular orientação e tamanho do PDF
        const orientation = canvasWidth > canvasHeight ? 'landscape' : 'portrait';
        
        // Criar novo documento PDF
        const pdf = new jsPDF({
          orientation: orientation,
          unit: 'px',
          format: [canvasWidth, canvasHeight],
          compress: true
        });
        
        // Adicionar título
        const title = "Calendário de Agendamentos";
        pdf.setFontSize(18);
        pdf.setFont('helvetica', 'bold');
        pdf.text(title, 20, 25);
        
        // Adicionar informações da visualização atual
        const viewNames = {
          'timeGridWeek': 'Visualização Semanal',
          'timeGridDay': 'Visualização Diária', 
          'dayGridMonth': 'Visualização Mensal',
          'week': 'Visualização Semanal',
          'day': 'Visualização Diária',
          'month': 'Visualização Mensal'
        };
        const currentViewName = viewNames[currentView] || 'Visualização do Calendário';
        
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');
        pdf.text(currentViewName, 20, 40);
        
        // Adicionar data atual do calendário
        const currentDate = getCurrentDate();
        const dateStr = currentDate.toLocaleDateString('pt-BR', { 
          weekday: 'long', 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        });
        pdf.text(`Período: ${dateStr}`, 20, 55);
        
        // Adicionar data de exportação
        const today = new Date().toLocaleDateString('pt-BR');
        pdf.setFontSize(10);
        pdf.text(`Exportado em: ${today} às ${new Date().toLocaleTimeString('pt-BR')}`, 20, 70);
        
        // Adicionar informações sobre filtros aplicados
        let filterInfo = [];
        if (filters.providers && filters.providers.length > 0) {
          filterInfo.push(`Profissionais: ${filters.providers.length} selecionado(s)`);
        }
        if (filters.persons && filters.persons.length > 0) {
          filterInfo.push(`Pacientes: ${filters.persons.length} selecionado(s)`);
        }
        if (filters.serviceTypes && filters.serviceTypes.length > 0) {
          filterInfo.push(`Tipos de Serviço: ${filters.serviceTypes.length} selecionado(s)`);
        }
        if (filters.locations && filters.locations.length > 0) {
          filterInfo.push(`Locais: ${filters.locations.length} selecionado(s)`);
        }
        
        if (filterInfo.length > 0) {
          pdf.text('Filtros aplicados:', 20, 85);
          filterInfo.forEach((info, index) => {
            pdf.text(`• ${info}`, 25, 95 + (index * 10));
          });
        }
        
        // Calcular posição da imagem baseada no conteúdo do cabeçalho
        const headerHeight = filterInfo.length > 0 ? 95 + (filterInfo.length * 10) + 20 : 90;
        
        // Adicionar a imagem do calendário
        pdf.addImage(imgData, 'PNG', 0, headerHeight, canvasWidth, canvasHeight);
        
        // Salvar o PDF
        pdf.save(`calendario-${new Date().toISOString().split('T')[0]}.pdf`);
        toast_success('Calendário exportado como PDF com sucesso!');
      }
    } catch (error) {
      console.error("Erro ao exportar:", error);
      toast_error('Erro ao exportar calendário. Tente novamente.');
    } finally {
      setIsExporting(false);
      setShowExportPreview(false);
      setExportPreviewData(null);
      setSelectedExportFormat(null);
    }
  };

  useEffect(() => {
    if (searchParams.get('novo') === '1') {
      setSelectedAppointment(null);
      
      // Verificar se existem parâmetros de horário
      const startTimeParam = searchParams.get('startTime');
      const endTimeParam = searchParams.get('endTime');
      
      if (startTimeParam && endTimeParam) {
        // Criar objeto de data selecionada com horários específicos
        const startDate = new Date(startTimeParam);
        const endDate = new Date(endTimeParam);
        
        setSelectedDate({
          start: startDate,
          end: endDate,
          allDay: false
        });
      }
      
      setIsModalOpen(true);
    }
  }, [searchParams]);

  // Logar currentView sempre que renderizar
  useEffect(() => {
    console.log('[AppointmentCalendar] currentView:', currentView);
  }, [currentView]);

  // Função para calcular horário automático
  const calculateAutoTime = () => {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    let startHour, startMinute, endHour, endMinute;
    
    // Se passou dos 30 minutos, ir para a próxima hora
    if (currentMinute >= 30) {
      startHour = currentHour + 1;
      startMinute = 0;
      endHour = currentHour + 1;
      endMinute = 30;
    } else {
      startHour = currentHour;
      startMinute = 0;
      endHour = currentHour;
      endMinute = 30;
    }
    
    // Criar datas para hoje com os horários calculados
    const today = new Date();
    const startDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), startHour, startMinute);
    const endDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), endHour, endMinute);
    
    return { startDate, endDate };
  };

  // Função para abrir modal de novo agendamento
  const handleNewAppointment = () => {
    const { startDate, endDate } = calculateAutoTime();
    
    setSelectedAppointment(null);
    setSelectedDate({
      start: startDate,
      end: endDate,
      allDay: false
    });
    setIsModalOpen(true);
  };

  // Verificar se o usuário tem permissão para visualizar o calendário
  if (!permissions.canViewCalendar) {
    return (
      <div className="p-6 bg-neutral-50 dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 text-center">
        <p className="text-neutral-600 dark:text-gray-300">
          Você não tem permissão para visualizar o calendário de agendamentos.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Título e botões de ação */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Calendar size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Calendário de Agendamentos
        </h1>

        <div className="flex items-center gap-2">
          <div className="text-purple-600 dark:text-purple-400">
            <CalendarExportButton
              onExport={handleExport}
              isExporting={isExporting}
              disabled={isLoading || appointments.length === 0}
              inHeader={false}
            />
          </div>
          {permissions.canCreateAppointment && !isClient() && (
            <button
              onClick={handleNewAppointment}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white rounded-lg hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700 shadow-md transition-all"
              disabled={isLoading}
            >
              <Plus size={18} />
              <span className="font-medium">Novo Agendamento</span>
            </button>
          )}
        </div>
      </div>

      {/* Cabeçalho da página */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />}
        description={isClient()
          ? "Visualize seus agendamentos e de pessoas relacionadas. Utilize os filtros abaixo para encontrar agendamentos específicos."
          : "Visualize e gerencie todos os agendamentos do sistema. Utilize os filtros abaixo para encontrar agendamentos específicos."}
        moduleColor="scheduler"
        tutorialSteps={calendarTutorialSteps}
        tutorialName="calendar-overview"
        filters={
          <div className="filter-section">
            {isClient() ? (
              <ClientCalendarFilters
                filters={filters}
                onFiltersChange={setFilters}
                onSearch={handleSearch}
              />
            ) : (
              <CalendarFilters
                filters={filters}
                onFiltersChange={setFilters}
                onSearch={handleSearch}
              />
            )}
          </div>
        }

      />

      {/* Mensagem de erro */}
      {errorMessage && (
        <div className="bg-error-50 dark:bg-red-900/20 border border-error-200 dark:border-red-800/50 text-error-800 dark:text-red-300 px-4 py-3 rounded relative">
          <span className="block sm:inline whitespace-pre-line">
            {errorMessage}
          </span>
          <button
            className="absolute top-0 bottom-0 right-0 px-4 py-3"
            onClick={() => setErrorMessage(null)}
          >
            <span className="text-error-500 dark:text-red-400 hover:text-error-800 dark:hover:text-red-300">×</span>
          </button>
        </div>
      )}

      {/* Calendário */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 z-20">
            <LoadingSpinner />
          </div>
        )}
        
        {currentView === 'timeGridWeek' || currentView === 'week' ? (
          (() => {
            console.log('[APPOINTMENT-CALENDAR] 🎯 Renderizando semana com', appointments.length, 'eventos');
            return null;
          })() ||
          <CustomWeekGrid
            events={appointments}
            startHour={calendarTimeRange?.slotMinTime ? Number(calendarTimeRange.slotMinTime.split(':')[0]) : 7}
            endHour={calendarTimeRange?.slotMaxTime ? Number(calendarTimeRange.slotMaxTime.split(':')[0]) : 20}
            weekStartDate={getCurrentDate()}
            onNavigate={handleNavigate}
            onViewChange={handleViewChange}
            onEventClick={handleEventClick}
            isDarkMode={isDarkMode}
            onCreateAppointment={handleDateSelect}
            preferences={preferences}
            allowedHours={allowedHours}
            onExport={handleExport}
            isExporting={isExporting}
            isClient={isClient()}
          />
        ) : currentView === 'timeGridDay' || currentView === 'day' ? (
          <CustomDayGrid
            events={appointments}
            startHour={calendarTimeRange?.slotMinTime ? Number(calendarTimeRange.slotMinTime.split(':')[0]) : 7}
            endHour={calendarTimeRange?.slotMaxTime ? Number(calendarTimeRange.slotMaxTime.split(':')[0]) : 20}
            selectedDate={getCurrentDate()}
            onNavigate={handleNavigate}
            onViewChange={handleViewChange}
            onEventClick={handleEventClick}
            isDarkMode={isDarkMode}
            onCreateAppointment={handleDateSelect}
            preferences={preferences}
            allowedHours={allowedHours}
            onExport={handleExport}
            isExporting={isExporting}
            isClient={isClient()}
          />
        ) : (
          <CustomMonthGrid
            events={appointments}
            monthStartDate={getCurrentDate()}
            onNavigate={handleNavigate}
            onViewChange={handleViewChange}
            onEventClick={handleEventClick}
            isDarkMode={isDarkMode}
            onCreateAppointment={handleDateSelect}
            preferences={preferences}
            allowedHours={allowedHours}
            onExport={handleExport}
            isExporting={isExporting}
            isClient={isClient()}
          />
        )}
      </div>

      {/* Modal de agendamento */}
      <AppointmentModal
        isOpen={isModalOpen}
        onClose={() => {
          console.log("[AppointmentCalendar] Fechando modal de agendamento");
          setIsModalOpen(false);
          setSelectedAppointment(null);
        }}
        selectedDate={isModalOpen && selectedAppointment == null && selectedDate && typeof selectedDate === 'object' && 'start' in selectedDate ? selectedDate : undefined}
        selectedAppointment={selectedAppointment}
        onAppointmentChange={loadAppointments}
        checkAvailability={validateAppointment}
        canCreate={permissions.canCreateAppointment}
        canEdit={permissions.canEditAppointment}
        canDelete={permissions.canDeleteAppointment}
      />

      {/* Modal de múltiplos eventos */}
      <MultipleEventsModal
        isOpen={isMultipleEventsModalOpen}
        onClose={() => setIsMultipleEventsModalOpen(false)}
        events={multipleEvents}
        date={multipleEventsDate}
        isDarkMode={isDarkMode}
        onEditAppointment={(event) => {
          // Usar o mesmo fluxo de edição que o handleEventClick
          setSelectedAppointment({
            id: event.id,
            title: event.title,
            description: event.extendedProps.description || '',
            startDate: new Date(event.start),
            endDate: new Date(event.end),
            providerId: event.extendedProps.providerId,
            personId: event.extendedProps.personId,
            locationId: event.extendedProps.locationId,
            serviceTypeId: event.extendedProps.serviceTypeId,
            insuranceId: event.extendedProps.insuranceId,
            status: event.extendedProps.status || 'PENDING',
            companyId: event.extendedProps.companyId || null,
            extendedProps: event.extendedProps
          });
          setIsModalOpen(true);
        }}
        onAppointmentChange={loadAppointments}
      />

      {/* Modal de grupos sequenciais */}
      <SequentialAppointmentsModal
        isOpen={isSequentialModalOpen}
        onClose={() => {
          setIsSequentialModalOpen(false);
          setSelectedSequentialGroup(null);
        }}
        sequentialGroup={selectedSequentialGroup}
        isDarkMode={isDarkMode}
        onSelectAppointment={handleSequentialAppointmentSelect}
        onBulkStatusChange={handleBulkStatusChange}
      />

      {/* Modal de preview da exportação */}
      <ExportPreviewModal
        isOpen={showExportPreview}
        onClose={() => {
          setShowExportPreview(false);
          setExportPreviewData(null);
          setSelectedExportFormat(null);
        }}
        onConfirm={handleConfirmExport}
        format={selectedExportFormat}
        previewData={exportPreviewData}
        isGeneratingPreview={isGeneratingPreview}
      />

      {/* Gerenciador de tutorial - IMPORTANTE: Inclua em todas as páginas que usam tutoriais */}
      <TutorialManager />
    </div>
  );
};

export default AppointmentCalendar;