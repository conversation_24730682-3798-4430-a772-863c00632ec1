// src/routes/personRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const profileImageUpload = require('../../middlewares/profileImageUpload');
const { dynamicValidation } = require('../../middlewares/dynamicValidation');
const { applyPatientPrivacyMasks } = require('../../middlewares/privacyMiddleware');
const { auditPatientDataAccess } = require('../../middlewares/auditMiddleware');
const { PersonController, createPersonValidation } = require('../../controllers/personController');
const AdvancedCacheMiddleware = require('../../middlewares/advancedCache');
const {
  validatePersonCreate,
  validatePersonUpdate,
  validateUuidParam,
  validatePagination
} = require('../../middlewares/validation');

// All routes require authentication
router.use(authenticate);

// Person CRUD routes
router.post('/',
  validatePersonCreate,
  dynamicValidation('patient'),
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['persons:*', 'clients:*'],
    tags: ['persons', 'clients']
  }),
  PersonController.create
);

router.get('/',
  validatePagination,
  AdvancedCacheMiddleware.smartCache({
    prefix: 'persons:list',
    ttl: 300, // 5 minutos
    strategy: 'search',
    tags: ['persons']
  }),
  applyPatientPrivacyMasks,
  auditPatientDataAccess,
  PersonController.list
);

router.get('/:id',
  validateUuidParam('id'),
  AdvancedCacheMiddleware.smartCache({
    prefix: 'persons:detail',
    ttl: 600, // 10 minutos
    strategy: 'standard',
    tags: ['persons']
  }),
  applyPatientPrivacyMasks,
  auditPatientDataAccess,
  PersonController.get
);

router.put('/:id',
  validateUuidParam('id'),
  validatePersonUpdate,
  dynamicValidation('patient', true),
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['persons:*', 'clients:*'],
    tags: ['persons', 'clients']
  }),
  PersonController.update
);

router.patch('/:id/status',
  validateUuidParam('id'),
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['persons:*', 'clients:*'],
    tags: ['persons', 'clients']
  }),
  PersonController.toggleStatus
);

router.delete('/:id',
  validateUuidParam('id'),
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['persons:*', 'clients:*'],
    tags: ['persons', 'clients']
  }),
  PersonController.delete
);

// Client relationship management routes
router.post('/:id/clients', validateUuidParam('id'), PersonController.addClientRelationship);
router.delete('/:id/clients/:clientId', validateUuidParam('id'), validateUuidParam('clientId'), PersonController.removeClientRelationship);
router.put('/:id/clients/:clientId', validateUuidParam('id'), validateUuidParam('clientId'), PersonController.updateClientRelationship);

// Person insurance management
router.post('/insurance', PersonController.addInsurance);
router.get('/:personId/insurances', validateUuidParam('personId'), PersonController.listInsurances);
router.put('/:personId/insurance/:insuranceId', validateUuidParam('personId'), validateUuidParam('insuranceId'), PersonController.updateInsurance);
router.delete('/:personId/insurance/:insuranceId', validateUuidParam('personId'), validateUuidParam('insuranceId'), PersonController.removeInsurance);

// Profile image management
router.post('/:id/profile-image', validateUuidParam('id'), profileImageUpload.single('profileImage'), PersonController.uploadProfileImage);
router.get('/:id/profile-image', validateUuidParam('id'), PersonController.getProfileImage);

module.exports = router;