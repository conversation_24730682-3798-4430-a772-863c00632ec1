import React from "react";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Calendar,
  Clock,
  User,
  Users,
  MapPin,
  Briefcase,
  RefreshCw
} from "lucide-react";
import { ModuleTable, ModuleCheckbox } from "@/components/ui";

// Componente para badge de status
const StatusBadge = ({ status }) => {
  const statusMap = {
    "PENDING": {
      text: "Pendente",
      className: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    },
    "CONFIRMED": {
      text: "Confirmado",
      className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    },
    "CANCELLED": {
      text: "Cancelado",
      className: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
    },
    "COMPLETED": {
      text: "Concluí<PERSON>",
      className: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
    },
    "NO_SHOW": {
      text: "Não Compareceu",
      className: "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200"
    }
  };

  const config = statusMap[status] || {
    text: status,
    className: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
  };

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.className}`}>
      {config.text}
    </span>
  );
};

const AppointmentTable = ({
  appointments,
  renderActions,
  isLoading,
  currentPage,
  totalPages,
  setCurrentPage,
  onRefresh,
  selectedIds = [],
  onSelectAll = () => {},
  onSelectOne = () => {},
  itemsPerPage = 20,
  onItemsPerPageChange = () => {},
  isClient = false,
}) => {
  // Não precisamos de funções de navegação personalizadas
  // pois o ModuleTable já lida com a paginação internamente

  // Definir colunas baseadas no tipo de usuário
  const getColumns = () => {
    const baseColumns = [
      { header: 'Título', field: 'title', width: isClient ? '18%' : '15%' },
      {
        header: 'Data/Hora',
        field: 'startDate',
        width: isClient ? '18%' : '15%',
        dataType: 'date',
        accessor: (appointment) => new Date(appointment.startDate)
      },
      {
        header: 'Paciente',
        field: 'personfullName',
        width: isClient ? '18%' : '15%',
        accessor: (appointment) => appointment.personfullName || ''
      },
      {
        header: 'Profissional',
        field: 'providerfullName',
        width: isClient ? '18%' : '15%',
        accessor: (appointment) => appointment.providerfullName || ''
      },
      {
        header: 'Local',
        field: 'locationName',
        width: isClient ? '12%' : '10%',
        accessor: (appointment) => appointment.locationName || ''
      },
      {
        header: 'Tipo de Serviço',
        field: 'serviceTypefullName',
        width: isClient ? '18%' : '15%',
        accessor: (appointment) => appointment.serviceTypefullName || ''
      },
      { header: 'Status', field: 'status', width: isClient ? '12%' : '10%' },
      { header: 'Ações', field: 'actions', className: 'text-right', width: isClient ? '8%' : '5%', sortable: false }
    ];

    // Para não-clientes, adicionar coluna de seleção no início
    if (!isClient) {
      baseColumns.unshift({ header: '', field: 'select', width: '3%', sortable: false });
    }

    return baseColumns;
  };

  return (
    <ModuleTable
      moduleColor="scheduler"
      columns={getColumns()}
      data={appointments}
      isLoading={isLoading}
      emptyMessage="Nenhum agendamento encontrado"
      emptyIcon={<Calendar size={24} />}
      currentPage={currentPage}
      totalPages={totalPages}
      onPageChange={setCurrentPage}
      showPagination={true}
      title="Lista de Agendamentos"
      itemsPerPage={itemsPerPage}
      onItemsPerPageChange={onItemsPerPageChange}
      headerContent={
        <button
          onClick={onRefresh}
          className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          disabled={isLoading}
          title="Atualizar lista"
        >
          <RefreshCw
            size={18}
            className={`text-gray-600 dark:text-gray-300 ${isLoading ? 'animate-spin' : ''}`}
          />
        </button>
      }
      tableId="scheduler-appointments-table"
      enableColumnToggle={true}
      defaultSortField="startDate"
      defaultSortDirection="desc"
      renderRow={(appointment, _index, moduleColors, visibleColumns) => (
        <tr key={appointment.id} className={moduleColors.hoverBg}>
          {!isClient && visibleColumns.includes('select') && (
            <td className="px-6 py-4 text-center">
              <ModuleCheckbox
                moduleColor="scheduler"
                checked={selectedIds.includes(appointment.id)}
                onChange={e => onSelectOne(appointment.id, e.target.checked)}
                name={`select-appointment-${appointment.id}`}
                aria-label="Selecionar agendamento"
              />
            </td>
          )}
          {visibleColumns.includes('title') && (
            <td className="px-4 py-3 whitespace-nowrap">
              <div className="flex items-center">
                <Calendar className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <div className="font-medium text-gray-900 dark:text-white truncate max-w-[150px]" title={appointment.title}>
                  {appointment.title}
                </div>
              </div>
            </td>
          )}

          {visibleColumns.includes('startDate') && (
            <td className="px-4 py-3 whitespace-nowrap">
              <div className="text-sm text-gray-900 dark:text-white">
                {dateFormat(new Date(appointment.startDate), "dd/MM/yyyy", { locale: ptBR })}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center mt-1">
                <Clock className="h-3 w-3 mr-1" />
                {dateFormat(new Date(appointment.startDate), "HH:mm", { locale: ptBR })} -
                {dateFormat(new Date(appointment.endDate), "HH:mm", { locale: ptBR })}
              </div>
            </td>
          )}

          {visibleColumns.includes('personfullName') && (
            <td className="px-4 py-3">
              <div className="flex items-center">
                <User className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <div className="text-sm text-gray-900 dark:text-white truncate max-w-[150px]" title={appointment.personfullName}>
                  {appointment.personfullName}
                </div>
              </div>
            </td>
          )}

          {visibleColumns.includes('providerfullName') && (
            <td className="px-4 py-3">
              <div className="flex items-center">
                <Users className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <div className="text-sm text-gray-900 dark:text-white truncate max-w-[150px]" title={appointment.providerfullName}>
                  {appointment.providerfullName}
                </div>
              </div>
            </td>
          )}

          {visibleColumns.includes('locationName') && (
            <td className="px-4 py-3">
              <div className="flex items-center">
                <MapPin className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <div className="text-sm text-gray-900 dark:text-white truncate max-w-[150px]">
                  {appointment.locationName || "Sem localização"}
                </div>
              </div>
            </td>
          )}

          {visibleColumns.includes('serviceTypefullName') && (
            <td className="px-4 py-3">
              <div className="flex items-center">
                <Briefcase className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <div className="text-sm text-gray-900 dark:text-white truncate max-w-[150px]" title={appointment.serviceTypefullName}>
                  {appointment.serviceTypefullName}
                </div>
              </div>
            </td>
          )}

          {visibleColumns.includes('status') && (
            <td className="px-4 py-3">
              <StatusBadge status={appointment.status} />
            </td>
          )}

          {visibleColumns.includes('actions') && (
            <td className="px-4 py-3 text-right">
              {renderActions(appointment)}
            </td>
          )}
        </tr>
      )}
      selectedIds={isClient ? [] : selectedIds}
      onSelectAll={isClient ? () => {} : onSelectAll}
      renderHeaderRow={(_columns, visibleColumns) => (
        <tr>
          {!isClient && visibleColumns.includes('select') && (
            <th className="px-4 py-3 text-center">
              <ModuleCheckbox
                moduleColor="scheduler"
                checked={appointments.length > 0 && appointments.every(a => selectedIds.includes(a.id))}
                indeterminate={appointments.some(a => selectedIds.includes(a.id)) && !appointments.every(a => selectedIds.includes(a.id))}
                onChange={e => onSelectAll(e.target.checked)}
                name="select-all-appointments"
                aria-label="Selecionar todos"
              />
            </th>
          )}
          {visibleColumns.includes('title') && (
            <th className="px-4 py-3 whitespace-nowrap">
              <div className="flex items-center">
                <Calendar className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Título
                </span>
              </div>
            </th>
          )}
          {visibleColumns.includes('startDate') && (
            <th className="px-4 py-3 whitespace-nowrap">
              <div className="flex items-center">
                <Clock className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Data/Hora
                </span>
              </div>
            </th>
          )}
          {visibleColumns.includes('personfullName') && (
            <th className="px-4 py-3">
              <div className="flex items-center">
                <User className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Paciente
                </span>
              </div>
            </th>
          )}
          {visibleColumns.includes('providerfullName') && (
            <th className="px-4 py-3">
              <div className="flex items-center">
                <Users className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Profissional
                </span>
              </div>
            </th>
          )}
          {visibleColumns.includes('locationName') && (
            <th className="px-4 py-3">
              <div className="flex items-center">
                <MapPin className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Local
                </span>
              </div>
            </th>
          )}
          {visibleColumns.includes('serviceTypefullName') && (
            <th className="px-4 py-3">
              <div className="flex items-center">
                <Briefcase className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Tipo de Serviço
                </span>
              </div>
            </th>
          )}
          {visibleColumns.includes('status') && (
            <th className="px-4 py-3">
              <div className="flex items-center">
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </span>
              </div>
            </th>
          )}
          {visibleColumns.includes('actions') && (
            <th className="px-4 py-3 text-right">
              <div className="flex items-center">
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Ações
                </span>
              </div>
            </th>
          )}
        </tr>
      )}
    />
  );
};

export default AppointmentTable;