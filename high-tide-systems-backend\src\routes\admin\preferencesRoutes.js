const express = require('express');
const router = express.Router();
const preferencesController = require('../../controllers/preferencesController');
const checkRole = require('../../middlewares/roleCheck');
const { clearCacheOnPreferencesUpdate } = require('../../middlewares/privacyMiddleware');
const { auditPrivacyConfigChanges } = require('../../middlewares/auditMiddleware');

router.get('/', preferencesController.getPreferences);
router.put('/', clearCacheOnPreferencesUpdate, auditPrivacyConfigChanges, preferencesController.updatePreferences);

// Rotas específicas para SYSTEM_ADMIN gerenciar preferências de empresas
router.get('/company/:companyId', checkRole(['SYSTEM_ADMIN']), preferencesController.getPreferencesForCompany);
router.put('/company/:companyId', checkRole(['SYSTEM_ADMIN']), clearCacheOnPreferencesUpdate, auditPrivacyConfigChanges, preferencesController.updatePreferencesForCompany);

module.exports = router; 