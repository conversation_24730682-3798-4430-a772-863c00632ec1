'use client';

import React, { useState, useEffect } from 'react';
import { Share2, MessageCircle, X, Search, User, Plus } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';
import { shareService } from '@/services/shareService';
import { useToast } from '@/contexts/ToastContext';
import { useAuth } from '@/contexts/AuthContext';
import { usersService } from '@/app/modules/admin/services/usersService';
import { motion, AnimatePresence } from 'framer-motion';
import { getRelatedClientIds, filterConversationsByRelatedClients, canShareWithUser } from '@/utils/sharePermissions';

const ShareButton = ({ 
  itemType, 
  itemId, 
  itemTitle, 
  className = '',
  size = 'sm',
  variant = 'default'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showNewChat, setShowNewChat] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [availableUsers, setAvailableUsers] = useState([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [buttonRect, setButtonRect] = useState(null);
  const [relatedClientIds, setRelatedClientIds] = useState([]);
  const [isLoadingPermissions, setIsLoadingPermissions] = useState(false);
  const { conversations, sendMessage, createOrGetConversation } = useChat();
  const { toast_success, toast_error } = useToast();
  const { user } = useAuth();

  // Carregar clientes relacionados quando o modal for aberto
  useEffect(() => {
    if (isOpen && itemType && itemId) {
      loadRelatedClients();
    }
  }, [isOpen, itemType, itemId]);

  const loadRelatedClients = async () => {
    setIsLoadingPermissions(true);
    try {
      const clientIds = await getRelatedClientIds(itemType, itemId);
      setRelatedClientIds(clientIds);
    } catch (error) {
      console.error('Erro ao carregar clientes relacionados:', error);
      setRelatedClientIds([]);
    } finally {
      setIsLoadingPermissions(false);
    }
  };

  const handleShare = async (conversationId) => {
    if (!conversationId) return;

    setIsLoading(true);
    try {
      let result;
      
      switch (itemType) {
        case 'appointment':
          result = await shareService.shareAppointment(conversationId, itemId, sendMessage);
          break;
        case 'person':
          result = await shareService.sharePerson(conversationId, itemId, sendMessage);
          break;
        case 'client':
          result = await shareService.shareClient(conversationId, itemId, sendMessage);
          break;
        case 'user':
          result = await shareService.shareUser(conversationId, itemId, sendMessage);
          break;
        case 'serviceType':
          result = await shareService.shareServiceType(conversationId, itemId, sendMessage);
          break;
        case 'location':
          result = await shareService.shareLocation(conversationId, itemId, sendMessage);
          break;
        case 'workingHours':
          result = await shareService.shareWorkingHours(conversationId, itemId, sendMessage);
          break;
        case 'insurance':
          result = await shareService.shareInsurance(conversationId, itemId, sendMessage);
          break;
        case 'insurance-limit':
          result = await shareService.shareInsuranceLimit(conversationId, itemId, sendMessage);
          break;
        default:
          throw new Error('Tipo de item não suportado');
      }

      toast_success(result.message);
      setIsOpen(false);
    } catch (error) {
      console.error('Erro ao compartilhar:', error);
      toast_error('Erro ao compartilhar item. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleShareWithNewChat = async (userId) => {
    try {
      const conversation = await createOrGetConversation({ id: userId });
      if (conversation) {
        await handleShare(conversation.id);
      }
    } catch (error) {
      console.error('Erro ao criar conversa:', error);
      toast_error('Erro ao criar conversa');
    }
  };

  const searchUsers = async (term) => {
    if (!term || term.length < 2) {
      setAvailableUsers([]);
      return;
    }

    setIsLoadingUsers(true);
    try {
      const response = await usersService.getUsers({
        search: term,
        limit: 20,
        active: true
      });

      // Filtrar o usuário atual da lista e aplicar filtros de permissão
      let filteredUsers = response.users?.filter(u => u.id !== user?.id) || [];
      
      // Aplicar filtro de permissões de compartilhamento
      if (relatedClientIds.length > 0) {
        filteredUsers = filteredUsers.filter(u => canShareWithUser(u, relatedClientIds));
      }
      
      setAvailableUsers(filteredUsers);
    } catch (error) {
      console.error('Erro ao buscar usuários:', error);
      toast_error('Erro ao buscar usuários');
    } finally {
      setIsLoadingUsers(false);
    }
  };

  const handleStartNewChat = async (selectedUser) => {
    try {
      setIsLoading(true);
      const conversation = await createOrGetConversation(selectedUser);
      if (conversation) {
        await handleShare(conversation.id);
      }
    } catch (error) {
      console.error('Erro ao iniciar nova conversa:', error);
      toast_error('Erro ao iniciar nova conversa');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearchChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    // Debounce para buscar usuários, mas apenas se as permissões já foram carregadas
    if (value.length >= 2 && !isLoadingPermissions) {
      setTimeout(() => searchUsers(value), 300);
    } else {
      setAvailableUsers([]);
    }
  };

  const getButtonStyles = () => {
    const baseStyles = 'inline-flex items-center gap-2 font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    switch (variant) {
      case 'outline':
        return `${baseStyles} border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 focus:ring-blue-500 rounded-lg`;
      case 'ghost':
        return `${baseStyles} bg-transparent text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 focus:ring-blue-500 border-0 rounded-lg shadow-none`;
      case 'primary':
        return `${baseStyles} bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 rounded-lg`;
      default:
        return `${baseStyles} bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 focus:ring-blue-500 rounded-lg`;
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'xs':
        return 'px-2 py-1 text-xs';
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'md':
        return 'px-4 py-2 text-sm';
      case 'lg':
        return 'px-5 py-2.5 text-base';
      default:
        return 'px-3 py-1.5 text-sm';
    }
  };

  const handleButtonClick = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setButtonRect(rect);
    setIsOpen(!isOpen);
  };

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={handleButtonClick}
        className={`${getButtonStyles()} ${getSizeStyles()}`}
        disabled={isLoading}
      >
        <Share2 className="w-4 h-4" />
        {size !== 'xs' && 'Compartilhar'}
      </button>

      <AnimatePresence>
        {isOpen && buttonRect && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            className="fixed w-96 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-[9999]"
            style={{
              top: buttonRect.bottom + 8,
              left: Math.max(16, Math.min(buttonRect.right - 384, window.innerWidth - 384 - 16)), // 384px = w-96, com margens
            }}
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                  Compartilhar {itemTitle}
                </h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              <div className="space-y-3">
                {/* Botão para nova conversa */}
                <div className="border-b border-gray-100 dark:border-gray-700 pb-3">
                  <button
                    onClick={() => setShowNewChat(!showNewChat)}
                    className="w-full flex items-center gap-2 px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                    <span>Nova conversa</span>
                  </button>
                </div>

                {/* Seção de nova conversa */}
                {showNewChat && (
                  <div className="space-y-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder={isLoadingPermissions ? "Verificando permissões..." : "Buscar usuário..."}
                        value={searchTerm}
                        onChange={handleSearchChange}
                        disabled={isLoadingPermissions}
                        className="w-full pl-10 pr-3 py-2 text-sm border border-gray-200 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                      />
                    </div>

                    {isLoadingUsers && (
                      <div className="flex items-center justify-center py-4">
                        <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                        <span className="ml-2 text-sm text-gray-500">Buscando usuários...</span>
                      </div>
                    )}

                    {availableUsers.length > 0 && (
                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {availableUsers.map((userItem) => (
                          <button
                            key={userItem.id}
                            onClick={() => handleStartNewChat(userItem)}
                            disabled={isLoading}
                            className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                          >
                            <div className="flex items-center gap-2">
                              <User className="w-4 h-4" />
                              <span className="truncate">{userItem.fullName}</span>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}

                    {searchTerm.length >= 2 && !isLoadingUsers && availableUsers.length === 0 && (
                      <div className="text-center py-4">
                        <User className="w-6 h-6 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Nenhum usuário encontrado
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Conversas existentes */}
                {(() => {
                  // Aplicar filtro de permissões nas conversas
                  const filteredConversations = isLoadingPermissions 
                    ? conversations 
                    : filterConversationsByRelatedClients(conversations, relatedClientIds);
                  
                  return filteredConversations.length > 0 ? (
                    <div>
                      <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Conversas recentes
                        {relatedClientIds.length > 0 && filteredConversations.length !== conversations.length && (
                          <span className="text-orange-600 dark:text-orange-400 ml-1">
                            (filtrado para clientes relacionados)
                          </span>
                        )}
                      </h4>
                      {isLoadingPermissions ? (
                        <div className="flex items-center justify-center py-4">
                          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                          <span className="ml-2 text-sm text-gray-500">Verificando permissões...</span>
                        </div>
                      ) : (
                        <div className="space-y-1 max-h-32 overflow-y-auto">
                          {filteredConversations.map((conversation) => (
                            <button
                              key={conversation.id}
                              onClick={() => handleShare(conversation.id)}
                              disabled={isLoading}
                              className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                            >
                              <div className="flex items-center gap-2">
                                <MessageCircle className="w-4 h-4" />
                                <span className="truncate">
                                  {conversation.type === 'GROUP' 
                                    ? conversation.title || 'Grupo'
                                    : conversation.participants?.find(p => p.userId !== user?.id)?.user?.fullName || 'Conversa'
                                  }
                                </span>
                              </div>
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : null;
                })()}

                {(() => {
                  const filteredConversations = isLoadingPermissions 
                    ? conversations 
                    : filterConversationsByRelatedClients(conversations, relatedClientIds);
                  
                  return filteredConversations.length === 0 && !showNewChat && !isLoadingPermissions ? (
                    <div className="text-center py-4">
                      <MessageCircle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {relatedClientIds.length > 0 && filteredConversations.length === 0 && conversations.length > 0
                          ? 'Nenhuma conversa com clientes relacionados'
                          : 'Nenhuma conversa encontrada'
                        }
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                        {relatedClientIds.length > 0 && filteredConversations.length === 0 && conversations.length > 0
                          ? 'Este item só pode ser compartilhado com clientes específicos. Use "Nova conversa" para encontrar usuários.'
                          : 'Use "Nova conversa" para compartilhar este item'
                        }
                      </p>
                    </div>
                  ) : null;
                })()}
              </div>

              {isLoading && (
                <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                    Compartilhando...
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Overlay para fechar o modal */}
      {isOpen && (
        <div
          className="fixed inset-0 z-[9998]"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default ShareButton; 