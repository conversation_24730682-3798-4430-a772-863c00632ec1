'use client';

import React from 'react';
import { ChevronRight } from 'lucide-react';

// Mapeamento de ações para módulos específicos baseado no contexto
const getModuleForAction = (action, index) => {
  // Mapear ações específicas para seus módulos correspondentes
  if (action.path?.includes('/admin/') || action.title?.toLowerCase().includes('admin') || action.title?.toLowerCase().includes('usuário') || action.title?.toLowerCase().includes('configuração')) {
    return 'admin';
  }
  if (action.path?.includes('/people/') || action.title?.toLowerCase().includes('cliente') || action.title?.toLowerCase().includes('paciente') || action.title?.toLowerCase().includes('pessoa')) {
    return 'people';
  }
  if (action.path?.includes('/scheduler/') || action.title?.toLowerCase().includes('agenda') || action.title?.toLowerCase().includes('consulta') || action.title?.toLowerCase().includes('horário')) {
    return 'scheduler';
  }
  if (action.path?.includes('/financial/') || action.title?.toLowerCase().includes('financeiro') || action.title?.toLowerCase().includes('pagamento')) {
    return 'financial';
  }
  
  // Fallback para rotação entre módulos principais
  const fallbackModules = ['scheduler', 'people', 'admin'];
  return fallbackModules[index % fallbackModules.length];
};

export const ActionButton = ({ action, index, onClick }) => {
  // Determina o módulo baseado no contexto da ação
  const moduleId = getModuleForAction(action, index);
  
  return (
    <button
      onClick={() => onClick(action.path, action)}
      className={`
        p-4 rounded-xl flex items-center justify-between
        shadow-md dark:shadow-lg dark:shadow-black/20 transition-all duration-300 
        border-2 border-module-${moduleId}-border dark:border-module-${moduleId}-border-dark
        hover:shadow-xl dark:hover:shadow-lg dark:hover:shadow-black/30 transform hover:-translate-y-1
        bg-white dark:bg-gray-800
      `}
      aria-label={action.title}
    >
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg bg-module-${moduleId}-bg dark:bg-module-${moduleId}-bg-dark/70`} aria-hidden="true">
          <action.icon size={20} className={`text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark`} />
        </div>
        <div className="text-left">
          <span className="font-bold text-lg text-gray-800 dark:text-gray-100">{action.title}</span>
          <p className="text-xs mt-0.5 text-gray-600 dark:text-gray-300">{action.description}</p>
        </div>
      </div>
      <ChevronRight 
        size={18} 
        className={`text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark transition-transform group-hover:translate-x-1`} 
        aria-hidden="true" 
      />
    </button>
  );
};

export default ActionButton;