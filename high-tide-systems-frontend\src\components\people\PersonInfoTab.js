"use client";

import React, { useState, useEffect, useRef } from "react";
import { User, Mail, Phone, CreditCard, Calendar, MapPin, FileText, Users } from "lucide-react";
import { InputMask } from "@react-input/mask";
import { personsService } from "@/app/modules/people/services/personsService";
import { clientsService } from "@/app/modules/people/services/clientsService";
import UserProfileImageUpload from "@/components/forms/UserProfileImageUpload";
import AddressForm from "@/components/common/AddressForm";
import MultiClientSelector from "./MultiClientSelector";
import { ModuleInput, ModuleSelect, ModuleTextarea, ModuleFormGroup, ModuleMaskedInput } from "@/components/ui";
import { usePreferences } from "@/hooks/usePreferences";

const PersonInfoTab = ({
  formData,
  setFormData,
  errors,
  isLoading,
  handleChange,
  onSubmit,
  personId,
  profileImageUploadRef,
  isCreating,
  onSetTempProfileImage,
  tempProfileImage
}) => {
  const { getRequiredFieldsForValidation } = usePreferences();
  const requiredFields = getRequiredFieldsForValidation('patient');
  const [clients, setClients] = useState([]);
  const [isLoadingClients, setIsLoadingClients] = useState(false);
  const [selectedImageFile, setSelectedImageFile] = useState(null);
  const [selectedClient, setSelectedClient] = useState(null);
  const [isLoadingClientData, setIsLoadingClientData] = useState(false);

  // Fetch clients for dropdown
  useEffect(() => {
    fetchClients();
  }, []);

  // Fetch client data when clientId changes
  useEffect(() => {
    if (formData.clientId) {
      fetchClientData(formData.clientId);
    } else {
      setSelectedClient(null);
    }
  }, [formData.clientId]);

  const fetchClients = async () => {
    setIsLoadingClients(true);
    try {
      const clientsList = await personsService.getClientsForSelect();
      setClients(clientsList);
    } catch (error) {
      console.error("Error fetching clients:", error);
    } finally {
      setIsLoadingClients(false);
    }
  };

  const fetchClientData = async (clientId) => {
    if (!clientId) return;

    setIsLoadingClientData(true);
    try {
      const clientData = await clientsService.getClient(clientId);
      setSelectedClient(clientData);

      // If checkboxes are checked, update the form data with client info
      if (formData.useClientEmail && clientData.email) {
        handleChange({
          target: { name: "email", value: clientData.email }
        });
      }

      // For phone, we need to get it from the client's person
      if (formData.useClientPhone && clientData.persons && clientData.persons.length > 0) {
        const personPhone = clientData.persons[0].phone;
        if (personPhone) {
          // Format phone number
          const cleanPhone = personPhone.replace(/\D/g, "");
          const formattedPhone = cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");

          handleChange({
            target: { name: "phone", value: formattedPhone }
          });
        }
      }
    } catch (error) {
      console.error("Error fetching client data:", error);
    } finally {
      setIsLoadingClientData(false);
    }
  };

  // Não precisamos mais dessas classes, pois usaremos os componentes de módulo

  // Simplificar o onSubmit para apenas chamar o onSubmit do componente pai
  const handleSubmit = async () => {
    console.log('Chamando onSubmit para salvar os dados da pessoa');
    await onSubmit();
  };

  // Função para renderizar label com asterisco vermelho se obrigatório
  const renderLabel = (fieldName, labelText) => {
    const isRequired = requiredFields[fieldName];
    return (
      <span>
        {labelText}
        {isRequired && <span className="text-red-500 ml-1">*</span>}
      </span>
    );
  };

  return (
    <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
      <div className="mb-8">
        <div className="border-b-2 border-orange-400 dark:border-orange-500 pb-3 mb-4">
          <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-orange-500 pl-3">
            Dados do Paciente
          </h4>
          <p className="text-xs text-neutral-600 dark:text-gray-300 pl-3">
            Informações pessoais do paciente:
          </p>
        </div>
      </div>

      {/* Imagem de perfil */}
      <div className="flex justify-center mb-6">
        <UserProfileImageUpload
          userId={personId}
          initialImageUrl={formData.profileImageFullUrl}
          deferUpload={true}
          uploadRef={profileImageUploadRef}
          onImageUploaded={(url, file) => {
            console.log('Imagem selecionada ou URL recebida:', url ? 'URL' : 'Arquivo');

            if (isCreating) {
              // Se estiver criando uma nova pessoa, armazenar o arquivo para upload posterior
              console.log('Armazenando arquivo de imagem temporário para nova pessoa');
              onSetTempProfileImage && onSetTempProfileImage(file);
            } else if (url) {
              // Se estiver editando uma pessoa existente e receber uma URL
              console.log('Nova URL da imagem recebida:', url);
              handleChange({
                target: {
                  name: 'profileImageFullUrl',
                  value: url
                }
              });
            }

            // Armazenar o arquivo selecionado para referência
            setSelectedImageFile(file);
            console.log('Arquivo de imagem armazenado:', file ? file.name : 'Nenhum');
          }}
          size="large"
          disabled={isLoading}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {/* Nome completo */}
            <ModuleFormGroup
              moduleColor="people"
              label={renderLabel("fullName", "Nome completo")}
              htmlFor="fullName"
              icon={<User size={16} />}
              error={errors.fullName}
              errorMessage={errors.fullName}
              className="md:col-span-2"
            >
              <ModuleInput
                moduleColor="people"
                type="text"
                id="fullName"
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                placeholder="Nome completo"
                disabled={isLoading}
                error={!!errors.fullName}
              />
            </ModuleFormGroup>

            {/* CPF */}
            <ModuleFormGroup
              moduleColor="people"
              label={renderLabel("cpf", "CPF")}
              htmlFor="cpf"
              icon={<CreditCard size={16} />}
              error={errors.cpf}
              errorMessage={errors.cpf}
            >
              <ModuleMaskedInput
                moduleColor="people"
                mask="999.999.999-99"
                replacement={{ 9: /[0-9]/ }}
                value={formData.cpf}
                onChange={(e) =>
                  handleChange({
                    target: { name: "cpf", value: e.target.value },
                  })
                }
                placeholder="000.000.000-00"
                disabled={isLoading}
                error={!!errors.cpf}
              />
            </ModuleFormGroup>

            {/* Data de nascimento */}
            <ModuleFormGroup
              moduleColor="people"
              label={renderLabel("birthDate", "Data de Nascimento")}
              htmlFor="birthDate"
              icon={<Calendar size={16} />}
              error={!!errors.birthDate}
              errorMessage={errors.birthDate}
            >
              <ModuleInput
                moduleColor="people"
                type="date"
                id="birthDate"
                name="birthDate"
                value={formData.birthDate}
                onChange={handleChange}
                disabled={isLoading}
                error={!!errors.birthDate}
                min="1900-01-01"
                max={new Date().toISOString().split('T')[0]}
              />
            </ModuleFormGroup>

            {/* Gênero */}
            <ModuleFormGroup
              moduleColor="people"
              label={renderLabel("gender", "Gênero")}
              htmlFor="gender"
              icon={<User size={16} />}
            >
              <ModuleSelect
                moduleColor="people"
                id="gender"
                name="gender"
                value={formData.gender}
                onChange={handleChange}
                disabled={isLoading}
              >
                <option value="">Selecione</option>
                <option value="M">Masculino</option>
                <option value="F">Feminino</option>
                <option value="O">Outro</option>
              </ModuleSelect>
            </ModuleFormGroup>

            {/* Email */}
            <ModuleFormGroup
              moduleColor="people"
              label={renderLabel("email", "Email")}
              htmlFor="email"
              icon={<Mail size={16} />}
              error={errors.email}
              errorMessage={errors.email}
            >
              <div className="space-y-2">
                <ModuleInput
                  moduleColor="people"
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  disabled={isLoading || formData.useClientEmail}
                  error={!!errors.email}
                />
                {formData.clientId && (
                  <div className="flex items-center mt-2">
                    <input
                      type="checkbox"
                      id="useClientEmail"
                      name="useClientEmail"
                      checked={formData.useClientEmail}
                      onChange={(e) => {
                        const isChecked = e.target.checked;
                        handleChange({
                          target: { name: "useClientEmail", value: isChecked }
                        });

                        // If checked and we have client data, update email
                        if (isChecked && selectedClient?.email) {
                          handleChange({
                            target: { name: "email", value: selectedClient.email }
                          });
                        }
                      }}
                      disabled={isLoading || isLoadingClientData || !selectedClient}
                      className="h-4 w-4 text-module-people-primary border-gray-300 rounded focus:ring-module-people-primary dark:focus:ring-module-people-primary-dark"
                    />
                    <label htmlFor="useClientEmail" className="ml-2 text-sm text-gray-600 dark:text-gray-300">
                      Usar email do cliente
                    </label>
                  </div>
                )}
              </div>
            </ModuleFormGroup>

            {/* Telefone */}
            <ModuleFormGroup
              moduleColor="people"
              label={renderLabel("phone", "Telefone")}
              htmlFor="phone"
              icon={<Phone size={16} />}
            >
              <div className="space-y-2">
                <ModuleMaskedInput
                  moduleColor="people"
                  mask="(99) 99999-9999"
                  replacement={{ 9: /[0-9]/ }}
                  value={formData.phone}
                  onChange={(e) =>
                    handleChange({
                      target: { name: "phone", value: e.target.value },
                    })
                  }
                  placeholder="(00) 00000-0000"
                  disabled={isLoading || formData.useClientPhone}
                />
                {formData.clientId && (
                  <div className="flex items-center mt-2">
                    <input
                      type="checkbox"
                      id="useClientPhone"
                      name="useClientPhone"
                      checked={formData.useClientPhone}
                      onChange={(e) => {
                        const isChecked = e.target.checked;
                        handleChange({
                          target: { name: "useClientPhone", value: isChecked }
                        });

                        // If checked and we have client data with persons, update phone
                        if (isChecked && selectedClient?.persons && selectedClient.persons.length > 0) {
                          const personPhone = selectedClient.persons[0].phone;
                          if (personPhone) {
                            // Format phone number
                            const cleanPhone = personPhone.replace(/\D/g, "");
                            const formattedPhone = cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");

                            handleChange({
                              target: { name: "phone", value: formattedPhone }
                            });
                          }
                        }
                      }}
                      disabled={isLoading || isLoadingClientData || !selectedClient || !selectedClient.persons || selectedClient.persons.length === 0}
                      className="h-4 w-4 text-module-people-primary border-gray-300 rounded focus:ring-module-people-primary dark:focus:ring-module-people-primary-dark"
                    />
                    <label htmlFor="useClientPhone" className="ml-2 text-sm text-gray-600 dark:text-gray-300">
                      Usar telefone do cliente
                    </label>
                  </div>
                )}
              </div>
            </ModuleFormGroup>

            {/* Endereço */}
            <div className="md:col-span-2">
              <div className="border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-3 mt-4">
                <h3 className="text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2">
                  <MapPin className="w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark" />
                  Endereço
                </h3>
              </div>
              <AddressForm
                formData={formData}
                setFormData={setFormData}
                errors={errors}
                isLoading={isLoading}
                fieldMapping={{
                  // Mapeamento personalizado para os campos da API ViaCEP
                  logradouro: "address",
                  bairro: "neighborhood",
                  localidade: "city",
                  uf: "state",
                  cep: "postalCode"
                }}
                moduleColor="people"
                requiredFields={{
                  postalCode: requiredFields.patientCep,
                  address: false, // Endereço não é obrigatório por padrão
                  neighborhood: false,
                  city: false,
                  state: false
                }}
                classes={{
                  label: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",
                  input: "block w-full pl-10 pr-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-people-border focus-visible:!border-module-people-border dark:focus-visible:!ring-module-people-border-dark dark:focus-visible:!border-module-people-border-dark bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100",
                  error: "mt-1 text-xs text-red-600 dark:text-red-400",
                  iconContainer: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                }}
              />
        </div>

        {/* Múltiplos Clientes */}
        <div className="md:col-span-2">
          <MultiClientSelector
            personId={personId}
            initialClients={formData.clientPersons || []}
            onClientsChange={(clients) => {
              setFormData(prev => ({
                ...prev,
                clientPersons: clients
              }));
            }}
            isLoading={isLoading}
            disabled={isLoading}
            isRequired={requiredFields.patientAssociateClient}
          />
        </div>

        {/* Observações */}
        <ModuleFormGroup
          moduleColor="people"
          label={renderLabel("notes", "Observações")}
          htmlFor="notes"
          icon={<FileText size={16} />}
          className="md:col-span-2"
        >
          <ModuleTextarea
            moduleColor="people"
            id="notes"
            name="notes"
            value={formData.notes}
            onChange={handleChange}
            placeholder="Observações adicionais"
            rows={3}
            disabled={isLoading}
          />
        </ModuleFormGroup>
      </div>
    </form>
  );
};

export default PersonInfoTab;