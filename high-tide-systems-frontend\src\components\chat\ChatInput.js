'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send, Paperclip, Smile, Mic, X, File } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';
import { motion, AnimatePresence } from 'framer-motion';

const ChatInput = ({ conversationId, compact = false }) => {
  const [message, setMessage] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [isRecording, setIsRecording] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef(null);
  const emojiPickerRef = useRef(null);
  const { sendMessage } = useChat();

  // Categorias de emojis organizadas
  const emojiCategories = {
    recentes: {
      icon: '🕒',
      emojis: ['😊', '😂', '❤️', '👍', '🔥', '💯', '🎉', '🙏']
    },
    rostos: {
      icon: '😊',
      emojis: [
        '😀', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃',
        '😉', '😊', '😇', '🥰', '😍', '🤩', '😘', '😗', '☺️', '😚',
        '😙', '🥲', '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭',
        '🤫', '🤔', '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄',
        '😬', '🤥', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢',
        '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '🥸',
        '😎', '🤓', '🧐', '😕', '😟', '🙁', '☹️', '😮', '😯', '😲',
        '😳', '🥺', '😦', '😧', '😨', '😰', '😥', '😢', '😭', '😱',
        '😖', '😣', '😞', '😓', '😩', '😫', '🥱', '😤', '😡', '😠',
        '🤬', '😈', '👿', '💀', '☠️', '💩', '🤡', '👹', '👺', '👻'
      ]
    },
    gestos: {
      icon: '👍',
      emojis: [
        '🤚', '🖐️', '✋', '🖖', '👌', '🤌', '🤏', '✌️', '🤞',
        '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍',
        '👎', '✊', '🤛', '🤜', '👏', '🙌', , '🤲', '🤝',
        '🙏', '✍️', '💅', '🤳', '💪', '🦾', '🦿', '🦵', '🦶', '👂',
        '🦻', '👃', '🧠', '🫀', '🫁', '🦷', '🦴', '👀', '👁️', '👅'
      ]
    },
    pessoas: {
      icon: '👤',
      emojis: [
        '👶', '🧒', '👦', '👧', '🧑', '👱', '👨', '🧔', '👩', '🧓',
        '👴', '👵', '🙍', '🙎', '🙅', '🙆', '💁', '🙋', '🧏', '🙇',
        '🤦', '🤷', '👮', '🕵️', '💂', '🥷', '👷', '🤴', '👸', '👳',
        '👲', '🧕', '🤵', '👰', '🤰', '🤱', '👼', '🎅', '🤶', '🦸',
        '🦹', '🧙', '🧚', '🧛', '🧜', '🧝', '🧞', '🧟', '💆', '💇'
      ]
    },
    animais: {
      icon: '🐶',
      emojis: [
        '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
        '🦁', '🐮', '🐷', '🐽', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒',
        '🐔', '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇',
        '🐺', '🐗', '🐴', '🦄', '🐝', '🐛', '🦋', '🐌', '🐞', '🐜',
        '🦟', '🦗', '🕷️', '🕸️', '🦂', '🐢', '🐍', '🦎', '🦖', '🦕',
        '🐙', '🦑', '🦐', '🦞', '🦀', '🐡', '🐠', '🐟', '🐬', '🐳',
        '🐋', '🦈', '🐊', '🐅', '🐆', '🦓', '🦍', '🦧', '🐘', '🦛',
        '🦏', '🐪', '🐫', '🦒', '🦘', '🐃', '🐂', '🐄', '🐎', '🐖'
      ]
    },
    comida: {
      icon: '🍕',
      emojis: [
        '🍎', '🍐', '🍊', '🍋', '🍌', '🍉', '🍇', '🍓', '🫐', '🍈',
        '🍒', '🍑', '🥭', '🍍', '🥥', '🥝', '🍅', '🍆', '🥑', '🥦',
        '🥬', '🥒', '🌶️', '🫑', '🌽', '🥕', '🫒', '🧄', '🧅', '🥔',
        '🍠', '🥐', '🥖', '🍞', '🥨', '🥯', '🧀', '🥚', '🍳', '🧈',
        '🥞', '🧇', '🥓', '🥩', '🍗', '🍖', '🦴', '🌭', '🍔', '🍟',
        '🍕', '🥪', '🥙', '🧆', '🌮', '🌯', '🫔', '🥗', '🥘', '🫕',
        '🍝', '🍜', '🍲', '🍛', '🍣', '🍱', '🥟', '🦪', '🍤', '🍙',
        '🍚', '🍘', '🍥', '🥠', '🥮', '🍢', '🍡', '🍧', '🍨', '🍦'
      ]
    },
    atividades: {
      icon: '⚽',
      emojis: [
        '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐', '🏉', '🥏', '🎱',
        '🪀', '🏓', '🏸', '🏒', '🏑', '🥍', '🏏', '🪃', '🥅', '⛳',
        '🪁', '🏹', '🎣', '🤿', '🥊', '🥋', '🎽', '🛹', '🛷', '⛸️',
        '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️', '🤼', '🤸', '⛹️', '🤺',
        '🏇', '🧘', '🏄', '🏊', '🤽', '🚣', '🧗', '🚵', '🚴', '🏆',
        '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️', '🎫', '🎟️', '🎪'
      ]
    },
    objetos: {
      icon: '💎',
      emojis: [
        '⌚', '📱', '📲', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '🕹️',
        '🗜️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥',
        '📽️', '🎞️', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️',
        '🎛️', '🧭', '⏱️', '⏲️', '⏰', '🕰️', '⌛', '⏳', '📡', '🔋',
        '🔌', '💡', '🔦', '🕯️', '🪔', '🧯', '🛢️', '💸', '💵', '💴',
        '💶', '💷', '💰', '💳', '💎', '⚖️', '🧰', '🔧', '🔨', '⚒️',
        '🛠️', '⛏️', '�', '⚙️', '🧱', '⛓️', '🧲', '🔫', '💣', '🧨'
      ]
    },
    símbolos: {
      icon: '❤️',
      emojis: [
        '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
        '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
        '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐',
        '⛎', '♈', '♉', '♊', '♋', '♌', '♍', '♎', '♏', '♐',
        '♑', '♒', '♓', '🆔', '⚛️', '🉑', '☢️', '☣️', '�', '📳',
        '🈶', '🈚', '🈸', '🈺', '🈷️', '✴️', '🆚', '💮', '🉐', '㊙️',
        '㊗️', '🈴', '🈵', '🈹', '🈲', '🅰️', '🅱️', '🆎', '🆑', '🅾️',
        '�', '❌', '⭕', '🛑', '⛔', '📛', '🚫', '💯', '�', '♨️'
      ]
    }
  };

  const [selectedCategory, setSelectedCategory] = useState('recentes');

  // Fechar picker de emojis ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target)) {
        setShowEmojiPicker(false);
      }
    };

    if (showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showEmojiPicker]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!message.trim() && selectedFiles.length === 0) return;

    const messageText = message.trim();
    console.log('DEBUG: Texto da mensagem antes do envio:', messageText);
    console.log('DEBUG: Arquivos selecionados:', selectedFiles.length);

    try {
      // Se há arquivos selecionados, enviar como anexos
      if (selectedFiles.length > 0) {
        setIsUploading(true);
        
        // Criar FormData para upload dos arquivos
        const uploadFormData = new FormData();
        selectedFiles.forEach(file => {
          uploadFormData.append('documents', file);
        });

        // Fazer upload dos arquivos usando o endpoint de documentos compartilhados
        const currentToken = localStorage.getItem('token');
        const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';
        
        const response = await fetch(`${API_URL}/chat/upload`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${currentToken}`
          },
          body: uploadFormData
        });

        if (response.ok) {
          const documents = await response.json();
          
          // A resposta é um array de documentos diretamente
          if (Array.isArray(documents) && documents.length > 0) {
            // Enviar mensagem com anexos
            const attachmentMetadata = {
              attachments: documents.map(doc => ({
                id: doc.id,
                filename: doc.filename,
                originalName: doc.filename,
                size: doc.size,
                mimetype: doc.mimeType, // Backend retorna 'mimeType', frontend usa 'mimetype'
                url: `/documents/${doc.id}/download`, // URL correta para acessar o documento
                path: doc.path
              }))
            };
            const messageText = message.trim();
            
            sendMessage(
              conversationId, 
              messageText, 
              'ATTACHMENT', 
              attachmentMetadata
            );
          } else {
            console.error('Resposta inválida do upload:', documents);
            alert('Erro ao processar arquivos enviados. Tente novamente.');
          }
        } else {
          const errorData = await response.json().catch(() => ({}));
          console.error('Erro na requisição de upload:', response.statusText, errorData);
          alert(`Erro ao fazer upload dos arquivos: ${errorData.message || response.statusText}`);
        }
      } else {
        // Enviar apenas mensagem de texto
        console.log('DEBUG: Enviando apenas texto:', messageText);
        sendMessage(conversationId, messageText);
      }
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      alert('Erro ao fazer upload dos arquivos. Tente novamente.');
      
      // Em caso de erro, enviar mensagem sem anexos se houver texto
      if (messageText) {
        console.log('DEBUG: Enviando texto após erro no upload:', messageText);
        sendMessage(conversationId, messageText);
      }
    } finally {
      setIsUploading(false);
    }

    setMessage('');
    setSelectedFiles([]);
    setShowEmojiPicker(false);
  };

  // Função para anexar arquivos
  const handleFileAttach = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(prev => [...prev, ...files]);
  };

  const removeFile = (index) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Função para adicionar emoji
  const handleEmojiSelect = (emoji) => {
    setMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
  };

  // Função para gravar áudio (simulada)
  const handleVoiceRecord = () => {
    if (!isRecording) {
      setIsRecording(true);
      // Simular gravação por 3 segundos
      setTimeout(() => {
        setIsRecording(false);
        // Aqui você implementaria a funcionalidade real de gravação
        console.log('Gravação de áudio simulada');
      }, 3000);
    } else {
      setIsRecording(false);
    }
  };

  // Formatar tamanho do arquivo
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const buttonVariants = {
    idle: { scale: 1 },
    hover: { 
      scale: 1.05,
      transition: { duration: 0.2 }
    },
    tap: { 
      scale: 0.95,
      transition: { duration: 0.1 }
    }
  };

  return (
    <div className={`bg-gradient-to-r from-cyan-50/50 via-white to-cyan-100/50 dark:from-gray-800/50 dark:via-gray-900 dark:to-gray-800/50 border-t border-cyan-200/50 dark:border-cyan-800/50 backdrop-blur-sm ${compact ? 'py-3' : 'py-4'}`}>
      {/* Preview de arquivos selecionados */}
      <AnimatePresence>
        {selectedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="px-4 pb-3"
          >
            <div className="flex flex-wrap gap-2">
              {selectedFiles.map((file, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center gap-2 bg-cyan-100 dark:bg-cyan-900/30 text-cyan-800 dark:text-cyan-200 px-3 py-2 rounded-lg text-sm"
                >
                  <File size={16} />
                  <span className="truncate max-w-32">{file.name}</span>
                  <span className="text-xs opacity-70">({formatFileSize(file.size)})</span>
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="ml-1 hover:bg-cyan-200 dark:hover:bg-cyan-800 rounded-full p-0.5"
                  >
                    <X size={12} />
                  </button>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <form
        onSubmit={handleSubmit}
        className="flex items-center gap-3 px-4 relative"
      >
        {/* Input file oculto */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.zip,.rar,.7z,.json,.xml"
        />
        {!compact && (
          <motion.button
            type="button"
            onClick={handleFileAttach}
            className="p-2.5 text-cyan-500 hover:text-cyan-600 dark:text-cyan-400 dark:hover:text-cyan-300 rounded-xl hover:bg-cyan-100/50 dark:hover:bg-cyan-900/20 transition-all duration-200 group"
            aria-label="Anexar arquivo"
            variants={buttonVariants}
            initial="idle"
            whileHover="hover"
            whileTap="tap"
          >
            <Paperclip size={compact ? 16 : 18} className="group-hover:scale-110 transition-transform duration-200" />
          </motion.button>
        )}

        <div className="flex-1 relative">
          <div className={`relative rounded-2xl transition-all duration-300 ${
            isFocused 
              ? 'ring-2 ring-cyan-500/50 shadow-lg shadow-cyan-500/20' 
              : 'ring-1 ring-cyan-200/50 dark:ring-cyan-800/50'
          }`}>
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              placeholder="Digite sua mensagem..."
              className="w-full py-3 px-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl focus:outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-300"
            />
            
            {/* Efeito de brilho no foco */}
            {isFocused && (
              <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/10 via-transparent to-cyan-700/10 rounded-2xl pointer-events-none"></div>
            )}
          </div>

          {!compact && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2" ref={emojiPickerRef}>
              <motion.button
                type="button"
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                className={`text-cyan-500 hover:text-cyan-600 dark:text-cyan-400 dark:hover:text-cyan-300 p-1.5 rounded-lg hover:bg-cyan-100/50 dark:hover:bg-cyan-900/20 transition-all duration-200 group ${showEmojiPicker ? 'bg-cyan-100 dark:bg-cyan-900/30' : ''}`}
                aria-label="Inserir emoji"
                variants={buttonVariants}
                initial="idle"
                whileHover="hover"
                whileTap="tap"
              >
                <Smile size={18} className="group-hover:scale-110 transition-transform duration-200" />
              </motion.button>

              {/* Emoji Picker */}
              <AnimatePresence>
                {showEmojiPicker && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.8, y: 10 }}
                    className="absolute bottom-full right-0 mb-2 bg-white dark:bg-gray-800 border border-cyan-200 dark:border-cyan-700 rounded-xl shadow-xl z-50 overflow-hidden"
                    style={{ width: '400px', height: '400px' }}
                  >
                    {/* Header com categorias */}
                      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 flex items-center gap-1 flex-wrap">
                        {Object.entries(emojiCategories).map(([key, category]) => (
                          <button
                            key={key}
                            type="button"
                            onClick={() => setSelectedCategory(key)}
                            className={`p-1.5 rounded-lg text-lg transition-all duration-200 ${
                              selectedCategory === key
                                ? 'bg-cyan-100 dark:bg-cyan-900/50 scale-110'
                                : 'hover:bg-gray-200 dark:hover:bg-gray-600 hover:scale-105'
                            }`}
                            title={category.name}
                          >
                            {category.icon}
                          </button>
                        ))}
                      </div>

                    {/* Grid de emojis */}
                    <div className="p-3 h-full overflow-y-auto">
                      <div className="grid grid-cols-8 gap-1">
                        {emojiCategories[selectedCategory].emojis.map((emoji, index) => (
                          <motion.button
                            key={index}
                            type="button"
                            onClick={() => handleEmojiSelect(emoji)}
                            className="text-xl hover:bg-cyan-100 dark:hover:bg-cyan-900/30 rounded-lg p-2 transition-all duration-200 hover:scale-125"
                            whileHover={{ scale: 1.2 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            {emoji}
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    {/* Footer com busca rápida */}
                    <div className="p-2 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                      <div className="flex items-center gap-1 justify-center">
                        {emojiCategories.recentes.emojis.slice(0, 8).map((emoji, index) => (
                          <button
                            key={index}
                            type="button"
                            onClick={() => handleEmojiSelect(emoji)}
                            className="text-lg hover:bg-cyan-100 dark:hover:bg-cyan-900/30 rounded-lg p-1 transition-all duration-200 hover:scale-110"
                          >
                            {emoji}
                          </button>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {!compact && (
            <motion.button
              type="button"
              onClick={handleVoiceRecord}
              className={`p-2.5 rounded-xl transition-all duration-200 group ${
                isRecording 
                  ? 'text-red-500 bg-red-100 dark:bg-red-900/30 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300' 
                  : 'text-cyan-500 hover:text-cyan-600 dark:text-cyan-400 dark:hover:text-cyan-300 hover:bg-cyan-100/50 dark:hover:bg-cyan-900/20'
              }`}
              aria-label={isRecording ? "Parar gravação" : "Gravar áudio"}
              variants={buttonVariants}
              initial="idle"
              whileHover="hover"
              whileTap="tap"
            >
              <motion.div
                animate={isRecording ? { scale: [1, 1.2, 1] } : { scale: 1 }}
                transition={isRecording ? { repeat: Infinity, duration: 1 } : {}}
              >
                <Mic size={compact ? 16 : 18} className="group-hover:scale-110 transition-transform duration-200" />
              </motion.div>
            </motion.button>
          )}

          <motion.button
            type="submit"
            disabled={(!message.trim() && selectedFiles.length === 0) || isUploading}
            className={`p-3 rounded-2xl transition-all duration-300 group ${
              (message.trim() || selectedFiles.length > 0) && !isUploading
                ? 'bg-gradient-to-r from-cyan-500 via-cyan-600 to-cyan-700 hover:from-cyan-600 hover:via-cyan-700 hover:to-cyan-800 text-white shadow-lg hover:shadow-xl' 
                : 'bg-gray-200 dark:bg-gray-700 text-gray-400 dark:text-gray-500 cursor-not-allowed'
            }`}
            aria-label={isUploading ? "Enviando..." : "Enviar mensagem"}
            variants={buttonVariants}
            initial="idle"
            whileHover={(message.trim() || selectedFiles.length > 0) && !isUploading ? "hover" : "idle"}
            whileTap={(message.trim() || selectedFiles.length > 0) && !isUploading ? "tap" : "idle"}
            style={{
              boxShadow: (message.trim() || selectedFiles.length > 0) && !isUploading
                ? "0 10px 25px -5px rgba(6, 182, 212, 0.4), 0 4px 6px -2px rgba(6, 182, 212, 0.1)"
                : "none"
            }}
          >
            {isUploading ? (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
              />
            ) : (
              <Send 
                size={compact ? 16 : 18} 
                className={`transition-all duration-300 ${
                  (message.trim() || selectedFiles.length > 0) ? 'group-hover:scale-110 group-hover:translate-x-0.5' : ''
                }`}
              />
            )}
          </motion.button>
        </div>
      </form>

      {/* Indicadores de status */}
      <AnimatePresence>
        {isRecording && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="px-4 pb-2"
          >
            <div className="flex items-center gap-2 text-red-500 dark:text-red-400 text-sm">
              <motion.div
                animate={{ opacity: [1, 0.5, 1] }}
                transition={{ repeat: Infinity, duration: 1 }}
                className="w-2 h-2 bg-red-500 rounded-full"
              />
              <span>Gravando áudio...</span>
              <span className="text-xs opacity-70">(Clique no microfone para parar)</span>
            </div>
          </motion.div>
        )}
        
        {isUploading && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="px-4 pb-2"
          >
            <div className="flex items-center gap-2 text-cyan-500 dark:text-cyan-400 text-sm">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                className="w-3 h-3 border-2 border-cyan-500 border-t-transparent rounded-full"
              />
              <span>Fazendo upload dos arquivos...</span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ChatInput;
