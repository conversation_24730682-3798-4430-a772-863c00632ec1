const fs = require('fs');

// Lê o arquivo
let content = fs.readFileSync('high-tide-systems-frontend/src/app/modules/scheduler/introduction/IntroductionPage.js', 'utf8');

// Substituições globais para modo escuro
const replacements = [
  // Containers principais
  {
    from: /bg-white w-full max-w-lg h-4\/5 overflow-hidden rounded-lg shadow-lg relative/g,
    to: 'bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700'
  },
  // Headers
  {
    from: /<div className="p-2 border-b">/g,
    to: '<div className="p-4 border-b border-gray-200 dark:border-gray-700">'
  },
  {
    from: /text-lg font-bold text-slate-800/g,
    to: 'text-lg font-bold text-slate-800 dark:text-white'
  },
  // Conteúdo
  {
    from: /<div className="p-3">/g,
    to: '<div className="p-4 bg-white dark:bg-gray-900">'
  },
  {
    from: /<div className="p-3 space-y-3">/g,
    to: '<div className="p-4 space-y-4 bg-white dark:bg-gray-900">'
  },
  // Labels
  {
    from: /text-xs font-medium text-gray-700/g,
    to: 'text-sm font-medium text-gray-700 dark:text-gray-300'
  },
  // Inputs
  {
    from: /w-full p-2 border rounded text-xs/g,
    to: 'w-full p-2 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400'
  },
  // Botões
  {
    from: /bg-blue-500 text-white rounded text-xs/g,
    to: 'bg-purple-600 dark:bg-purple-700 text-white rounded text-sm hover:bg-purple-700 dark:hover:bg-purple-800 transition-colors'
  },
  {
    from: /bg-gray-300 text-gray-700 rounded text-xs/g,
    to: 'bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors'
  }
];

// Aplica todas as substituições
replacements.forEach(replacement => {
  content = content.replace(replacement.from, replacement.to);
});

// Salva o arquivo
fs.writeFileSync('high-tide-systems-frontend/src/app/modules/scheduler/introduction/IntroductionPage.js', content);

console.log('Atualizações aplicadas com sucesso!');
