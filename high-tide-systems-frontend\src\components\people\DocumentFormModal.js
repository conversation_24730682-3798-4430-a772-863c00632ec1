"use client";

import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog.jsx";
import Button from "@/components/ui/Button.js";
import Input from "@/components/ui/Input.js";
import Label from "@/components/ui/Label.js";
import { ModuleSelect } from "@/components/ui";
import Badge from "@/components/ui/Badge.js";
import { X, Upload, Plus, Users, User, Building, FileText, AlertCircle, Info, Share2, Check } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { companyService } from "@/app/modules/admin/services/companyService";

const DocumentFormModal = ({ isOpen, onClose, document, categories, onSuccess }) => {
  const { user: currentUser } = useAuth();
  const [formData, setFormData] = useState({
    name: "",
    categoryId: "",
    files: [],
    companyId: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [loadingCompanies, setLoadingCompanies] = useState(false);
  const [users, setUsers] = useState([]);
  const [professions, setProfessions] = useState([]);
  const [clients, setClients] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectedProfessions, setSelectedProfessions] = useState([]);
  const [selectedClients, setSelectedClients] = useState([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState("info");
  const fileInputRef = useRef(null);

  // Verificar se o usuário é system_admin
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";

  // Carregar empresas se for system_admin
  useEffect(() => {
    const loadCompanies = async () => {
      if (isSystemAdmin) {
        setLoadingCompanies(true);
        try {
          const companiesData = await companyService.getCompaniesForSelect();
          setCompanies(companiesData);
        } catch (error) {
          console.error("Erro ao carregar empresas:", error);
        } finally {
          setLoadingCompanies(false);
        }
      }
    };

    if (isOpen && isSystemAdmin) {
      loadCompanies();
    }
  }, [isOpen, isSystemAdmin]);

  // Carregar dados iniciais
  useEffect(() => {
    if (isOpen) {
      setErrors({});
      loadUsers();
      loadProfessions();
      loadClients();
      
      if (document) {
        setFormData({
          name: document.filename || "",
          categoryId: document.categoryDocumentId || "",
          files: [],
          companyId: document.companyId || "",
        });
        
        // Carregar permissões existentes do documento
        loadDocumentPermissions();
      } else {
        setFormData({
          name: "",
          categoryId: "",
          files: [],
          companyId: "",
        });
      }
    }
  }, [isOpen, document]);

  const loadUsers = async () => {
    try {
      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || data || []);
      }
    } catch (error) {
      console.error("Erro ao carregar usuários:", error);
    }
  };

  const loadProfessions = async () => {
    try {
      const response = await fetch('/api/professions', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setProfessions(data.professions || data || []);
      }
    } catch (error) {
      console.error("Erro ao carregar profissões:", error);
    }
  };

  const loadClients = async () => {
    try {
      const response = await fetch('/api/clients', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setClients(data.clients || data || []);
      }
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
    }
  };

  const loadDocumentPermissions = async () => {
    if (!document) return;
    
    try {
      const response = await fetch(`/api/documents/${document.id}/permissions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setSelectedUsers(data.users || []);
        setSelectedProfessions(data.professions || []);
        setSelectedClients(data.clients || []);
      } else {
        console.error("Erro ao carregar permissões do documento:", response.status);
      }
    } catch (error) {
      console.error("Erro ao carregar permissões do documento:", error);
    }
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    const maxSize = 50 * 1024 * 1024; // 50MB
    const validFiles = files.filter(file => {
      if (file.size > maxSize) {
        alert(`O arquivo "${file.name}" é muito grande. Máximo: 50MB`);
        return false;
      }
      return true;
    });
    
    setFormData(prev => ({
      ...prev,
      files: [...prev.files, ...validFiles]
    }));
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const maxSize = 50 * 1024 * 1024;
    
    const validFiles = files.filter(file => {
      if (file.size > maxSize) {
        alert(`O arquivo "${file.name}" é muito grande. Máximo: 50MB`);
        return false;
      }
      return true;
    });
    
    if (validFiles.length > 0) {
      setFormData(prev => ({
        ...prev,
        files: [...prev.files, ...validFiles]
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nome do documento é obrigatório";
    }

    if (!document && formData.files.length === 0) {
      newErrors.files = "Selecione pelo menos um arquivo";
    }

    if (isSystemAdmin && !formData.companyId) {
      newErrors.companyId = "Selecione uma empresa para o documento";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      if (document) {
        // Edição de documento existente
        const response = await fetch(`/api/documents/${document.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify({
            name: formData.name.trim(),
            categoryId: formData.categoryId
          })
        });

        if (response.ok) {
          // Salvar permissões
          try {
            await fetch(`/api/documents/${document.id}/permissions`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              },
              body: JSON.stringify({
                users: selectedUsers,
                professions: selectedProfessions,
                clients: selectedClients
              })
            });
          } catch (permissionsError) {
            console.error("Erro ao salvar permissões:", permissionsError);
          }
          
          onSuccess();
          onClose();
        } else {
          const error = await response.json();
          setErrors({ general: error.message || 'Erro ao atualizar documento' });
        }
      } else {
        // Criação de novo documento
        const formDataToSend = new FormData();
        
        formData.files.forEach(file => {
          formDataToSend.append('documents', file);
        });
        
        formDataToSend.append('name', formData.name.trim());
        formDataToSend.append('categoryId', formData.categoryId);
        formDataToSend.append('sharedWithUsers', JSON.stringify(selectedUsers));
        formDataToSend.append('sharedWithProfessions', JSON.stringify(selectedProfessions));
        formDataToSend.append('sharedWithClients', JSON.stringify(selectedClients));
        
        if (isSystemAdmin && formData.companyId) {
          formDataToSend.append('companyId', formData.companyId);
        }

        const response = await fetch('/api/documents/shared/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: formDataToSend
        });

        if (response.ok) {
          onSuccess();
          onClose();
        } else {
          const error = await response.json();
          if (error.errors) {
            setErrors(error.errors);
          } else {
            setErrors({ general: error.message || 'Erro ao salvar documento' });
          }
        }
      }
    } catch (error) {
      console.error("Erro ao salvar documento:", error);
      setErrors({ general: 'Erro ao salvar documento' });
    } finally {
      setIsLoading(false);
    }
  };

  const removeFile = (index) => {
    setFormData(prev => ({
      ...prev,
      files: prev.files.filter((_, i) => i !== index)
    }));
  };

  const removeUser = (userId) => {
    setSelectedUsers(prev => prev.filter(id => id !== userId));
  };

  const removeClient = (clientId) => {
    setSelectedClients(prev => prev.filter(id => id !== clientId));
  };

  const getSelectedUserNames = () => {
    return selectedUsers.map(id => {
      const user = users.find(u => u.id === id);
      return user?.fullName || id;
    });
  };

  const getSelectedClientNames = () => {
    return selectedClients.map(id => {
      const client = clients.find(c => c.id === id);
      return client?.login || id;
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[13000] flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="fixed left-[50%] top-[50%] z-[13050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-orange-300 dark:border-orange-600 bg-background shadow-lg duration-200 rounded-xl max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="pb-4 border-b-2 border-orange-400 dark:border-orange-500 flex-shrink-0 px-6 pt-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg text-white">
              <FileText className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-orange-800 dark:text-white border-l-4 border-orange-400 dark:border-orange-500 pl-3">
                {document ? 'Editar Documento' : 'Novo Documento'}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                {document ? 'Modifique as informações do documento' : 'Adicione um novo documento ao sistema'}
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(90vh-200px)]">
          <div className="flex-1 overflow-y-auto p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
          {/* Erro geral */}
          {errors.general && (
            <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
              <p className="text-sm text-red-700 dark:text-red-300">{errors.general}</p>
            </div>
          )}

          {/* Sistema de Abas */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <div className="flex space-x-8">
              <button
                type="button"
                onClick={() => setActiveTab("info")}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === "info"
                    ? "border-orange-500 text-orange-600 dark:text-orange-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center gap-2">
                  <FileText size={16} />
                  Informações
                </div>
              </button>
              <button
                type="button"
                onClick={() => setActiveTab("sharing")}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === "sharing"
                    ? "border-orange-500 text-orange-600 dark:text-orange-400"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center gap-2">
                  <Share2 size={16} />
                                              Compartilhamento
                            {(selectedUsers.length > 0 || selectedClients.length > 0) && (
                              <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                                {selectedUsers.length + selectedClients.length}
                              </Badge>
                            )}
                </div>
              </button>
            </div>
          </div>

          {/* Conteúdo das Abas */}
          {activeTab === "info" && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Coluna Esquerda - Upload de arquivos */}
              <div className="space-y-6">
                {/* Upload de arquivos */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Upload size={20} className="text-orange-500" />
                    <Label className="text-lg font-semibold">Arquivos</Label>
                  </div>
                  
                  <div 
                    className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 cursor-pointer ${
                      isDragOver 
                        ? 'border-orange-500 dark:border-orange-400 bg-orange-50 dark:bg-orange-900/20 scale-105' 
                        : 'border-orange-300 dark:border-orange-600 hover:border-orange-400 dark:hover:border-orange-500'
                    }`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="mx-auto h-12 w-12 text-orange-400 dark:text-orange-500 mb-3" />
                    <div className="space-y-2">
                      <p className="text-base font-medium text-gray-700 dark:text-gray-300">
                        {isDragOver 
                          ? "Solte os arquivos aqui!" 
                          : formData.files.length > 0 
                            ? `${formData.files.length} arquivo(s) selecionado(s)` 
                            : document 
                              ? "Selecione novos arquivos (opcional)"
                              : "Selecione os arquivos"
                        }
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {isDragOver 
                          ? "Arquivos serão adicionados automaticamente" 
                          : document
                            ? "Arraste e solte novos arquivos aqui ou clique para selecionar (opcional)"
                            : "Arraste e solte arquivos aqui ou clique em qualquer lugar para selecionar"
                        }
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500">
                        Formatos aceitos: PDF, DOC, DOCX, TXT, JPG, JPEG, PNG, GIF (máx. 50MB por arquivo)
                      </p>
                      <div className="mt-2">
                        <input
                          ref={fileInputRef}
                          type="file"
                          id="files"
                          multiple
                          onChange={(e) => {
                            handleFileChange(e);
                            setErrors(prev => ({ ...prev, files: "" }));
                          }}
                          className="hidden"
                          accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"
                        />
                        <label htmlFor="files" className="cursor-pointer">
                          <Button
                            type="button"
                            variant="outline"
                            moduleColor="people"
                            className={errors.files ? "border-red-500" : ""}
                          >
                            <Upload size={16} className="mr-2" />
                            Selecionar Arquivos
                          </Button>
                        </label>
                      </div>
                    </div>
                  </div>
                  
                  {/* Lista de arquivos selecionados */}
                  {formData.files.length > 0 && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Arquivos selecionados ({formData.files.length}):
                      </Label>
                      <div className="max-h-32 overflow-y-auto space-y-2">
                        {formData.files.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800/30">
                            <div className="flex items-center gap-2 flex-1 min-w-0">
                              <div className="w-6 h-6 bg-orange-100 dark:bg-orange-800/30 rounded flex items-center justify-center">
                                <Upload size={12} className="text-orange-600 dark:text-orange-400" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                                  {file.name}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400">
                                  {(file.size / 1024 / 1024).toFixed(2)} MB
                                </p>
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeFile(index)}
                              className="flex-shrink-0 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors duration-200"
                              title="Remover arquivo"
                            >
                              <X size={14} />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Informações do arquivo atual (quando editando) */}
                  {document && formData.files.length === 0 && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Arquivo atual:
                      </Label>
                      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="flex items-center gap-3">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center">
                              <FileText size={16} className="text-gray-600 dark:text-gray-400" />
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                              {document.filename}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {document.size ? `${(document.size / 1024 / 1024).toFixed(2)} MB` : "Tamanho não disponível"}
                            </p>
                          </div>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                          Para substituir o arquivo, selecione um novo arquivo acima.
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {/* Erro de arquivos */}
                  {errors.files && (
                    <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.files}
                    </p>
                  )}
                </div>
              </div>

              {/* Coluna Direita - Informações básicas */}
              <div className="space-y-6">
                {/* Informações básicas */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Plus size={20} className="text-orange-500" />
                    <Label className="text-lg font-semibold">Informações Básicas</Label>
                  </div>
                  
                  {/* Nome do documento */}
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-medium flex items-center gap-2">
                      <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                      Nome do Documento *
                    </Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => {
                        setFormData(prev => ({ ...prev, name: e.target.value }));
                        setErrors(prev => ({ ...prev, name: "" }));
                      }}
                      placeholder="Digite um nome descritivo para o documento"
                      className={`w-full ${errors.name ? "border-red-500 focus:ring-red-500" : ""}`}
                    />
                    {errors.name && (
                      <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.name}
                      </p>
                    )}
                  </div>

                  {/* Categoria */}
                  <div className="space-y-2">
                    <Label htmlFor="category" className="text-sm font-medium flex items-center gap-2">
                      <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                      Categoria
                    </Label>
                    <ModuleSelect
                      moduleColor="people"
                      value={formData.categoryId}
                      onChange={(e) => setFormData(prev => ({ ...prev, categoryId: e.target.value }))}
                      placeholder="Selecione uma categoria"
                    >
                      <option value="">Selecione uma categoria</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </ModuleSelect>
                  </div>

                  {/* Configurações de empresa (apenas para system_admin) */}
                  {isSystemAdmin && (
                    <div className="space-y-4 p-4 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                      <div className="flex items-center gap-2 mb-4">
                        <Building className="h-4 w-4 text-orange-600" />
                        <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Empresa de Destino</h3>
                        <div className="flex items-center gap-1 px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 rounded-full text-xs">
                          <Info className="h-3 w-3" />
                          System Admin
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="company" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Empresa *
                        </Label>
                        <ModuleSelect
                          moduleColor="people"
                          value={formData.companyId}
                          onChange={(e) => setFormData(prev => ({ ...prev, companyId: e.target.value }))}
                          placeholder="Selecione uma empresa"
                          disabled={loadingCompanies}
                        >
                          <option value="">Selecione uma empresa</option>
                          {companies.map((company) => (
                            <option key={company.id} value={company.id}>
                              {company.name}
                            </option>
                          ))}
                        </ModuleSelect>
                        {errors.companyId && (
                          <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            {errors.companyId}
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Aba de Compartilhamento */}
          {activeTab === "sharing" && (
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <Share2 size={20} className="text-orange-500" />
                <Label className="text-lg font-semibold">Compartilhamento</Label>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Compartilhamento por Profissões */}
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/30 h-full">
                    <Label className="text-sm font-medium text-blue-800 dark:text-blue-200 flex items-center gap-2 mb-3">
                      <Users size={16} />
                      Profissões
                    </Label>
                    <p className="text-xs text-blue-600 dark:text-blue-300 mb-3">
                      Selecione uma profissão para incluir todos os usuários automaticamente.
                    </p>
                    
                    <div className="space-y-2 max-h-64 overflow-y-auto">
                      {professions.map(profession => {
                        const professionUsers = users.filter(user => user.professionId === profession.id);
                        const isSelected = selectedProfessions.includes(profession.id);
                        
                        return (
                          <div key={profession.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <button
                                  type="button"
                                  onClick={() => {
                                    if (isSelected) {
                                      setSelectedProfessions(prev => prev.filter(id => id !== profession.id));
                                      setSelectedUsers(prev => prev.filter(id => 
                                        !professionUsers.some(u => u.id === id)
                                      ));
                                    } else {
                                      setSelectedProfessions(prev => [...prev, profession.id]);
                                      const newUsers = [...selectedUsers, ...professionUsers.map(u => u.id)];
                                      setSelectedUsers([...new Set(newUsers)]);
                                    }
                                  }}
                                  className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-colors ${
                                    isSelected 
                                      ? 'bg-blue-500 border-blue-500 text-white' 
                                      : 'border-gray-300 dark:border-gray-600'
                                  }`}
                                >
                                  {isSelected && <Check size={12} />}
                                </button>
                                <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                  {profession.name}
                                </span>
                              </div>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {professionUsers.length}
                              </span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>

                {/* Compartilhamento por Usuários Individuais */}
                <div className="space-y-4">
                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800/30 h-full">
                    <Label className="text-sm font-medium text-green-800 dark:text-green-200 flex items-center gap-2 mb-3">
                      <User size={16} />
                      Usuários Individuais
                    </Label>
                    <p className="text-xs text-green-600 dark:text-green-300 mb-3">
                      Selecione usuários específicos para compartilhar o documento.
                    </p>
                    
                    <div className="space-y-2">
                      <input
                        type="text"
                        placeholder="Buscar usuários..."
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                        onChange={(e) => {
                          // Implementar busca de usuários
                        }}
                      />
                      
                      <div className="max-h-48 overflow-y-auto space-y-1">
                        {users.map(user => {
                          const isSelected = selectedUsers.includes(user.id);
                          return (
                            <div key={user.id} className="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-600 rounded-lg">
                              <div className="flex items-center gap-2">
                                <button
                                  type="button"
                                  onClick={() => {
                                    if (isSelected) {
                                      removeUser(user.id);
                                    } else {
                                      setSelectedUsers(prev => [...prev, user.id]);
                                    }
                                  }}
                                  className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-colors ${
                                    isSelected 
                                      ? 'bg-green-500 border-green-500 text-white' 
                                      : 'border-gray-300 dark:border-gray-600'
                                  }`}
                                >
                                  {isSelected && <Check size={12} />}
                                </button>
                                <span className="text-sm text-gray-900 dark:text-gray-100 truncate">
                                  {user.fullName}
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Compartilhamento por Clientes */}
                <div className="space-y-4">
                  <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800/30 h-full">
                    <Label className="text-sm font-medium text-purple-800 dark:text-purple-200 flex items-center gap-2 mb-3">
                      <Building size={16} />
                      Clientes
                    </Label>
                    <p className="text-xs text-purple-600 dark:text-purple-300 mb-3">
                      Selecione clientes específicos para compartilhar o documento.
                    </p>
                    
                    <div className="space-y-2">
                      <input
                        type="text"
                        placeholder="Buscar clientes..."
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                        onChange={(e) => {
                          // Implementar busca de clientes
                        }}
                      />
                      
                      <div className="max-h-48 overflow-y-auto space-y-1">
                        {clients.map(client => {
                          const isSelected = selectedClients.includes(client.id);
                          return (
                            <div key={client.id} className="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-600 rounded-lg">
                              <div className="flex items-center gap-2">
                                <button
                                  type="button"
                                  onClick={() => {
                                    if (isSelected) {
                                      removeClient(client.id);
                                    } else {
                                      setSelectedClients(prev => [...prev, client.id]);
                                    }
                                  }}
                                  className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-colors ${
                                    isSelected 
                                      ? 'bg-purple-500 border-purple-500 text-white' 
                                      : 'border-gray-300 dark:border-gray-600'
                                  }`}
                                >
                                  {isSelected && <Check size={12} />}
                                </button>
                                <span className="text-sm text-gray-900 dark:text-gray-100 truncate">
                                  {client.login}
                                </span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Resumo do Compartilhamento */}
              {(selectedUsers.length > 0 || selectedProfessions.length > 0 || selectedClients.length > 0) && (
                <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                  <div className="flex items-center gap-2 mb-3">
                    <Info className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Resumo do Compartilhamento</span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-gray-600 dark:text-gray-400">
                    {selectedProfessions.length > 0 && (
                      <div>
                        <span className="font-medium text-blue-700 dark:text-blue-300">
                          Profissões ({selectedProfessions.length}):
                        </span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {selectedProfessions.map(id => {
                            const profession = professions.find(p => p.id === id);
                            return (
                              <Badge key={`profession-${id}`} variant="secondary" className="text-xs">
                                {profession?.name || id}
                              </Badge>
                            );
                          })}
                        </div>
                      </div>
                    )}
                    
                    {selectedUsers.length > 0 && (
                      <div>
                        <span className="font-medium text-green-700 dark:text-green-300">
                          Usuários ({selectedUsers.length}):
                        </span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {getSelectedUserNames().slice(0, 3).map((name, index) => (
                            <Badge key={`user-${index}`} variant="secondary" className="text-xs">
                              <User size={10} className="mr-1" />
                              {name}
                            </Badge>
                          ))}
                          {selectedUsers.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{selectedUsers.length - 3} mais
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                    
                    {selectedClients.length > 0 && (
                      <div>
                        <span className="font-medium text-purple-700 dark:text-purple-300">
                          Clientes ({selectedClients.length}):
                        </span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {getSelectedClientNames().slice(0, 3).map((name, index) => (
                            <Badge key={`client-${index}`} variant="secondary" className="text-xs">
                              <Building size={10} className="mr-1" />
                              {name}
                            </Badge>
                          ))}
                          {selectedClients.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{selectedClients.length - 3} mais
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Resumo antes dos botões */}
          <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-2 mb-2">
              <Info className="h-4 w-4 text-gray-600 dark:text-gray-400" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Resumo do Documento</span>
            </div>
            <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
              <p><strong>Nome:</strong> {formData.name || "Não definido"}</p>
              <p><strong>Categoria:</strong> {
                formData.categoryId 
                  ? categories.find(c => c.id === formData.categoryId)?.name || "Selecionada"
                  : "Não selecionada"
              }</p>
              <p><strong>Arquivos:</strong> {
                formData.files.length > 0 
                  ? `${formData.files.length} arquivo(s) selecionado(s)`
                  : document 
                    ? `Arquivo atual: ${document.filename}`
                    : "Nenhum arquivo selecionado"
              }</p>
              {isSystemAdmin && (
                <p><strong>Empresa:</strong> {
                  formData.companyId
                    ? companies.find(c => c.id === formData.companyId)?.name || "Selecionada"
                    : "Não selecionada"
                }</p>
              )}
              {!isSystemAdmin && (
                <p><strong>Empresa:</strong> {currentUser?.company?.name || "Empresa atual"}</p>
              )}
              <p><strong>Compartilhamentos:</strong> {
                selectedUsers.length + selectedClients.length
              } {selectedUsers.length + selectedClients.length === 1 ? "destinatário" : "destinatários"}</p>
            </div>
          </div>

            </form>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 border-t-2 border-orange-400 dark:border-orange-500 pt-4 flex-shrink-0 px-6 pb-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
              disabled={isLoading}
            >
              Cancelar
            </button>
            <button
              type="submit"
              form="document-form"
              onClick={handleSubmit}
              className="px-4 py-2 bg-orange-500 dark:bg-orange-600 text-white rounded-lg hover:bg-orange-600 dark:hover:bg-orange-700 transition-colors flex items-center gap-2"
              disabled={isLoading || (!document && formData.files.length === 0) || !formData.name.trim()}
            >
              {isLoading ? (
                <>
                  <Upload size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  <FileText size={16} />
                  <span>{document ? "Atualizar" : "Salvar"}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentFormModal; 