import React, { useState, useEffect } from 'react';
import EventTooltip from './EventTooltip';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Grid, Clock, Eye, Edit3, Download } from 'lucide-react';
import { getEventBackgroundColor, getEventTextColor, getEventStatusLabel, getEventHoverColor } from '../utils/eventColors';
import '../styles/vibrant-calendar.css';
import MultipleEventsModal from './MultipleEventsModal';

/**
 * CustomWeekGrid - Componente de grade semanal customizada
 * 
 * Funcionalidades:
 * - Suporte a eventos que ocupam múltiplas horas (ex: 13h-16h)
 * - Eventos são distribuídos por todos os slots que ocupam
 * - Primeiro slot mostra informações completas, slots subsequentes mostram "(continua...)"
 * - Estilos visuais diferenciados para eventos em série
 * - Indicadores visuais para eventos que continuam
 * 
 * Exemplo de evento multi-hora:
 * {
 *   id: 1,
 *   startDate: "2024-01-15T13:00:00Z",
 *   endDate: "2024-01-15T16:00:00Z",
 *   title: "Consulta Longa",
 *   // ... outras propriedades
 * }
 * 
 * Este evento aparecerá nos slots 13h, 14h e 15h com estilos apropriados.
 */

const diasSemana = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

function getHoursArray(start, end) {
  const arr = [];
  for (let h = start; h <= end; h++) arr.push(h);
  return arr;
}

function addDays(date, days) {
  const d = new Date(date);
  d.setDate(d.getDate() + days);
  return d;
}

function formatHour(hour) {
  return `${hour.toString().padStart(2, '0')}:00`;
}

function formatWeekRange(startDate) {
  const endDate = addDays(startDate, 6);
  const options = { day: '2-digit', month: 'long', year: 'numeric' };
  const startStr = startDate.toLocaleDateString('pt-BR', { day: '2-digit' });
  const endStr = endDate.toLocaleDateString('pt-BR', options);
  return `${startStr} - ${endStr}`;
}

// Função para garantir que o início da semana seja sempre segunda-feira
function getStartOfWeek(date) {
  const d = new Date(date);
  const day = d.getDay();
  // Se domingo (0), volta 6 dias; senão, volta (day-1)
  const diff = day === 0 ? -6 : 1 - day;
  d.setDate(d.getDate() + diff);
  d.setHours(0, 0, 0, 0);
  return d;
}

// Função para criar tooltip detalhado do evento
function createEventTooltip(event) {
  const personName = event.extendedProps?.personfullName || event.title || 'Paciente não informado';
  const providerName = event.extendedProps?.providerfullName || 'Profissional não informado';
  const serviceType = event.extendedProps?.serviceTypefullName || event.extendedProps?.serviceType?.name || 'Serviço não informado';
  const startTime = new Date(event.startDate || event.start).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
  const endTime = new Date(event.endDate || event.end).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
  
  return (
    <div className="event-tooltip-content">
      <div className="tooltip-section">
        <strong>Paciente:</strong> {personName}
      </div>
      <div className="tooltip-section">
        <strong>Profissional:</strong> {providerName}
      </div>
      <div className="tooltip-section">
        <strong>Serviço:</strong> {serviceType}
      </div>
      <div className="tooltip-section">
        <strong>Horário:</strong> {startTime} - {endTime}
      </div>
    </div>
  );
}

export default function CustomWeekGrid({ events, startHour, endHour, weekStartDate, onNavigate, onViewChange, onEventClick, isDarkMode = false, onCreateAppointment, preferences, allowedHours, onExport, isExporting, isClient = false }) {
  const [currentWeekStart, setCurrentWeekStart] = useState(new Date(weekStartDate));
  const [modalEvents, setModalEvents] = useState(null);
  
  // Log para debug dos eventos
  useEffect(() => {
    console.log('[WEEK-GRID] 📊 Eventos recebidos:', events.length);
    if (events.length > 0) {
      const eventDates = [...new Set(events.map(e => new Date(e.start).toLocaleDateString()))];
      console.log('[WEEK-GRID] 📅 Datas dos eventos:', eventDates.slice(0, 5), eventDates.length > 5 ? `... (+${eventDates.length - 5})` : '');
    }
  }, [events]);
  
  // Atualizar a semana atual quando weekStartDate mudar
  useEffect(() => {
    const newWeekStart = new Date(weekStartDate);
    if (!isNaN(newWeekStart.getTime())) {
      setCurrentWeekStart(newWeekStart);
    }
  }, [weekStartDate]);
  
  // Filtrar horários baseado nas preferências
  const filteredHours = getHoursArray(startHour, endHour).filter(hour => {
    return allowedHours && allowedHours[hour] !== false;
  });
  
  // Filtrar dias da semana baseado nas preferências
  const selectedWeekDays = preferences?.selectedWeekDays || [1, 2, 3, 4, 5];
  const normalizedWeekStart = getStartOfWeek(currentWeekStart);
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(normalizedWeekStart, i));

  // Função para calcular todas as horas que um evento ocupa
  function getEventHours(event) {
    const start = new Date(event.startDate || event.start);
    const end = new Date(event.endDate || event.end);
    const hours = [];
    
    // Pegar a hora de início
    let currentHour = start.getHours();
    
    // Pegar a hora de fim (incluir a hora se o evento termina após o início da hora)
    let endHour = end.getHours();
    if (end.getMinutes() > 0) {
      endHour = end.getHours();
    }
    
    // Gerar todas as horas entre início e fim
    while (currentHour <= endHour) {
      hours.push(currentHour);
      currentHour++;
    }
    
    return hours;
  }

  // Função para determinar se um evento é o primeiro slot de uma série
  function isFirstSlot(event, currentHour) {
    const start = new Date(event.startDate || event.start);
    return currentHour === start.getHours();
  }

  // Função para determinar se um evento é o último slot de uma série
  function isLastSlot(event, currentHour) {
    const end = new Date(event.endDate || event.end);
    const eventEndHour = end.getMinutes() > 0 ? end.getHours() : end.getHours();
    return currentHour === eventEndHour;
  }

  // Agrupar eventos por dia e hora, incluindo eventos que ocupam múltiplas horas
  const eventsByDayHour = {};
  events.forEach(ev => {
    const start = new Date(ev.startDate || ev.start);
    const dayKey = start.toLocaleDateString('en-CA'); // formato yyyy-mm-dd
    const eventHours = getEventHours(ev);
    
    // Para eventos multi-hora, colocar apenas no primeiro slot com informações de extensão
    if (eventHours.length > 1) {
      const firstHour = eventHours[0];
      const key = `${dayKey}-${firstHour}`;
      if (!eventsByDayHour[key]) eventsByDayHour[key] = [];
      
      const eventWithExtension = {
        ...ev,
        _isMultiHour: true,
        _totalSlots: eventHours.length,
        _startHour: firstHour,
        _endHour: eventHours[eventHours.length - 1]
      };
      
      eventsByDayHour[key].push(eventWithExtension);
    } else {
      // Eventos de uma hora apenas
      const hour = eventHours[0];
      const key = `${dayKey}-${hour}`;
      if (!eventsByDayHour[key]) eventsByDayHour[key] = [];
      
      const singleHourEvent = {
        ...ev,
        _isMultiHour: false,
        _totalSlots: 1
      };
      
      eventsByDayHour[key].push(singleHourEvent);
    }
  });

  const hours = getHoursArray(startHour, endHour);

  return (
    <div className="modern-calendar-container p-4">
      {/* Toolbar customizada */}
      <div className="modern-calendar-toolbar mb-4">
        <div className="toolbar-left">
          <button onClick={() => onNavigate && onNavigate('TODAY')} className="today-btn">
            <CalendarIcon size={16} />
            <span>Hoje</span>
          </button>
          <div className="navigation-buttons">
            <button onClick={() => onNavigate && onNavigate('PREV')} className="nav-btn">
              <ChevronLeft size={18} />
            </button>
            <button onClick={() => onNavigate && onNavigate('NEXT')} className="nav-btn">
              <ChevronRight size={18} />
            </button>
          </div>
          <div className="current-period">
            <h2 className="period-title">Semana</h2>
            <p className="period-date">{formatWeekRange(currentWeekStart)}</p>
          </div>
        </div>

        <div className="toolbar-right">
          <div className="view-buttons">
            <button
              className="view-btn"
              onClick={() => onViewChange && onViewChange('month')}
            >
              <Grid size={16} />
              <span>Mês</span>
            </button>
            <button
              className="view-btn active"
              onClick={() => onViewChange && onViewChange('week')}
            >
              <Clock size={16} />
              <span>Semana</span>
            </button>
            <button
              className="view-btn"
              onClick={() => onViewChange && onViewChange('day')}
            >
              <Eye size={16} />
              <span>Dia</span>
            </button>
          </div>
          
          {/* Botão de exportar */}
          {onExport && (
            <div className="export-section ml-4">
              <button
                onClick={() => onExport('image')}
                disabled={isExporting || events.length === 0}
                className="export-btn"
                title="Exportar calendário como imagem"
              >
                <Download size={16} />
                <span>Exportar</span>
              </button>
            </div>
          )}
        </div>
      </div>
      <div className="grid grid-cols-8 gap-0 border border-purple-200 dark:border-[#374151] rounded-lg overflow-hidden">
        {/* Header */}
        <div className="bg-purple-100 dark:bg-[#1a1d2a] text-purple-800 dark:text-[#a78bfa] flex items-center justify-center font-bold p-2 border-b border-r border-purple-200 dark:border-[#374151]">Horário</div>
        {weekDays.map((date, i) => (
          <div
            key={i}
            className={`bg-purple-100 dark:bg-[#1a1d2a] text-purple-800 dark:text-[#a78bfa] font-bold p-2 border-b border-r border-purple-200 dark:border-[#374151] text-center ${!selectedWeekDays.includes(date.getDay()) ? 'opacity-50 text-gray-500 dark:text-[#6b7280] cursor-not-allowed' : ''}`}
          >
            {diasSemana[date.getDay()]}<br/>{date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })}
          </div>
        ))}
        {/* Corpo do grid */}
        {hours.map(hour => (
          <React.Fragment key={hour}>
            {/* Coluna de horários */}
            <div className={`font-bold p-2 border-b border-r flex items-center justify-center border-purple-200 dark:border-[#374151] text-right ${
              allowedHours && allowedHours[hour] === false 
                ? 'bg-gray-100 dark:bg-[#1a1d2a] text-gray-400 dark:text-[#6b7280] opacity-50' 
                : 'bg-white dark:bg-[#23273a] text-purple-700 dark:text-[#a78bfa]'
            }`}>
              {formatHour(hour)}
            </div>
            {weekDays.map((date, i) => {
              const dayKey = date.toLocaleDateString('en-CA');
              const slotStart = new Date(date);
              slotStart.setHours(hour, 0, 0, 0);
              const slotEnd = new Date(date);
              slotEnd.setHours(hour + 1, 0, 0, 0);
              const slotHeightPx = 48;
              // Eventos que COMEÇAM neste slot
              const slotEvents = events.filter(ev => {
                const start = new Date(ev.startDate || ev.start);
                return (
                  start.getDay() === date.getDay() &&
                  start.getHours() === hour
                );
              });
              // Badge de eventos extras
              const showEvents = slotEvents.slice(0, 3);
              const extraCount = slotEvents.length - 3;
              return (
                <div
                  key={i}
                  className={`relative min-h-[48px] border-b border-r border-purple-200 dark:border-[#374151] p-1 ${
                    !selectedWeekDays.includes(date.getDay()) || (allowedHours && allowedHours[hour] === false)
                      ? 'bg-gray-100 dark:bg-[#1a1d2a] opacity-50 cursor-not-allowed'
                      : 'bg-white dark:bg-[#23273a] cursor-pointer hover:bg-purple-50 dark:hover:bg-gray-800'
                  }`}
                  style={{ 
                    cursor: !selectedWeekDays.includes(date.getDay()) || (allowedHours && allowedHours[hour] === false) ? 'not-allowed' : 'pointer',
                    position: 'relative',
                    minHeight: `${slotHeightPx}px`,
                    height: `${slotHeightPx}px`,
                    overflow: 'visible'
                  }}
                  onClick={() => {
                    if (!selectedWeekDays.includes(date.getDay()) || (allowedHours && allowedHours[hour] === false)) return;
                    if (isClient) return;
                    const start = new Date(date);
                    start.setHours(hour, 0, 0, 0);
                    const end = new Date(start);
                    end.setHours(hour + 1, 0, 0, 0);
                    if (typeof onCreateAppointment === 'function') {
                      onCreateAppointment({ start, end, dia: dayKey, hora: hour });
                    }
                  }}
                >
                  {showEvents.map((ev, idx) => {
                    const start = new Date(ev.startDate || ev.start);
                    const end = new Date(ev.endDate || ev.end);
                    // Cálculo do top e height em px
                    const minutesFromSlotStart = start.getMinutes();
                    const eventDurationMinutes = Math.max(5, (end - start) / 60000); // mínimo 5min
                    const topPx = (minutesFromSlotStart / 60) * slotHeightPx;
                    const heightPx = (eventDurationMinutes / 60) * slotHeightPx;
                    // Empilhamento horizontal - apenas entre eventos visíveis
                    const visibleEvents = showEvents.length;
                    const eventWidth = 100 / visibleEvents;
                    const eventLeft = idx * eventWidth;
                    return (
                                                                      <EventTooltip key={ev.id} content={createEventTooltip(ev)} useChildRef={true}>
                          <div
                            className={`absolute calendar-event-container rounded px-2 py-1 font-bold text-xs shadow-md cursor-pointer transition-all duration-200`}
                            style={{
                              background: getEventBackgroundColor(ev.extendedProps?.status || ev.status || 'PENDING', isDarkMode),
                              color: getEventTextColor(ev.extendedProps?.status || ev.status || 'PENDING', isDarkMode),
                              border: `2px solid ${getEventHoverColor(ev.extendedProps?.status || ev.status || 'PENDING', isDarkMode)}`,
                              top: `${topPx}px`,
                              height: `${heightPx}px`,
                              left: `${eventLeft}%`,
                              width: `${eventWidth}%`,
                              zIndex: 10 + idx,
                            }}
                            onMouseEnter={(e) => {
                              e.target.style.background = getEventHoverColor(ev.extendedProps?.status || ev.status || 'PENDING', isDarkMode);
                              e.target.style.borderColor = getEventBackgroundColor(ev.extendedProps?.status || ev.status || 'PENDING', isDarkMode);
                              e.target.style.transform = 'scale(1.02)';
                            }}
                            onMouseLeave={(e) => {
                              e.target.style.background = getEventBackgroundColor(ev.extendedProps?.status || ev.status || 'PENDING', isDarkMode);
                              e.target.style.borderColor = getEventHoverColor(ev.extendedProps?.status || ev.status || 'PENDING', isDarkMode);
                              e.target.style.transform = 'scale(1)';
                            }}
                            onClick={e => {
                              e.stopPropagation();
                              if (isClient) return;
                              onEventClick && onEventClick({ event: ev });
                            }}
                          >
                            <span className="calendar-event-text" style={{ fontWeight: 'normal' }}>
  <span style={{ fontWeight: 'bold', fontSize: '1em' }}>{ev.extendedProps?.personfullName || 'N/I'}</span><br />
  <span style={{ fontWeight: 'normal', fontSize: '0.85em' }}>{ev.extendedProps?.providerfullName || 'N/I'}</span><br />
  <span style={{ fontWeight: 'normal', fontSize: '0.85em' }}>{(ev.extendedProps?.serviceTypefullName || ev.extendedProps?.serviceType?.name || 'N/I')}</span><br />
  <span style={{ fontWeight: 'normal', fontSize: '0.85em' }}>{(new Date(ev.startDate || ev.start)).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}</span><br />
  <span style={{ fontWeight: 'normal', fontSize: '0.85em' }}>{(new Date(ev.endDate || ev.end)).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}</span>
</span>
                          </div>
                        </EventTooltip>
                    );
                  })}
                  {extraCount > 0 && (
                    <button
                      className="event-more-badge absolute top-1 right-2 z-20 bg-purple-600 dark:bg-[#7c3aed] text-white font-bold text-xs px-2 py-1 rounded-full shadow-md hover:bg-purple-700 dark:hover:bg-[#a78bfa] transition"
                      style={{ fontSize: '11px', padding: '2px 8px' }}
                      onClick={e => { e.stopPropagation(); setModalEvents(slotEvents); }}
                    >
                      +{extraCount}
                    </button>
                  )}
                </div>
              );
            })}
          </React.Fragment>
        ))}
      </div>
      {/* Modal de eventos extras */}
      <MultipleEventsModal
        isOpen={!!modalEvents}
        onClose={() => setModalEvents(null)}
        events={modalEvents || []}
        title="Agendamentos do horário"
        onEventClick={onEventClick}
        isDarkMode={isDarkMode}
      />
    </div>
  );
} 