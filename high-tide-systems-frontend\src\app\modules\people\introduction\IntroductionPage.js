"use client";

import React, { useState, useEffect } from "react";
import {
  Users,
  UserPlus,
  CreditCard,
  Shield,
  Info,
  Building,
  Play,
  Pause,
  ArrowRight,
  BarChart4,
  PieChart,
  LineChart,
  Activity,
  Mail,
  Phone,
  FileText,
  User,
  Calendar,
  ChevronRight,
  Plus,
  Filter,
  Edit,
  Trash,
  Power,
  RefreshCw,
  XCircle,
  Send,
  Download,
  SlidersHorizontal,
  Eye,
  CheckCircle
} from "lucide-react";
import { ModuleHeader } from "@/components/ui";

const IntroductionPage = () => {
  const [selectedTutorial, setSelectedTutorial] = useState(null);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [totalSlides, setTotalSlides] = useState(8);

  // Configuração dos tutoriais disponíveis
  const tutorials = [
    {
      id: 'clients',
      title: 'Gerenciamento de Clientes',
      description: 'Aprenda a cadastrar, editar e gerenciar clientes no sistema',
      icon: <UserPlus size={20} />,
      color: 'from-orange-600 to-orange-500',
      darkColor: 'from-orange-700 to-orange-600',
      slides: 8,
      features: [
        'Cadastrar clientes',
        'Editar informações',
        'Gerenciar contatos',
        'Vincular pacientes',
        'Controlar status'
      ]
    },
    {
      id: 'patients',
      title: 'Cadastro de Pacientes',
      description: 'Gerencie o cadastro completo de pacientes e suas informações',
      icon: <Users size={20} />,
      color: 'from-blue-600 to-blue-500',
      darkColor: 'from-blue-700 to-blue-600',
      slides: 8,
      features: [
        'Cadastrar pacientes',
        'Informações médicas',
        'Documentos pessoais',
        'Histórico familiar',
        'Relacionar com clientes'
      ]
    },
    {
      id: 'insurances',
      title: 'Convênios e Seguros',
      description: 'Configure convênios, planos de saúde e limites autorizados',
      icon: <CreditCard size={20} />,
      color: 'from-green-600 to-green-500',
      darkColor: 'from-green-700 to-green-600',
      slides: 8,
      features: [
        'Cadastrar convênios',
        'Configurar planos',
        'Definir limites',
        'Controlar autorizações',
        'Gerenciar validades'
      ]
    },
    {
      id: 'documents',
      title: 'Documentos e Arquivos',
      description: 'Organize e gerencie documentos de clientes e pacientes',
      icon: <FileText size={20} />,
      color: 'from-purple-600 to-purple-500',
      darkColor: 'from-purple-700 to-purple-600',
      slides: 8,
      features: [
        'Upload de documentos',
        'Organizar por categoria',
        'Controle de acesso',
        'Histórico de versões',
        'Download seguro'
      ]
    }
  ];

  // Auto-advance slides
  useEffect(() => {
    if (!isVideoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % totalSlides);
    }, 7000);

    return () => clearInterval(interval);
  }, [isVideoPlaying, totalSlides]);

  useEffect(() => {
    if (selectedTutorial) {
      setTotalSlides(selectedTutorial.slides);
      setCurrentSlide(0);
    }
  }, [selectedTutorial]);

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Info size={24} className="mr-2 text-orange-600 dark:text-orange-400" />
          Introdução
        </h1>
      </div>

      {/* Main content */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-people-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-orange-600 to-orange-400 dark:from-orange-700 dark:to-orange-600 px-6 py-4">
          <div className="flex items-center">
            <Users className="mr-3 text-white" size={24} aria-hidden="true" />
            <h2 className="text-xl font-bold text-white">Módulo de Pessoas</h2>
          </div>
        </div>

        {/* Introduction text and tutorials */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div>
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                Bem-vindo ao Módulo de Pessoas do High Tide Systems. Este módulo é o centro de gerenciamento de clientes, pacientes e convênios,
                permitindo organizar todas as informações de forma eficiente.
                Aqui você encontrará todas as ferramentas necessárias para gerenciar pessoas e seus relacionamentos.
              </p>

            </div>

            {/* Lista de Tutoriais */}
            <div className="bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-lg">
              <div className="bg-gradient-to-r from-orange-600 to-orange-500 dark:from-orange-700 dark:to-orange-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-white flex items-center">
                    <Play className="mr-2" size={18} />
                    {selectedTutorial ? `Tutorial: ${selectedTutorial.title}` : 'Tutoriais Disponíveis'}
                  </h3>
                  {selectedTutorial && (
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => {
                          setSelectedTutorial(null);
                          setIsVideoPlaying(false);
                        }}
                        className="text-white hover:text-orange-200 transition-colors p-1 rounded"
                        title="Voltar à lista"
                      >
                        <ArrowRight size={18} className="rotate-180" />
                      </button>
                      <button
                        onClick={() => setIsVideoPlaying(!isVideoPlaying)}
                        className="text-white hover:text-orange-200 transition-colors p-1 rounded"
                        aria-label={isVideoPlaying ? "Pausar tutorial" : "Iniciar tutorial"}
                      >
                        {isVideoPlaying ? <Pause size={18} /> : <Play size={18} />}
                      </button>
                    </div>
                  )}
                </div>
              </div>
              <div className="relative aspect-video bg-gradient-to-br from-orange-900 to-orange-800 overflow-hidden">
                {!selectedTutorial ? (
                  // Lista de tutoriais
                  <div className="p-6 h-full overflow-y-auto">
                    <div className="grid grid-cols-1 gap-4 h-full">
                      {tutorials.map((tutorial) => (
                        <div
                          key={tutorial.id}
                          onClick={() => setSelectedTutorial(tutorial)}
                          className="bg-white/10 backdrop-blur-sm rounded-lg p-4 cursor-pointer hover:bg-white/20 transition-all duration-200 border border-white/20 hover:border-white/30"
                        >
                          <div className="flex items-start gap-3">
                            <div className={`p-2 rounded-lg bg-gradient-to-r ${tutorial.color} dark:${tutorial.darkColor}`}>
                              {tutorial.icon}
                            </div>
                            <div className="flex-1">
                              <h4 className="text-white font-semibold text-sm mb-1">{tutorial.title}</h4>
                              <p className="text-orange-200 text-xs mb-3">{tutorial.description}</p>
                              <div className="grid grid-cols-2 gap-1">
                                {tutorial.features.map((feature, index) => (
                                  <div key={index} className="flex items-center gap-1 text-xs text-green-300">
                                    <div className="w-1 h-1 rounded-full bg-green-400"></div>
                                    <span>{feature}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                            <div className="text-white/60">
                              <ChevronRight size={16} />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : isVideoPlaying ? (
                  <div className="w-full h-full relative">
                    {/* Slideshow navigation */}
                    <div className="absolute bottom-0 left-0 right-0 bg-black/50 p-3 z-20">
                      <div className="flex items-center justify-center gap-2">
                        {Array.from({length: totalSlides}).map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentSlide(index)}
                            className={`w-2 h-2 rounded-full transition-all duration-300 ${
                              currentSlide === index ? 'bg-orange-500 w-6' : 'bg-gray-400 hover:bg-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>

                    {/* Conteúdo do tutorial */}
                    <div className="w-full h-full flex items-center justify-center relative">
                      <div className="tutorial-content w-full h-full relative overflow-hidden">

                        {/* Tutorial de Clientes */}
                        {selectedTutorial?.id === 'clients' && (
                          <>
                      {/* Slide 1: Página Real de Clientes */}
                      <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                        <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                          <div className="p-2 border-b">
                            <div className="flex justify-between items-center mb-2">
                              <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                <UserPlus size={20} className="mr-2 text-slate-600" />
                                Clientes
                              </h1>
                              <div className="flex items-center gap-1">
                                <button className="flex items-center gap-1 px-2 py-1 bg-orange-500 text-white rounded text-xs">
                                  <Plus size={12} />
                                  Novo
                                </button>
                              </div>
                            </div>
                          </div>
                          <div className="bg-white p-2 border-b">
                            <div className="flex items-center gap-2 text-xs">
                              <input placeholder="Buscar clientes..." className="flex-1 p-1 border rounded text-xs" />
                              <button className="px-2 py-1 bg-gray-100 text-gray-700 rounded">
                                <Filter size={12} />
                              </button>
                            </div>
                          </div>
                          <div className="p-2">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between p-2 bg-gray-50 rounded border">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-orange-200 rounded-full flex items-center justify-center">
                                    <UserPlus size={14} className="text-orange-600" />
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">Maria Silva</div>
                                    <div className="text-xs text-gray-500"><EMAIL></div>
                                  </div>
                                </div>
                                <div className="flex gap-1">
                                  <button className="p-1 text-blue-600"><Edit size={12} /></button>
                                  <button className="p-1 text-red-600"><Trash size={12} /></button>
                                </div>
                              </div>
                              <div className="flex items-center justify-between p-2 bg-gray-50 rounded border">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-orange-200 rounded-full flex items-center justify-center">
                                    <UserPlus size={14} className="text-orange-600" />
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">João Santos</div>
                                    <div className="text-xs text-gray-500"><EMAIL></div>
                                  </div>
                                </div>
                                <div className="flex gap-1">
                                  <button className="p-1 text-blue-600"><Edit size={12} /></button>
                                  <button className="p-1 text-red-600"><Trash size={12} /></button>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 bg-orange-600 text-white p-2 rounded text-xs">
                            <strong>Clientes:</strong> Gerencie o cadastro de clientes do sistema
                          </div>
                        </div>
                      </div>

                      {/* Slide 2: Criando Novo Cliente */}
                      <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                        <div className="bg-white w-full max-w-md h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                          <div className="p-3 border-b bg-orange-50">
                            <h2 className="text-sm font-bold text-slate-800 flex items-center">
                              <Plus size={16} className="mr-2 text-orange-600" />
                              Novo Cliente
                            </h2>
                          </div>
                          <div className="p-3 space-y-3">
                            <div>
                              <label className="text-xs font-medium text-gray-700">Nome Completo</label>
                              <div className="mt-1 p-2 border rounded text-xs bg-gray-50">Ana Paula Costa</div>
                            </div>
                            <div>
                              <label className="text-xs font-medium text-gray-700">Email</label>
                              <div className="mt-1 p-2 border rounded text-xs bg-gray-50"><EMAIL></div>
                            </div>
                            <div className="grid grid-cols-2 gap-2">
                              <div>
                                <label className="text-xs font-medium text-gray-700">Telefone</label>
                                <div className="mt-1 p-2 border rounded text-xs bg-gray-50">(11) 99999-9999</div>
                              </div>
                              <div>
                                <label className="text-xs font-medium text-gray-700">CPF</label>
                                <div className="mt-1 p-2 border rounded text-xs bg-gray-50">123.456.789-00</div>
                              </div>
                            </div>
                            <div>
                              <label className="text-xs font-medium text-gray-700">Endereço</label>
                              <div className="mt-1 p-2 border rounded text-xs bg-gray-50">Rua das Flores, 123</div>
                            </div>
                            <div className="flex gap-2 pt-2">
                              <button className="flex-1 px-3 py-2 bg-orange-600 text-white rounded text-xs">Salvar</button>
                              <button className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-xs">Cancelar</button>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 bg-orange-600 text-white p-2 rounded text-xs">
                            <strong>Cadastro:</strong> Formulário completo para novos clientes
                          </div>
                        </div>
                      </div>

                      {/* Slide 3: Filtros de Clientes */}
                      <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                        <div className="bg-white w-full max-w-md h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                          <div className="p-2 border-b">
                            <h2 className="text-sm font-bold text-slate-800 flex items-center">
                              <Filter size={16} className="mr-2 text-orange-600" />
                              Filtros de Clientes
                            </h2>
                          </div>
                          <div className="p-3 space-y-3">
                            <div>
                              <label className="text-xs font-medium text-gray-700">Status</label>
                              <select className="mt-1 w-full p-2 border rounded text-xs">
                                <option>Todos</option>
                                <option>Ativo</option>
                                <option>Inativo</option>
                              </select>
                            </div>
                            <div>
                              <label className="text-xs font-medium text-gray-700">Cidade</label>
                              <select className="mt-1 w-full p-2 border rounded text-xs">
                                <option>Todas</option>
                                <option>São Paulo</option>
                                <option>Rio de Janeiro</option>
                              </select>
                            </div>
                            <div>
                              <label className="text-xs font-medium text-gray-700">Data de Cadastro</label>
                              <div className="grid grid-cols-2 gap-2 mt-1">
                                <input type="date" className="p-2 border rounded text-xs" />
                                <input type="date" className="p-2 border rounded text-xs" />
                              </div>
                            </div>
                            <div className="flex gap-2 pt-2">
                              <button className="flex-1 px-3 py-2 bg-orange-600 text-white rounded text-xs">Aplicar</button>
                              <button className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-xs">Limpar</button>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 bg-orange-600 text-white p-2 rounded text-xs">
                            <strong>Filtros:</strong> Encontre clientes específicos rapidamente
                          </div>
                        </div>
                      </div>

                      {/* Slide 4: Detalhes do Cliente */}
                      <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                        <div className="bg-white w-full max-w-md h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                          <div className="p-3 border-b bg-orange-50">
                            <h2 className="text-sm font-bold text-slate-800 flex items-center">
                              <Eye size={16} className="mr-2 text-orange-600" />
                              Detalhes do Cliente
                            </h2>
                          </div>
                          <div className="p-3 space-y-3">
                            <div className="bg-gray-50 p-3 rounded">
                              <div className="text-xs text-gray-600 mb-1">Nome Completo</div>
                              <div className="text-sm font-medium">Maria Silva Santos</div>
                            </div>
                            <div className="grid grid-cols-2 gap-2">
                              <div className="bg-gray-50 p-3 rounded">
                                <div className="text-xs text-gray-600 mb-1">Email</div>
                                <div className="text-sm font-medium"><EMAIL></div>
                              </div>
                              <div className="bg-gray-50 p-3 rounded">
                                <div className="text-xs text-gray-600 mb-1">Telefone</div>
                                <div className="text-sm font-medium">(11) 99999-9999</div>
                              </div>
                            </div>
                            <div className="bg-gray-50 p-3 rounded">
                              <div className="text-xs text-gray-600 mb-1">Pacientes Vinculados</div>
                              <div className="text-sm font-medium text-blue-600">2 pacientes</div>
                            </div>
                            <div className="flex gap-2 pt-2">
                              <button className="flex-1 px-3 py-2 bg-blue-600 text-white rounded text-xs">Editar</button>
                              <button className="flex-1 px-3 py-2 bg-green-600 text-white rounded text-xs">Pacientes</button>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 bg-orange-600 text-white p-2 rounded text-xs">
                            <strong>Detalhes:</strong> Visualize informações completas do cliente
                          </div>
                        </div>
                      </div>

                      {/* Slide 5: Vincular Pacientes */}
                      <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                        <div className="bg-white w-full max-w-md h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                          <div className="p-2 border-b">
                            <h2 className="text-sm font-bold text-slate-800 flex items-center">
                              <Users size={16} className="mr-2 text-orange-600" />
                              Vincular Pacientes
                            </h2>
                          </div>
                          <div className="p-3 space-y-3">
                            <div>
                              <label className="text-xs font-medium text-gray-700">Cliente</label>
                              <div className="mt-1 p-2 border rounded text-xs bg-gray-50">Maria Silva Santos</div>
                            </div>
                            <div>
                              <label className="text-xs font-medium text-gray-700">Pacientes Disponíveis</label>
                              <div className="mt-2 space-y-2 max-h-32 overflow-y-auto">
                                <div className="flex items-center gap-2 p-2 border rounded">
                                  <input type="checkbox" className="rounded" />
                                  <div className="flex-1">
                                    <div className="text-sm font-medium">João Silva Santos</div>
                                    <div className="text-xs text-gray-600">Nascimento: 15/03/2010</div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2 p-2 border rounded">
                                  <input type="checkbox" className="rounded" defaultChecked />
                                  <div className="flex-1">
                                    <div className="text-sm font-medium">Ana Silva Santos</div>
                                    <div className="text-xs text-gray-600">Nascimento: 22/08/2008</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="flex gap-2 pt-2">
                              <button className="flex-1 px-3 py-2 bg-orange-600 text-white rounded text-xs">Salvar</button>
                              <button className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-xs">Cancelar</button>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 bg-orange-600 text-white p-2 rounded text-xs">
                            <strong>Vinculação:</strong> Relacione clientes com seus pacientes
                          </div>
                        </div>
                      </div>

                      {/* Slide 6: Status do Cliente */}
                      <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                        <div className="bg-white w-full max-w-md h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                          <div className="p-2 border-b">
                            <h2 className="text-sm font-bold text-slate-800 flex items-center">
                              <Power size={16} className="mr-2 text-orange-600" />
                              Gerenciar Status
                            </h2>
                          </div>
                          <div className="p-3 space-y-3">
                            <div className="bg-green-50 p-3 rounded border border-green-200">
                              <div className="flex items-center justify-between">
                                <div>
                                  <div className="text-sm font-medium">Maria Silva Santos</div>
                                  <div className="text-xs text-gray-600">Cadastrado em: 15/01/2024</div>
                                </div>
                                <div className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Ativo</div>
                              </div>
                            </div>
                            <div>
                              <label className="text-xs font-medium text-gray-700">Alterar Status</label>
                              <select className="mt-1 w-full p-2 border rounded text-xs">
                                <option>Ativo</option>
                                <option>Inativo</option>
                                <option>Suspenso</option>
                              </select>
                            </div>
                            <div>
                              <label className="text-xs font-medium text-gray-700">Motivo (opcional)</label>
                              <textarea className="mt-1 w-full p-2 border rounded text-xs" rows="2" placeholder="Descreva o motivo da alteração..."></textarea>
                            </div>
                            <div className="flex gap-2 pt-2">
                              <button className="flex-1 px-3 py-2 bg-orange-600 text-white rounded text-xs">Alterar</button>
                              <button className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-xs">Cancelar</button>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 bg-orange-600 text-white p-2 rounded text-xs">
                            <strong>Status:</strong> Controle o status dos clientes no sistema
                          </div>
                        </div>
                      </div>

                      {/* Slide 7: Relatórios de Clientes */}
                      <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                        <div className="bg-white w-full max-w-md h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                          <div className="p-2 border-b">
                            <h2 className="text-sm font-bold text-slate-800 flex items-center">
                              <BarChart4 size={16} className="mr-2 text-orange-600" />
                              Relatórios de Clientes
                            </h2>
                          </div>
                          <div className="p-3 space-y-3">
                            <div className="bg-gray-50 p-3 rounded">
                              <div className="text-xs text-gray-600 mb-2">Resumo Geral</div>
                              <div className="space-y-1">
                                <div className="flex justify-between text-xs">
                                  <span>Total de Clientes</span>
                                  <span className="font-medium">156</span>
                                </div>
                                <div className="flex justify-between text-xs">
                                  <span>Clientes Ativos</span>
                                  <span className="font-medium text-green-600">142</span>
                                </div>
                                <div className="flex justify-between text-xs">
                                  <span>Clientes Inativos</span>
                                  <span className="font-medium text-red-600">14</span>
                                </div>
                              </div>
                            </div>
                            <div className="bg-gray-50 p-3 rounded">
                              <div className="text-xs text-gray-600 mb-2">Novos Cadastros (Mês)</div>
                              <div className="space-y-1">
                                <div className="flex justify-between text-xs">
                                  <span>Janeiro</span>
                                  <span className="font-medium">23</span>
                                </div>
                                <div className="flex justify-between text-xs">
                                  <span>Fevereiro</span>
                                  <span className="font-medium">18</span>
                                </div>
                              </div>
                            </div>
                            <div className="flex gap-2 pt-2">
                              <button className="flex-1 px-3 py-2 bg-orange-600 text-white rounded text-xs">
                                <Download size={12} className="inline mr-1" />
                                Exportar
                              </button>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 bg-orange-600 text-white p-2 rounded text-xs">
                            <strong>Relatórios:</strong> Analise dados e estatísticas dos clientes
                          </div>
                        </div>
                      </div>

                      {/* Slide 8: Resumo Final */}
                      <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                        <div className="bg-white w-full max-w-md h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                          <div className="p-2 border-b">
                            <h2 className="text-sm font-bold text-slate-800 flex items-center">
                              <CheckCircle size={16} className="mr-2 text-orange-600" />
                              Gerenciamento Completo
                            </h2>
                          </div>
                          <div className="p-3 space-y-3">
                            <div className="bg-green-50 p-3 rounded border border-green-200">
                              <div className="text-sm font-medium text-green-800 mb-2">Tutorial Concluído!</div>
                              <div className="text-xs text-green-700">
                                ✓ Cadastro de clientes<br/>
                                ✓ Filtros e busca<br/>
                                ✓ Vinculação com pacientes<br/>
                                ✓ Controle de status<br/>
                                ✓ Relatórios e análises
                              </div>
                            </div>
                            <div className="bg-orange-50 p-3 rounded">
                              <div className="text-xs font-medium text-orange-800 mb-1">Próximos Passos:</div>
                              <div className="text-xs text-orange-700">
                                1. Cadastrar seus primeiros clientes<br/>
                                2. Vincular pacientes aos clientes<br/>
                                3. Explorar outros módulos
                              </div>
                            </div>
                            <div className="flex gap-2 pt-2">
                              <button className="flex-1 px-3 py-2 bg-orange-600 text-white rounded text-xs">Começar</button>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 bg-orange-600 text-white p-2 rounded text-xs">
                            <strong>Sucesso:</strong> Você está pronto para gerenciar clientes!
                          </div>
                        </div>
                      </div>
                        </>
                      )}

                      {/* Tutorial de Pacientes */}
                      {selectedTutorial?.id === 'patients' && (
                        <>
                      {/* Slide 1: Página de Pacientes */}
                      <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                        <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                          <div className="p-2 border-b">
                            <h1 className="text-lg font-bold text-slate-800 flex items-center">
                              <Users size={20} className="mr-2 text-slate-600" />
                              Pacientes
                            </h1>
                          </div>
                          <div className="p-3">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between p-2 bg-blue-50 rounded border">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-blue-200 rounded-full flex items-center justify-center">
                                    <User size={14} className="text-blue-600" />
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">Pedro Silva</div>
                                    <div className="text-xs text-gray-500">Cliente: Maria Silva</div>
                                    <div className="text-xs text-gray-500">Nascimento: 15/03/2010</div>
                                  </div>
                                </div>
                                <div className="flex gap-1">
                                  <button className="p-1 text-blue-600"><Eye size={12} /></button>
                                  <button className="p-1 text-green-600"><Edit size={12} /></button>
                                </div>
                              </div>
                              <div className="flex items-center justify-between p-2 bg-blue-50 rounded border">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-blue-200 rounded-full flex items-center justify-center">
                                    <User size={14} className="text-blue-600" />
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">Lucas Santos</div>
                                    <div className="text-xs text-gray-500">Cliente: João Santos</div>
                                    <div className="text-xs text-gray-500">Nascimento: 22/08/2008</div>
                                  </div>
                                </div>
                                <div className="flex gap-1">
                                  <button className="p-1 text-blue-600"><Eye size={12} /></button>
                                  <button className="p-1 text-green-600"><Edit size={12} /></button>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 right-2 bg-blue-600 text-white p-2 rounded text-xs">
                            <strong>Pacientes:</strong> Cadastro completo com informações médicas
                          </div>
                        </div>
                      </div>
                        </>
                      )}

                      {/* Placeholder para outros tutoriais */}
                      {!selectedTutorial && (
                        <div className="text-center p-6">
                          <div className="w-20 h-20 rounded-full bg-white/20 flex items-center justify-center mx-auto mb-4">
                            <Play size={36} className="text-white ml-1" />
                          </div>
                          <p className="text-white text-sm mb-2">Selecione um tutorial para começar</p>
                          <p className="text-orange-200 text-xs">Escolha uma das opções ao lado para ver a demonstração</p>
                        </div>
                      )}

                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center p-4">
                    <div className="mb-4">
                      <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${selectedTutorial?.color || 'from-orange-500/20 to-orange-500/20'} flex items-center justify-center mx-auto mb-3 hover:opacity-80 transition-all duration-300 cursor-pointer group`}
                           onClick={() => setIsVideoPlaying(true)}>
                        <Play size={24} className="text-white ml-1 group-hover:scale-110 transition-transform" />
                      </div>
                      <h3 className="text-white text-sm font-semibold mb-2">{selectedTutorial?.title || 'Tutorial Completo'}</h3>
                      <p className="text-orange-200 text-xs mb-3">
                        {selectedTutorial?.description || 'Aprenda a usar todas as funcionalidades'}
                      </p>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs text-orange-300 mb-4">
                      {(selectedTutorial?.features || ['Funcionalidade 1', 'Funcionalidade 2', 'Funcionalidade 3', 'Funcionalidade 4', 'Funcionalidade 5']).map((feature, index) => (
                        <div key={index} className="flex items-center gap-1">
                          <div className="w-1.5 h-1.5 rounded-full bg-green-400"></div>
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                    <button
                      onClick={() => setIsVideoPlaying(true)}
                      className={`bg-gradient-to-r ${selectedTutorial?.color || 'from-orange-600 to-orange-600'} hover:opacity-90 text-white px-4 py-2 rounded-lg text-xs font-medium transition-all duration-200 flex items-center gap-2 mx-auto`}
                    >
                      <Play size={14} />
                      Iniciar Tutorial
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Section cards */}
          <h3 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center">
            <Info className="mr-2 text-orange-500" size={20} />
            Seções do Módulo
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            {/* Clients section */}
            <div className="bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500">
                <div className="flex items-center">
                  <UserPlus className="mr-2 text-orange-600 dark:text-orange-300" size={20} />
                  <h3 className="font-semibold text-orange-800 dark:text-white">Clientes</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie o cadastro de clientes no sistema. Mantenha informações completas
                      como dados pessoais, endereço, contatos e documentos.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro completo;
                        Gestão de documentos; Histórico de atendimentos; Relacionamento com pacientes.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                      <UserPlus size={32} className="text-orange-500 dark:text-orange-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Patients section */}
            <div className="bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Users className="mr-2 text-orange-600 dark:text-orange-300" size={20} />
                  <h3 className="font-semibold text-orange-800 dark:text-white">Pacientes</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Cadastre e gerencie pacientes, vinculando-os aos clientes titulares.
                      Mantenha um histórico completo de atendimentos e informações médicas.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro de pacientes;
                        Vínculo com clientes; Histórico médico; Gestão de convênios.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                      <Users size={32} className="text-orange-500 dark:text-orange-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Insurances section */}
            <div className="bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500">
                <div className="flex items-center">
                  <CreditCard className="mr-2 text-orange-600 dark:text-orange-300" size={20} />
                  <h3 className="font-semibold text-orange-800 dark:text-white">Convênios</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie os convênios e planos de saúde disponíveis no sistema.
                      Configure informações como operadora, tipo de plano e cobertura.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro de convênios;
                        Configuração de planos; Vinculação com pacientes; Gestão de coberturas.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                      <CreditCard size={32} className="text-orange-500 dark:text-orange-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Insurance Limits section */}
            <div className="bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Shield className="mr-2 text-orange-600 dark:text-orange-300" size={20} />
                  <h3 className="font-semibold text-orange-800 dark:text-white">Limites de Convênio</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Configure os limites de serviços disponíveis para cada convênio.
                      Defina quantidades, valores e períodos de carência.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Configuração de limites;
                        Definição de carências; Controle de utilização; Alertas de limite.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                      <Shield size={32} className="text-orange-500 dark:text-orange-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Getting started section */}
          <div className="mt-8 bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 p-6">
            <h3 className="text-lg font-semibold text-orange-800 dark:text-white mb-4 flex items-center">
              <Activity className="mr-2 text-orange-600 dark:text-orange-300" size={20} />
              Começando
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Para começar a utilizar o módulo de pessoas, recomendamos seguir estes passos:
            </p>
            <ol className="space-y-3 text-gray-600 dark:text-gray-300">
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0">1</span>
                <span>Cadastre os <strong>clientes</strong> com informações completas, incluindo documentos e contatos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0">2</span>
                <span>Registre os <strong>pacientes</strong> vinculados aos clientes, com seus dados pessoais e médicos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0">3</span>
                <span>Configure os <strong>convênios</strong> disponíveis no sistema.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0">4</span>
                <span>Defina os <strong>limites de convênio</strong> para cada tipo de serviço oferecido.</span>
              </li>
            </ol>
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => window.location.href = '/dashboard/people/clients'}
                className="px-5 py-2.5 bg-orange-600 hover:bg-orange-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
              >
                <UserPlus size={18} />
                Ir para Clientes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntroductionPage;
