name: Frontend CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Testes do Frontend
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout có<PERSON>
        uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Instalar dependências
        run: npm ci
      
      - name: Executar linter
        run: npm run lint
        continue-on-error: true
      
      - name: Executar testes
        run: npm run test --if-present
        continue-on-error: true
      
      - name: Build da aplicação
        run: npm run build

  # Build da imagem Docker
  build:
    needs: [test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout código
        uses: actions/checkout@v4
      
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Login no GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Build e push Frontend
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: |
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
            ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy para VPS
  deploy:
    needs: [build]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Deploy Frontend para VPS
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SSH_KEY }}
          port: 22
          script: |
            set -e
            
            echo "🚀 Iniciando deploy do Frontend..."
            
            # Navegar para diretório do frontend
            cd /root/high-tide-systems-frontend
            
            # Fazer pull das mudanças
            echo "📥 Atualizando código do frontend..."
            git pull origin main
            
            # Atualizar docker-compose para usar nova imagem
            echo "🔄 Atualizando container do frontend..."
            cd /root
            
            # Criar backup da configuração atual
            cp docker-compose.yml docker-compose.yml.backup
            
            # Login no GitHub Container Registry
            echo "🔐 Fazendo login no GitHub Container Registry..."
            echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin

            # Pull da nova imagem
            echo "📦 Baixando nova imagem do frontend..."
            docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}

            # Restaurar backup e substituir image do frontend no docker-compose
            cp docker-compose.yml.backup docker-compose.yml
            sed -i "/frontend:/,/container_name:/ { s|image: ghcr.io/gabrielbr619/high-tide-systems-frontend:.*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}|g }" docker-compose.yml
            
            # Atualizar apenas o frontend (rolling update)
            echo "🔄 Reiniciando container do frontend..."
            docker compose up -d --no-deps frontend
            
            # Aguardar container ficar pronto
            echo "⏳ Aguardando frontend ficar pronto..."
            sleep 20
            
            # Verificar se frontend está respondendo
            echo "🩺 Verificando saúde do frontend..."
            max_attempts=5
            attempt=1
            
            while [ $attempt -le $max_attempts ]; do
              if curl -f http://localhost:3000 > /dev/null 2>&1; then
                echo "✅ Frontend está respondendo!"
                break
              else
                echo "⏳ Tentativa $attempt/$max_attempts - Frontend ainda não está pronto..."
                if [ $attempt -eq $max_attempts ]; then
                  echo "❌ Frontend não respondeu após $max_attempts tentativas"
                  exit 1
                fi
                sleep 10
                attempt=$((attempt + 1))
              fi
            done
            
            # Testar acesso externo
            if curl -f https://hightide.site > /dev/null 2>&1; then
              echo "✅ Site externo está acessível!"
            else
              echo "⚠️ Site externo pode não estar respondendo corretamente"
            fi
            
            # Limpar imagens antigas
            echo "🧹 Limpando imagens antigas..."
            docker image prune -f
            
            echo "✅ Deploy do frontend concluído com sucesso!"
      
      - name: Notificar Discord
        if: always()
        uses: Ilshidur/action-discord@master
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        with:
          args: |
            🎨 **Frontend Deploy** 
            **Status:** ${{ job.status == 'success' && '✅ Sucesso' || '❌ Falhou' }}
            **Branch:** ${{ github.ref_name }}
            **Commit:** ${{ github.event.head_commit.message }}
            **Autor:** ${{ github.event.head_commit.author.name }}
            **URL:** https://hightide.site