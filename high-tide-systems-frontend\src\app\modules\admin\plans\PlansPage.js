"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { calculateAdditionalUsersCost, formatCurrency } from "@/utils/pricing";
import {
  CreditCard,
  Users,
  Package,
  Calendar,
  DollarSign,
  AlertCircle,
  CheckCircle,
  XCircle,
  Plus,
  Minus,
  RefreshCw,
  Settings,
  Crown,
  Shield,
  Zap,
  Building,
  AlertTriangle,
  Lock,
  Ban,
  X,
  ChevronRight,
  ArrowUpCircle
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from "@/hooks/usePermissions";
import { useToast } from "@/contexts/ToastContext";
import { plansService } from "@/app/modules/admin/services/plansService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { ModuleHeader, ModuleSelect } from "@/components/ui";
import ModuleModal from '@/components/ui/ModuleModal';

const PlansPage = () => {
  const router = useRouter();
  const { user } = useAuth();
  const { can } = usePermissions();
  const { toast_success, toast_error } = useToast();

  console.log('[PlansPage] Inicializando página');
  console.log('[PlansPage] Usuário atual:', user);
  console.log('[PlansPage] Permissões:', user?.permissions);
  console.log('[PlansPage] Módulos:', user?.modules);
  console.log('[PlansPage] Role:', user?.role);
  console.log('[PlansPage] CompanyId:', user?.companyId);

  const [isLoading, setIsLoading] = useState(true);
  const [plans, setPlans] = useState([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState(null);
  const [companies, setCompanies] = useState([]);
  const [currentCompany, setCurrentCompany] = useState(null);

  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  // Adicionar após a declaração de outros estados
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [selectedPlanId, setSelectedPlanId] = useState(null);
  const [isAnnual, setIsAnnual] = useState(false);
  const [userCount, setUserCount] = useState(5);

  const basePrice = 19.90; // Preço base por usuário

  // Função para calcular desconto baseado na quantidade de usuários
  const getDiscountByUserCount = (users) => {
    if (users >= 200) return 40;
    if (users >= 100) return 35;
    if (users >= 50) return 25;
    if (users >= 20) return 15;
    if (users >= 5) return 10;
    return 0;
  };

  // Função para calcular preços
  const calculatePrice = (users) => {
    const discount = getDiscountByUserCount(users);
    const totalWithoutDiscount = users * basePrice;
    const discountAmount = totalWithoutDiscount * (discount / 100);
    const finalPrice = totalWithoutDiscount - discountAmount;

    // Desconto adicional de 10% para pagamento anual à vista
    const annualDiscount = 0.10;
    const yearlyPriceWithDiscount = (finalPrice * 12) * (1 - annualDiscount);

    return {
      totalWithoutDiscount,
      discountAmount,
      finalPrice,
      discount,
      monthlyPrice: finalPrice,
      yearlyPrice: finalPrice * 12,
      yearlyPriceWithDiscount,
      annualDiscount: annualDiscount * 100
    };
  };

  const pricing = calculatePrice(userCount);

  // Opções de usuários predefinidas
  const userOptions = [1, 5, 20, 50, 100, 200];

  useEffect(() => {
    const loadData = async () => {
      try {
        if (isSystemAdmin) {
          // Carregar lista de empresas para admin do sistema
          const companiesData = await companyService.getCompaniesForSelect();
          setCompanies(companiesData);

          // Se houver empresas, selecionar a primeira por padrão
          if (companiesData.length > 0 && !selectedCompanyId) {
            setSelectedCompanyId(companiesData[0].id);
          }
        } else if (user?.companyId) {
          // Para usuários não-admin, usar a empresa atual
          setSelectedCompanyId(user.companyId);
          try {
            const company = await companyService.getCurrentCompany();
            setCurrentCompany(company);
          } catch (error) {
            console.error('[PlansPage] Erro ao carregar empresa atual:', error);
          }
        }
      } catch (error) {
        console.error('[PlansPage] Erro ao carregar dados iniciais:', error);
        toast_error('Erro ao carregar dados iniciais');
      }
    };

    loadData();
  }, [user, isSystemAdmin]);

  useEffect(() => {
    const loadPlanData = async () => {
      if (!selectedCompanyId) return;

      setIsLoading(true);
      try {
        console.log('[PlansPage] Carregando dados do plano para companyId:', selectedCompanyId);
        const [plansData, subscriptionData] = await Promise.all([
          plansService.getAvailablePlans(),
          plansService.getPlansData(selectedCompanyId)
        ]);

        console.log('[PlansPage] Dados dos planos carregados:', plansData);
        console.log('[PlansPage] Dados da assinatura carregados:', subscriptionData);

        setPlans(plansData);
      } catch (error) {
        console.error('[PlansPage] Erro ao carregar dados dos planos:', error);
        console.error('[PlansPage] Detalhes do erro:', error.response?.data);
        toast_error('Erro ao carregar dados dos planos');
      } finally {
        setIsLoading(false);
      }
    };

    loadPlanData();
  }, [selectedCompanyId]);

  const [planData, setPlanData] = useState(null);
  const [availablePlans, setAvailablePlans] = useState(null);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Estados para o modal de adicionar usuários
  const [showAddUsersModal, setShowAddUsersModal] = useState(false);
  const [additionalUsersCount, setAdditionalUsersCount] = useState(1);
  const [couponCode, setCouponCode] = useState('');
  const [couponValidation, setCouponValidation] = useState(null);
  const [isValidatingCoupon, setIsValidatingCoupon] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelConfirmationText, setCancelConfirmationText] = useState('');

  // Estados para o modal de confirmação de módulos
  const [showModuleConfirmModal, setShowModuleConfirmModal] = useState(false);
  const [moduleAction, setModuleAction] = useState(null); // 'add' ou 'remove'
  const [selectedModule, setSelectedModule] = useState(null);

  // Verificar se o usuário atual é um system_admin
  console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);

  // Função para carregar empresas (apenas para system_admin)
  const loadCompanies = async () => {
    if (!isSystemAdmin) return;

    setIsLoadingCompanies(true);
    try {
      const response = await companyService.getCompaniesForSelect();
      setCompanies(response);
      
      // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira
      if (!selectedCompanyId && response.length > 0) {
        setSelectedCompanyId(response[0].id);
      }
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível carregar as empresas."
      });
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  // Função para carregar dados do plano
  const loadPlanData = async (forceRefresh = false) => {
    console.log('[DEBUG] ===== INICIANDO loadPlanData =====');
    console.log('[DEBUG] forceRefresh:', forceRefresh);
    console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);
    console.log('[DEBUG] selectedCompanyId:', selectedCompanyId);
    console.log('[DEBUG] currentUser.companyId:', user?.companyId);
    console.log('[DEBUG] Tem permissão admin.dashboard.view?', can('admin.dashboard.view'));

    // Para system_admin, não carregar se não tiver empresa selecionada
    if (isSystemAdmin && !selectedCompanyId) {
      console.log('[DEBUG] System admin sem empresa selecionada, não carregando dados');
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : user?.companyId;
      console.log('[DEBUG] Carregando dados do plano para empresa:', companyId, 'forceRefresh:', forceRefresh);

      if (!companyId) {
        console.error('[DEBUG] Nenhum companyId disponível para carregar dados do plano');
        throw new Error('ID da empresa não disponível');
      }

      console.log('[DEBUG] Fazendo chamadas para API...');
      const [planResponse, availablePlansResponse] = await Promise.all([
        plansService.getPlansData(companyId, forceRefresh),
        plansService.getAvailablePlans()
      ]);

      console.log('[DEBUG] ===== RESPOSTA RECEBIDA =====');
      console.log('[DEBUG] planResponse completo:', JSON.stringify(planResponse, null, 2));
      console.log('[DEBUG] availablePlansResponse completo:', JSON.stringify(availablePlansResponse, null, 2));
      console.log('[DEBUG] Módulos ativos:', planResponse?.modules?.map(m => `${m.moduleType} (${m.active ? 'ATIVO' : 'INATIVO'})`));
      console.log('[DEBUG] Quantidade de módulos:', planResponse?.modules?.length);
      console.log('[DEBUG] availablePlans.modules:', availablePlansResponse?.modules);

      console.log('[DEBUG] Estado anterior planData:', planData?.modules?.map(m => m.moduleType));

      setPlanData(planResponse);
      setAvailablePlans(availablePlansResponse);

      console.log('[DEBUG] ===== ESTADO ATUALIZADO =====');

    } catch (error) {
      console.error("[DEBUG] ===== ERRO AO CARREGAR DADOS =====");
      console.error("Erro ao carregar dados do plano:", error);
      console.error("Error details:", error.response?.data);
      console.error("Error status:", error.response?.status);
      console.error("Error headers:", error.response?.headers);
      toast_error({
        title: "Erro",
        message: error.response?.data?.message || "Não foi possível carregar os dados do plano."
      });
    } finally {
      setIsLoading(false);
      console.log('[DEBUG] ===== FIM loadPlanData =====');
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    if (isSystemAdmin) {
      loadCompanies();
    } else {
      loadPlanData();
    }
  }, [isSystemAdmin]);

  // Recarregar dados quando a empresa selecionada mudar
  useEffect(() => {
    if (isSystemAdmin && selectedCompanyId) {
      loadPlanData();
    } else if (!isSystemAdmin) {
      loadPlanData();
    }
  }, [selectedCompanyId, isSystemAdmin]);

  // Monitor planData changes
  useEffect(() => {
    console.log('[DEBUG] ===== PLANDATA MUDOU =====');
    console.log('[DEBUG] planData:', planData);
    console.log('[DEBUG] Módulos no estado:', planData?.modules?.map(m => `${m.moduleType} (${m.active ? 'ATIVO' : 'INATIVO'})`));
    console.log('[DEBUG] ================================');
  }, [planData]);

  // Função para abrir modal de adicionar usuários
  const handleOpenAddUsersModal = () => {
    setAdditionalUsersCount(1);
    setCouponCode('');
    setCouponValidation(null);
    setIsValidatingCoupon(false);
    setShowAddUsersModal(true);
  };

  // Função para fechar modal de adicionar usuários
  const handleCloseAddUsersModal = () => {
    setShowAddUsersModal(false);
    setAdditionalUsersCount(1);
    setCouponCode('');
    setCouponValidation(null);
    setIsValidatingCoupon(false);
  };

  // Função para abrir modal de cancelamento
  const handleOpenCancelModal = () => {
    setCancelConfirmationText('');
    setShowCancelModal(true);
  };

  // Função para fechar modal de cancelamento
  const handleCloseCancelModal = () => {
    setShowCancelModal(false);
    setCancelConfirmationText('');
  };

  // Função para calcular o custo adicional usando a função centralizada
  const calculateAdditionalCost = () => {
    if (!planData) return { additionalCost: 0, costPerAdditionalUser: 19.90 };

    const currentUsers = planData.subscription.userLimit;
    const isAnnual = planData.subscription.billingCycle === 'YEARLY';

    return calculateAdditionalUsersCost(currentUsers, additionalUsersCount, isAnnual);
  };

  // Função para calcular preço por usuário atual
  const calculatePricePerUser = () => {
    if (!planData) return 19.90;

    const currentPrice = planData.subscription.pricePerMonth;
    const currentUsers = planData.subscription.userLimit;

    if (currentUsers > 0) {
      return currentPrice / currentUsers;
    }

    return 19.90;
  };

  // Função para validar cupom
  const validateCoupon = async (code) => {
    if (!code || code.trim() === '') {
      setCouponValidation(null);
      return;
    }

    setIsValidatingCoupon(true);
    try {
      const response = await plansService.validateCoupon(code.trim());
      setCouponValidation({
        valid: true,
        coupon: response.coupon,
        message: `Cupom válido! ${response.coupon.type === 'PERCENT' ? `${response.coupon.value}% de desconto` : `R$ ${response.coupon.value.toFixed(2)} de desconto`}`
      });
    } catch (error) {
      setCouponValidation({
        valid: false,
        message: error.response?.data?.message || 'Cupom inválido'
      });
    } finally {
      setIsValidatingCoupon(false);
    }
  };

  // Debounce para validação de cupom
  useEffect(() => {
    const timer = setTimeout(() => {
      if (couponCode.trim()) {
        validateCoupon(couponCode);
      } else {
        setCouponValidation(null);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [couponCode]);

  // Função para adicionar usuários (confirmada pelo modal)
  const handleAddUsers = async () => {
    try {
      setIsUpdating(true);
      const newUserCount = planData.usage.currentUsers + additionalUsersCount;
      
      // Validar cupom antes de prosseguir se foi informado
      if (couponCode.trim() && (!couponValidation || !couponValidation.valid)) {
        toast_error('Aguarde a validação do cupom ou remova-o para continuar');
        return;
      }
      
      // Criar uma nova sessão de checkout do Stripe
      const response = await plansService.createCheckoutSession(
        planData.subscription.billingCycle.toLowerCase(),
        newUserCount,
        couponCode.trim() || null
      );

      // Redirecionar para a página de checkout do Stripe
      window.location.href = response.url;
    } catch (error) {
      console.error('Erro ao atualizar usuários:', error);
      toast_error(error.response?.data?.message || 'Erro ao atualizar usuários');
    } finally {
      setIsUpdating(false);
      handleCloseAddUsersModal();
    }
  };

  // Função para abrir modal de confirmação para adicionar módulo
  const openAddModuleConfirmation = (moduleType) => {
    console.log('[DEBUG] openAddModuleConfirmation:', { moduleType, availablePlans });
    console.log('[DEBUG] availablePlans.modules:', availablePlans?.modules);

    if (!availablePlans?.modules) {
      console.error('[DEBUG] availablePlans.modules não está disponível');
      toast_error({
        title: "Erro",
        message: "Dados dos módulos não estão disponíveis. Tente recarregar a página."
      });
      return;
    }

    const moduleInfo = availablePlans.modules[moduleType];
    console.log('[DEBUG] moduleInfo encontrado:', moduleInfo);

    setSelectedModule({ type: moduleType, info: moduleInfo });
    setModuleAction('add');
    setShowModuleConfirmModal(true);
  };

  // Função para abrir modal de confirmação para remover módulo
  const openRemoveModuleConfirmation = (moduleType) => {
    const moduleInfo = planData?.modules?.find(m => m.moduleType === moduleType);
    setSelectedModule({ type: moduleType, info: moduleInfo });
    setModuleAction('remove');
    setShowModuleConfirmModal(true);
  };

  // Função para fechar modal de confirmação de módulos
  const closeModuleConfirmModal = () => {
    setShowModuleConfirmModal(false);
    setModuleAction(null);
    setSelectedModule(null);
  };

  // Função para confirmar a ação do módulo
  const confirmModuleAction = async () => {
    if (!selectedModule || !moduleAction) return;

    closeModuleConfirmModal();

    if (moduleAction === 'add') {
      await handleAddModule(selectedModule.type);
    } else if (moduleAction === 'remove') {
      await handleRemoveModule(selectedModule.type);
    }
  };

  // Função para adicionar módulo
  const handleAddModule = async (moduleType) => {
    setIsUpdating(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      await plansService.addModule(moduleType, companyId);
      toast_success({
        title: "Sucesso",
        message: "Módulo adicionado ao plano com sucesso."
      });

      // Aguardar um pouco para garantir que o cache foi invalidado
      await new Promise(resolve => setTimeout(resolve, 500));
      await loadPlanData(true); // Force refresh
    } catch (error) {
      console.error("Erro ao adicionar módulo:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível adicionar o módulo ao plano."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para remover módulo
  const handleRemoveModule = async (moduleType) => {
    console.log('[DEBUG] Iniciando remoção do módulo:', moduleType);
    setIsUpdating(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      console.log('[DEBUG] Removendo módulo para empresa:', companyId);

      const result = await plansService.removeModule(moduleType, companyId);
      console.log('[DEBUG] Resultado da remoção:', result);

      toast_success({
        title: "Sucesso",
        message: "Módulo removido do plano com sucesso."
      });

      console.log('[DEBUG] Aguardando invalidação de cache...');
      // Aguardar um pouco para garantir que o cache foi invalidado
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('[DEBUG] Recarregando dados do plano...');
      await loadPlanData(true); // Force refresh para evitar cache
      console.log('[DEBUG] Dados recarregados');

    } catch (error) {
      console.error("Erro ao remover módulo:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível remover o módulo do plano."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para cancelar assinatura (confirmada pelo modal)
  const handleCancelSubscription = async () => {
    setIsUpdating(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      await plansService.cancelSubscription(companyId);
      toast_success({
        title: "Assinatura Cancelada",
        message: "Sua assinatura foi cancelada com sucesso. O acesso será mantido até o final do período pago."
      });
      handleCloseCancelModal();
      loadPlanData(true); // Force refresh
    } catch (error) {
      console.error("Erro ao cancelar assinatura:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível cancelar a assinatura."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Função para reativar assinatura
  const handleReactivateSubscription = async () => {
    setIsUpdating(true);
    try {
      const companyId = isSystemAdmin ? selectedCompanyId : null;
      await plansService.reactivateSubscription(companyId);
      toast_success({
        title: "Sucesso",
        message: "Assinatura reativada com sucesso."
      });
      loadPlanData();
    } catch (error) {
      console.error("Erro ao reativar assinatura:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível reativar a assinatura."
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Adicionar a função handleUpgrade após a função handleCancelSubscription
  const handleUpgrade = async () => {
    try {
      const billingCycle = isAnnual ? 'yearly' : 'monthly';
      const response = await plansService.createCheckoutSession(billingCycle, userCount);
      if (response.url) {
        window.location.href = response.url;
      } else {
        throw new Error('URL de checkout não encontrada');
      }
    } catch (error) {
      console.error('Erro ao iniciar checkout:', error);
      toast_error('Erro ao iniciar processo de upgrade. Tente novamente.');
    }
  };

  // Função para formatar status
  const getStatusInfo = (status) => {
    switch (status) {
      case 'ACTIVE':
        return { 
          label: 'Ativo', 
          color: 'text-green-600 dark:text-green-400', 
          bgColor: 'bg-green-100 dark:bg-green-900/30',
          icon: CheckCircle 
        };
      case 'CANCELED':
        return { 
          label: 'Cancelado', 
          color: 'text-red-600 dark:text-red-400', 
          bgColor: 'bg-red-100 dark:bg-red-900/30',
          icon: XCircle 
        };
      case 'PAST_DUE':
        return { 
          label: 'Em Atraso', 
          color: 'text-yellow-600 dark:text-yellow-400', 
          bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
          icon: AlertCircle 
        };
      default:
        return { 
          label: status, 
          color: 'text-gray-600 dark:text-gray-400', 
          bgColor: 'bg-gray-100 dark:bg-gray-900/30',
          icon: AlertCircle 
        };
    }
  };

  // Função para formatar ciclo de cobrança
  const getBillingCycleLabel = (cycle) => {
    switch (cycle) {
      case 'MONTHLY':
        return 'Mensal';
      case 'YEARLY':
        return 'Anual';
      default:
        return cycle;
    }
  };

  // Após carregar planData (ou subscriptionData)
  const isTrial = planData?.subscription?.status === 'TRIAL';
  const trialEndDate = planData?.subscription?.endDate ? new Date(planData.subscription.endDate).toLocaleDateString('pt-BR') : 'N/A';

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="animate-spin h-8 w-8 text-gray-400" />
        <span className="ml-2 text-gray-600 dark:text-gray-400">Carregando dados do plano...</span>
      </div>
    );
  }

  // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada
  if (isSystemAdmin && !selectedCompanyId) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Gerenciamento de Planos"
          icon={<CreditCard size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
          description="Gerencie planos, usuários e módulos das assinaturas das empresas."
          moduleColor="admin"
          filters={
            <div className="w-full sm:w-64">
              <ModuleSelect
                moduleColor="admin"
                value={selectedCompanyId}
                onChange={(e) => setSelectedCompanyId(e.target.value)}
                placeholder="Selecione uma empresa"
                disabled={isLoadingCompanies}
              >
                <option value="">Selecione uma empresa</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>
          }
        />

        <div className="text-center py-12">
          <Building className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            Selecione uma empresa
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.
          </p>
        </div>
      </div>
    );
  }

  if (!planData) {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Gerenciamento de Planos"
          icon={<CreditCard size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
          description="Gerencie seu plano, usuários e módulos da assinatura."
          moduleColor="admin"
          filters={
            isSystemAdmin && (
              <div className="w-full sm:w-64">
                <ModuleSelect
                  moduleColor="admin"
                  value={selectedCompanyId}
                  onChange={(e) => setSelectedCompanyId(e.target.value)}
                  placeholder="Selecione uma empresa"
                  disabled={isLoadingCompanies}
                >
                  <option value="">Selecione uma empresa</option>
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </ModuleSelect>
              </div>
            )
          }
        />

        <div className="text-center py-12">
          <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
            Nenhum plano encontrado
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Não foi possível encontrar informações do plano para esta empresa.
          </p>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(planData.subscription.status);
  const StatusIcon = statusInfo.icon;

  return (
    <div className="space-y-6">
      {/* Cabeçalho */}
      <ModuleHeader
        title="Gerenciamento de Planos"
        icon={<CreditCard size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
        description={isSystemAdmin
          ? `Gerencie o plano, usuários e módulos da assinatura de ${planData.company.name}.`
          : "Gerencie seu plano, usuários e módulos da assinatura."
        }
        moduleColor="admin"
        filters={
          isSystemAdmin && (
            <div className="w-full sm:w-64">
              <ModuleSelect
                moduleColor="admin"
                value={selectedCompanyId}
                onChange={(e) => setSelectedCompanyId(e.target.value)}
                placeholder="Selecione uma empresa"
                disabled={isLoadingCompanies}
              >
                <option value="">Selecione uma empresa</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>
          )
        }
      />

      {/* Cards principais */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Card do Plano Atual */}
        <div className="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <Crown className="mr-2 h-5 w-5 text-yellow-500" />
              Plano Atual
            </h2>
            <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusInfo.bgColor} ${statusInfo.color}`}>
              <StatusIcon className="mr-1 h-3 w-3" />
              {statusInfo.label}
            </div>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Empresa</p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-100">
                  {planData.company.name}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Ciclo de Cobrança</p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-100">
                  {getBillingCycleLabel(planData.subscription.billingCycle)}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Preço Mensal Total</p>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  R$ {planData.subscription.pricePerMonth.toFixed(2)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Próxima Cobrança</p>
                <p className="text-base font-medium text-gray-900 dark:text-gray-100">
                  {planData.subscription.nextBillingDate
                    ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR')
                    : 'N/A'
                  }
                </p>
              </div>
            </div>

            {/* Breakdown detalhado dos preços */}
            <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 space-y-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center">
                <DollarSign className="mr-2 h-4 w-4 text-green-500" />
                Detalhamento dos Valores
              </h4>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Usuários ativos:</span>
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {planData.usage.currentUsers} de {planData.usage.userLimit}
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Preço base (R$ 19,90/usuário):</span>
                  <span className="text-gray-500 dark:text-gray-400 line-through">
                    R$ {(planData.usage.userLimit * 19.90).toFixed(2)}
                  </span>
                </div>

                {(() => {
                  const currentDiscount = getDiscountByUserCount(planData.usage.userLimit);
                  const discountAmount = (planData.usage.userLimit * 19.90) * (currentDiscount / 100);
                  const priceAfterDiscount = (planData.usage.userLimit * 19.90) - discountAmount;
                  
                  return (
                    <>
                      {currentDiscount > 0 && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400">
                            Desconto por volume ({currentDiscount}%):
                          </span>
                          <span className="font-medium text-green-600 dark:text-green-400">
                            -R$ {discountAmount.toFixed(2)}
                          </span>
                        </div>
                      )}
                      
                      {planData.subscription.billingCycle === 'YEARLY' && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400">Desconto anual (10%):</span>
                          <span className="font-medium text-green-600 dark:text-green-400">
                            -R$ {(priceAfterDiscount * 0.10).toFixed(2)}
                          </span>
                        </div>
                      )}
                      
                      {planData.subscription.appliedCoupon && (
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400">
                            Cupom aplicado ({planData.subscription.appliedCoupon.code}):
                          </span>
                          <span className="font-medium text-green-600 dark:text-green-400">
                            {planData.subscription.appliedCoupon.type === 'PERCENT' 
                              ? `-${planData.subscription.appliedCoupon.value}%` 
                              : `-R$ ${planData.subscription.appliedCoupon.value.toFixed(2)}`
                            }
                          </span>
                        </div>
                      )}
                      
                      <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-gray-900 dark:text-gray-100">Preço por usuário:</span>
                          <span className="font-medium text-gray-900 dark:text-gray-100">
                            R$ {(planData.subscription.pricePerMonth / planData.usage.userLimit).toFixed(2)}
                          </span>
                        </div>
                      </div>

                      {planData.subscription.billingCycle === 'YEARLY' && (
                        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-md p-3 mt-3">
                          <div className="flex items-center text-blue-700 dark:text-blue-300">
                            <Shield className="mr-2 h-4 w-4" />
                            <span className="text-sm font-medium">Plano Anual Ativo</span>
                          </div>
                          <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                            Você economiza R$ {((priceAfterDiscount * 12) - (planData.subscription.pricePerMonth * 12)).toFixed(2)} por ano
                          </p>
                        </div>
                      )}
                    </>
                  );
                })()}
              </div>
            </div>

            {/* Ações do plano */}
            <div className="flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
              {planData.subscription.status === 'ACTIVE' ? (
                <button
                  onClick={handleOpenCancelModal}
                  disabled={isUpdating}
                  className="flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50"
                >
                  <XCircle className="mr-1 h-4 w-4" />
                  Cancelar Plano
                </button>
              ) : planData.subscription.status === 'CANCELED' ? (
                <button
                  onClick={() => setShowUpgradeModal(true)}
                  disabled={isUpdating}
                  className="flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50"
                >
                  <CheckCircle className="mr-1 h-4 w-4" />
                  Reativar Plano
                </button>
              ) : planData.subscription.status === 'TRIAL' ? (
                <button
                  onClick={() => setShowUpgradeModal(true)}
                  disabled={isUpdating}
                  className="flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50"
                >
                  <ArrowUpCircle className="mr-1 h-4 w-4" />
                  Fazer Upgrade
                </button>
              ) : null}

              <button
                onClick={() => {
                  const url = isSystemAdmin && selectedCompanyId
                    ? `/subscription/invoices?companyId=${selectedCompanyId}`
                    : '/subscription/invoices';
                  router.push(url);
                }}
                className="flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors"
              >
                <Calendar className="mr-1 h-4 w-4" />
                Ver Faturas
              </button>
            </div>
          </div>
        </div>

        {/* Card de Uso de Usuários */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center">
              <Users className="mr-2 h-5 w-5 text-blue-500" />
              Uso de Usuários
            </h2>
            <button
              onClick={handleOpenAddUsersModal}
              disabled={isUpdating || planData.subscription.status === 'TRIAL' || planData.subscription.status === 'CANCELED'}
              className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                planData.subscription.status === 'TRIAL' || planData.subscription.status === 'CANCELED'
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              <Plus className="mr-1 h-4 w-4" />
              Adicionar Usuários
            </button>
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Usuários Ativos</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {planData.usage.currentUsers}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500 dark:text-gray-400">Limite de Usuários</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {planData.usage.userLimit}
                </p>
              </div>
            </div>

            {/* Barra de progresso */}
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
              <div
                className="h-2.5 rounded-full transition-all duration-500"
                style={{
                  width: `${planData.usage.userLimitUsage}%`,
                  backgroundColor: planData.usage.userLimitUsage >= 90
                    ? '#EF4444' // Vermelho para uso >= 90%
                    : planData.usage.userLimitUsage >= 75
                      ? '#F59E0B' // Amarelo para uso >= 75%
                      : '#3B82F6' // Azul para uso < 75%
                }}
              />
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-gray-500 dark:text-gray-400">
                {planData.usage.currentUsers} de {planData.usage.userLimit} usuários
              </span>
              <span className="text-gray-500 dark:text-gray-400">
                {planData.usage.userLimitUsage}% utilizado
              </span>
            </div>

            {planData.usage.userLimitUsage >= 90 && (
              <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 text-sm rounded-md">
                <AlertCircle className="inline-block mr-1 h-4 w-4" />
                Você está próximo do limite de usuários. Considere adicionar mais usuários ao seu plano.
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Seção de Módulos */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6">
          <Package className="mr-2 h-5 w-5 text-purple-500" />
          Módulos da Assinatura
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Módulos Ativos */}
          {planData.modules.map((module) => (
            <div key={module.id} className="border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <span className="font-medium text-gray-900 dark:text-gray-100">
                    {getModuleName(module.moduleType)}
                  </span>
                </div>
                <span className="text-sm text-green-600 dark:text-green-400 font-medium">
                  Ativo
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                R$ {module.pricePerMonth.toFixed(2)}/mês
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Adicionado em {new Date(module.addedAt).toLocaleDateString('pt-BR')}
              </p>

              {/* Botão para remover módulo (apenas módulos opcionais) */}
              {!isBasicModule(module.moduleType) && (
                <button
                  onClick={() => openRemoveModuleConfirmation(module.moduleType)}
                  disabled={isUpdating}
                  className="mt-3 w-full flex items-center justify-center px-2 py-1 bg-red-100 hover:bg-red-200 dark:bg-red-900/30 dark:hover:bg-red-900/50 text-red-700 dark:text-red-400 text-xs font-medium rounded transition-colors disabled:opacity-50"
                >
                  <Minus className="mr-1 h-3 w-3" />
                  Remover
                </button>
              )}
            </div>
          ))}

          {/* Informação sobre o plano básico */}
          <div className="border border-blue-200 dark:border-blue-700 rounded-lg p-4 bg-blue-50 dark:bg-blue-900/20">
            <div className="flex items-center mb-2">
              <CheckCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
              <span className="font-medium text-blue-900 dark:text-blue-100">
                Plano Básico Completo
              </span>
            </div>
            <p className="text-sm text-blue-800 dark:text-blue-200 mb-2">
              Seu plano já inclui todos os módulos essenciais para o funcionamento completo do sistema.
            </p>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Administração completa</li>
              <li>• Sistema de agendamento</li>
              <li>• Gerenciamento de pessoas</li>
              <li>• Relatórios e dashboards</li>
              <li>• Suporte técnico incluído</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Mensagem de alerta para plano em trial */}
      {isTrial && (
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded flex items-center gap-3">
          <AlertCircle className="w-6 h-6 text-yellow-500" />
          <div>
            <strong>Você está em período de avaliação (trial).</strong>
            <div>
              Aproveite para testar todos os recursos! O acesso será limitado após o término do trial.<br/>
              <strong>Data de término do trial:</strong> {trialEndDate}<br/>
              Caso queira migrar para um plano completo, clique no botão abaixo.
            </div>
            <button
              onClick={() => setShowUpgradeModal(true)}
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Fazer Upgrade
            </button>
          </div>
        </div>
      )}

      {/* Modal de Upgrade */}
      <ModuleModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        title="Quantos usuários você precisa?"
        size="lg"
        moduleColor="admin"
        footer={
          <div className="flex flex-col sm:flex-row items-center justify-center gap-3 px-4 py-3 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-b-xl">
            <button
              onClick={() => setShowUpgradeModal(false)}
              className="inline-flex items-center justify-center px-7 py-2 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={handleUpgrade}
              className="inline-flex items-center justify-center px-7 py-2 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
            >
              Confirmar Upgrade
              <ChevronRight className="ml-2 h-5 w-5" />
            </button>
          </div>
        }
      >
        <div className="flex flex-col gap-4 px-2 py-2 md:px-4 md:py-2">
          <div className="text-center mb-1">
            <p className="text-gray-600 dark:text-gray-300 text-base md:text-lg mt-2 mb-2">
              Selecione a quantidade de usuários para ver o preço personalizado
            </p>
            {/* Toggle Anual/Mensal */}
            <div className="flex items-center justify-center gap-3 mt-1 mb-2">
              <span className={`mr-1 ${!isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'}`}>Mensal</span>
              <button
                onClick={() => setIsAnnual(!isAnnual)}
                className={`relative inline-flex h-6 w-14 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 ${isAnnual ? 'bg-orange-600' : 'bg-gray-300 dark:bg-gray-600'}`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${isAnnual ? 'translate-x-9' : 'translate-x-1'}`}
                />
              </button>
              <span className={`ml-1 ${isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'}`}>Anual</span>
            </div>
          </div>

          <div className="flex flex-col gap-3">
            <div className="flex flex-wrap justify-center gap-2 mb-1">
              {userOptions.map((option) => {
                const discount = getDiscountByUserCount(option);
                return (
                  <button
                    key={option}
                    onClick={() => setUserCount(option)}
                    className={`relative p-5 min-w-[130px] min-h-[70px] rounded-xl border-2 transition-all text-center flex flex-col items-center justify-center gap-1 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 text-lg font-semibold ${
                      userCount === option
                        ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400'
                        : 'border-gray-200 dark:border-gray-700 hover:border-orange-300 dark:hover:border-orange-600 bg-white dark:bg-gray-900 text-gray-900 dark:text-white'
                    }`}
                  >
                    {discount > 0 && (
                      <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow">
                        -{discount}%
                      </div>
                    )}
                    <div>{option}</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 font-normal">{option === 1 ? 'usuário' : 'usuários'}</div>
                  </button>
                );
              })}
            </div>

            <div className="flex flex-col items-center gap-1 mb-1">
              <p className="text-xs text-gray-600 dark:text-gray-300 mb-0.5">Ou digite uma quantidade personalizada:</p>
              <div className="flex items-center justify-center w-full">
                <div className="relative flex items-center w-32 md:w-44">
                  <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <input
                    type="number"
                    min="1"
                    max="1000"
                    value={userCount}
                    onChange={(e) => setUserCount(Math.max(1, parseInt(e.target.value) || 1))}
                    className="pl-10 pr-2 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700 w-full text-center text-base"
                    placeholder="1"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-3 bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mt-1 border border-gray-200 dark:border-gray-700">
            <div className="flex-1 space-y-2 text-left">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-600 dark:text-gray-400">Usuários:</span>
                <span className="font-medium text-gray-900 dark:text-white">{userCount}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-600 dark:text-gray-400">Preço por usuário {isAnnual ? 'anual à vista' : 'mensal'}:</span>
                <div className="text-right">
                  {pricing.discount > 0 ? (
                    <>
                      <span className="text-xs text-gray-500 dark:text-gray-400 line-through">R$ {(isAnnual ? basePrice * 12 * (1 - pricing.annualDiscount / 100) : basePrice).toFixed(2).replace('.', ',')}</span>
                      <span className="ml-2 font-medium text-gray-900 dark:text-white">R$ {(isAnnual ? (basePrice * (1 - pricing.discount / 100)) * 12 * (1 - pricing.annualDiscount / 100) : basePrice * (1 - pricing.discount / 100)).toFixed(2).replace('.', ',')}</span>
                    </>
                  ) : (
                    <span className="font-medium text-gray-900 dark:text-white">R$ {(isAnnual ? basePrice * 12 * (1 - pricing.annualDiscount / 100) : basePrice).toFixed(2).replace('.', ',')}</span>
                  )}
                </div>
              </div>
              {pricing.discount > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-600 dark:text-gray-400">Desconto por quantidade:</span>
                  <span className="font-medium text-green-600 dark:text-green-400">-{pricing.discount}%</span>
                </div>
              )}
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">Valor mensal sem desconto:</span>
                <span className="text-sm text-gray-500 dark:text-gray-400 line-through">R$ {pricing.totalWithoutDiscount.toFixed(2).replace('.', ',')}</span>
              </div>
              {isAnnual && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Desconto anual à vista:</span>
                  <span className="font-medium text-green-600 dark:text-green-400">-{pricing.annualDiscount}%</span>
                </div>
              )}
            </div>
            <div className="flex flex-col items-end justify-center min-w-[180px]">
              <span className="text-sm text-gray-600 dark:text-gray-400 mb-1">Valor mensal:</span>
              <span className="text-3xl font-bold text-orange-600 dark:text-orange-400">R$ {isAnnual ? (pricing.yearlyPriceWithDiscount / 12).toFixed(2).replace('.', ',') : pricing.monthlyPrice.toFixed(2).replace('.', ',')}</span>
            </div>
          </div>
        </div>
      </ModuleModal>

      {/* Modal de Gerenciar Limite de Usuários */}
      {showAddUsersModal && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white dark:bg-gray-900 p-8 rounded-xl shadow-lg max-w-4xl w-full border border-gray-200 dark:border-gray-700">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Gerenciar limite de usuários
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Ajuste o limite de usuários do seu plano
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-8">
              {[1, 5, 10, 20].map((option) => {
                const newLimit = planData.usage.userLimit + option;
                const discount = getDiscountByUserCount(newLimit);
                const currentDiscount = getDiscountByUserCount(planData.usage.userLimit);
                const discountChange = discount - currentDiscount;
                const isValid = true;

                return (
                  <button
                    key={option}
                    onClick={() => setAdditionalUsersCount(option)}
                    className={`p-4 rounded-lg border-2 transition-all text-center relative ${
                      additionalUsersCount === option
                        ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400'
                        : 'border-gray-200 dark:border-gray-700 hover:border-orange-300 dark:hover:border-orange-600'
                    }`}
                  >
                    {discountChange > 0 && (
                      <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                        +{discountChange}%
                      </div>
                    )}
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      +{option}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {option === 1 ? 'usuário' : 'usuários'}
                    </div>
                  </button>
                );
              })}
            </div>

            {/* Campo de cupom */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Cupom de desconto (opcional)
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                  placeholder="Digite o código do cupom"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700 pr-10"
                />
                {isValidatingCoupon && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <RefreshCw className="animate-spin h-4 w-4 text-gray-400" />
                  </div>
                )}
              </div>
              {couponValidation && (
                <div className={`mt-2 text-sm ${couponValidation.valid ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                  {couponValidation.valid ? (
                    <div className="flex items-center">
                      <CheckCircle className="mr-1 h-4 w-4" />
                      {couponValidation.message}
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <XCircle className="mr-1 h-4 w-4" />
                      {couponValidation.message}
                    </div>
                  )}
                </div>
              )}
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Limite atual:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{planData.usage.userLimit}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Alteração:</span>
                    <span className="font-medium text-green-600 dark:text-green-400">
                      +{additionalUsersCount}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Novo limite:</span>
                    <span className="font-medium text-gray-900 dark:text-white">{planData.usage.userLimit + additionalUsersCount}</span>
                  </div>
                  {couponValidation && couponValidation.valid && (
                    <div className="flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Cupom aplicado:</span>
                      <span className="font-medium text-green-600 dark:text-green-400">
                        {couponValidation.coupon.code}
                      </span>
                    </div>
                  )}
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Preço atual por usuário:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      R$ {(planData.subscription.pricePerMonth / planData.usage.userLimit).toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Novo preço por usuário:</span>
                    <span className="font-medium text-gray-900 dark:text-white">
                      R$ {(calculateAdditionalCost().costPerAdditionalUser).toFixed(2)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Diferença mensal:</span>
                    <span className={`font-medium ${calculateAdditionalCost().additionalCost < 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                      {calculateAdditionalCost().additionalCost > 0 ? '+' : ''}R$ {Math.abs(calculateAdditionalCost().additionalCost).toFixed(2)}
                    </span>
                  </div>
                  {couponValidation && couponValidation.valid && (
                    <div className="flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Desconto do cupom:</span>
                      <span className="font-medium text-green-600 dark:text-green-400">
                        {couponValidation.coupon.type === 'PERCENT' 
                          ? `${couponValidation.coupon.value}%` 
                          : `R$ ${couponValidation.coupon.value.toFixed(2)}`
                        }
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {calculateAdditionalCost().costPerAdditionalUser < (planData.subscription.pricePerMonth / planData.usage.userLimit) && (
                <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-sm rounded-md">
                  <CheckCircle className="inline-block mr-1 h-4 w-4" />
                  Você receberá um desconto adicional por ter mais usuários!
                </div>
              )}

              {calculateAdditionalCost().costPerAdditionalUser > (planData.subscription.pricePerMonth / planData.usage.userLimit) && (
                <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400 text-sm rounded-md">
                  <AlertTriangle className="inline-block mr-1 h-4 w-4" />
                  O preço por usuário aumentará devido à redução do desconto por volume.
                </div>
              )}

              {planData.usage.currentUsers > (planData.usage.userLimit + additionalUsersCount) && (
                <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 text-sm rounded-md">
                  <AlertTriangle className="inline-block mr-1 h-4 w-4" />
                  O novo limite não pode ser menor que a quantidade atual de usuários ({planData.usage.currentUsers}).
                </div>
              )}
            </div>

            <div className="mt-6 text-center flex justify-center gap-4">
              <button
                onClick={handleCloseAddUsersModal}
                className="inline-flex items-center justify-center px-8 py-3 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleAddUsers}
                disabled={isUpdating || additionalUsersCount <= 0 || isValidatingCoupon || (couponCode.trim() && (!couponValidation || !couponValidation.valid))}
                className="inline-flex items-center justify-center px-8 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors disabled:opacity-50"
              >
                {isValidatingCoupon ? 'Validando cupom...' : 'Confirmar Alteração'}
                <ChevronRight className="ml-2 h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Função auxiliar para obter nome do módulo
const getModuleName = (moduleType) => {
  const moduleNames = {
    'BASIC': 'Módulo Básico',
    'ADMIN': 'Administração',
    'SCHEDULING': 'Agendamento',
    'PEOPLE': 'Pessoas',
    'REPORTS': 'Relatórios',
    'CHAT': 'Chat',
    'ABAPLUS': 'ABA+'
  };
  return moduleNames[moduleType] || moduleType;
};

// Função auxiliar para verificar se é módulo básico
const isBasicModule = (moduleType) => {
  return ['BASIC', 'ADMIN', 'SCHEDULING'].includes(moduleType);
};

export default PlansPage;
