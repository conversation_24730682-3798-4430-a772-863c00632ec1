// src/middlewares/auth.js
const jwt = require('jsonwebtoken');
const prisma = require('../utils/prisma');
const cacheService = require('../services/cacheService');
const subscriptionSyncService = require('../services/subscriptionSyncService');

const MAX_TOKEN_AGE = 24 * 60 * 60; // 24 horas em segundos
const TOKEN_BLACKLIST_PREFIX = 'token:blacklist:';

// Função para verificar se um token está na lista negra
async function isTokenBlacklisted(token) {
  try {
    return await cacheService.get(`${TOKEN_BLACKLIST_PREFIX}${token}`) === true;
  } catch (error) {
    console.error('Erro ao verificar token na lista negra:', error);
    return false;
  }
}

// Função para adicionar um token à lista negra
async function addToBlacklist(token, expiresIn = MAX_TOKEN_AGE) {
  try {
    await cacheService.set(`${TOKEN_BLACKLIST_PREFIX}${token}`, true, expiresIn);
    return true;
  } catch (error) {
    console.error('Erro ao adicionar token à lista negra:', error);
    return false;
  }
}

// Token de teste para ambiente de desenvolvimento e testes
const TEST_TOKEN_PREFIX = 'TEST_TOKEN_';
const isTestEnvironment = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';

const authMiddleware = async (req, res, next) => {
  try {

    // Verifica se o token foi fornecido via header ou query parameter
    const authHeader = req.headers.authorization;
    const tokenFromQuery = req.query.token;
    
    let token;
    
    if (authHeader) {
      const [scheme, headerToken] = authHeader.split(' ');
      
      // Verifica se o token está no formato correto
      if (!/^Bearer$/i.test(scheme)) {
        return res.status(401).json({ message: 'Formato de token inválido' });
      }
      
      token = headerToken;
    } else if (tokenFromQuery) {
      // Usar token do query parameter
      token = tokenFromQuery;
    } else {
      return res.status(401).json({ message: 'Token de autenticação não fornecido' });
    }

    // Verificar se é um token de teste em ambiente de desenvolvimento/teste
    if (isTestEnvironment && token.startsWith(TEST_TOKEN_PREFIX)) {

      // Extrair o ID do usuário do token de teste
      const testUserId = token.replace(TEST_TOKEN_PREFIX, '');

      // Criar um usuário de teste para fins de teste
      const testUser = {
        id: testUserId || '00000000-0000-0000-0000-000000000001',
        login: 'test_user',
        email: '<EMAIL>',
        fullName: 'Test User',
        role: 'SYSTEM_ADMIN',
        active: true,
        modules: ['ADMIN', 'RH', 'FINANCIAL', 'SCHEDULING', 'BASIC'],
        permissions: ['*'],
        createdAt: new Date(),
        companyId: '00000000-0000-0000-0000-000000000001',
        company: {
          id: '00000000-0000-0000-0000-000000000001',
          name: 'Test Company',
          active: true
        }
      };

      // Adicionar usuário de teste à requisição
      req.user = testUser;
      return next();
    }

    // Verifica se o token está na lista negra
    if (await isTokenBlacklisted(token)) {
      return res.status(401).json({ message: 'Token foi revogado' });
    }

    // Verify and decode token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Check token expiration
    const currentTimestamp = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp < currentTimestamp) {
      return res.status(401).json({ message: 'Token expired' });
    }

    // Check if token is for a client or a user
    const isClient = decoded.isClient === true;

    let user;

    if (isClient) {
      // Find client with necessary data
      const client = await prisma.client.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          login: true,
          email: true,
          active: true,
          createdAt: true,
          companyId: true,
          Company: {
            select: {
              id: true,
              name: true,
              active: true,
              licenseValidUntil: true
            }
          }
        }
      });

      // Verify if client exists and is active
      if (!client) {
        return res.status(401).json({ message: 'Client not found' });
      }

      if (!client.active) {
        return res.status(401).json({ message: 'Client account is deactivated' });
      }

      // Verify if company is active
      if (client.companyId && client.Company) {
        if (!client.Company.active) {
          return res.status(401).json({ message: 'Company is deactivated. Contact administrator.' });
        }

        // Check company license
        if (client.Company.licenseValidUntil && new Date(client.Company.licenseValidUntil) < new Date()) {
          return res.status(401).json({ message: 'Company license has expired. Contact administrator.' });
        }
      }

      // Add client to request with isClient flag
      user = {
        ...client,
        isClient: true,
        // Add empty modules and permissions for compatibility
        modules: [],
        permissions: [],
        role: 'CLIENT',
        company: client.Company
      };
    } else {
      // Find user with all necessary data
      user = await prisma.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          login: true,
          email: true,
          fullName: true,
          cpf: true,
          cnpj: true,
          birthDate: true,
          address: true,
          phone: true,
          modules: true,
          permissions: true,
          role: true,
          active: true,
          createdAt: true,
          lastLoginAt: true,
          passwordChangedAt: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              active: true,
              licenseValidUntil: true
            }
          }
        }
      });
      // Verify if user exists and is active
      if (!user) {
        return res.status(401).json({ message: 'User not found' });
      }

      if (!user.active) {
        return res.status(401).json({ message: 'User account is deactivated' });
      }

      // Verify if company is active (except for SYSTEM_ADMIN)
      if (user.companyId && user.role !== 'SYSTEM_ADMIN') {
        if (!user.company.active) {
          return res.status(401).json({ message: 'Company is deactivated. Contact administrator.' });
        }

        // Check company license
        if (user.company.licenseValidUntil && new Date(user.company.licenseValidUntil) < new Date()) {
          return res.status(401).json({ message: 'Company license has expired. Contact administrator.' });
        }
      }

      // Verify if token is issued before password change
      if (user.passwordChangedAt && decoded.iat < Math.floor(user.passwordChangedAt.getTime() / 1000)) {
        return res.status(401).json({ message: 'Password has been changed. Please login again.' });
      }

      // Sincronizar módulos do usuário com a assinatura da empresa (exceto SYSTEM_ADMIN)
      if (user.role !== 'SYSTEM_ADMIN' && user.companyId) {
        try {
          const syncedModules = await subscriptionSyncService.syncUserModulesWithSubscription(user.id, user.companyId);
          user.modules = syncedModules; // Atualizar módulos na sessão atual
        } catch (syncError) {
          console.error('Erro ao sincronizar módulos do usuário:', syncError);
          // Não bloquear a requisição se a sincronização falhar
        }
      }

      // Verify if modules in token match current user modules
      if (JSON.stringify(decoded.modules) !== JSON.stringify(user.modules)) {
        return res.status(401).json({ message: 'Token outdated, please login again' });
      }
    }

    // API access logging removed - no longer creating audit logs for every API call

    // Add user/client to request
    req.user = user;

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expired' });
    }

    console.error('Authentication error:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// Middleware para bloquear acesso se trial expirou e não há licença ativa
async function checkTrialOrLicense(req, res, next) {
  try {
    const user = req.user;
    if (!user || !user.companyId) return res.status(401).json({ message: 'Usuário sem empresa vinculada.' });
    const company = await require('../utils/prisma').company.findUnique({ where: { id: user.companyId } });
    if (!company) return res.status(403).json({ message: 'Empresa não encontrada.' });
    const now = new Date();
    if (company.isTrial && company.trialEnd && now > company.trialEnd) {
      return res.status(402).json({ message: 'Seu período de trial expirou. Realize o pagamento para continuar.' });
    }
    if (!company.active) {
      return res.status(402).json({ message: 'Licença inativa. Realize o pagamento para continuar.' });
    }
    next();
  } catch (err) {
    console.error('Erro no middleware de trial/licença:', err);
    return res.status(500).json({ message: 'Erro ao validar licença/trial.' });
  }
}

// Add utility function to revoke token (for logout)
authMiddleware.revokeToken = async (token) => {
  try {
    // Decodificar o token para obter o tempo de expiração
    const decoded = jwt.verify(token, process.env.JWT_SECRET, { ignoreExpiration: true });

    // Calcular o tempo restante até a expiração
    const currentTime = Math.floor(Date.now() / 1000);
    const expiryTime = decoded.exp || (currentTime + MAX_TOKEN_AGE);
    const timeToLive = Math.max(1, expiryTime - currentTime); // Mínimo de 1 segundo

    // Adicionar à lista negra no Redis
    return await addToBlacklist(token, timeToLive);
  } catch (error) {
    console.error('Erro ao revogar token:', error);
    return false;
  }
};

module.exports = {
  authenticate: authMiddleware,
  isTokenBlacklisted,
  addToBlacklist,
  checkTrialOrLicense,
};