import { usePreferences } from './usePreferences';
import { DataPrivacyUtils, FIELD_MASK_MAPPING } from '@/utils/dataPrivacy';

/**
 * Hook para gerenciar privacidade de dados baseado nas preferências da empresa
 */
export const useDataPrivacy = () => {
  const { preferences, shouldHideField, getPrivacySettings } = usePreferences();

  /**
   * Aplica máscara em um valor específico se necessário
   * @param {string} entityType - Tipo da entidade (user, client, patient)
   * @param {string} fieldName - Nome do campo
   * @param {any} value - Valor original
   * @param {object} options - Opções adicionais
   * @returns {any} - Valor original ou mascarado
   */
  const applyPrivacyMask = (entityType, fieldName, value, options = {}) => {
    if (!value) return value;

    // Verifica se o campo deve ser ocultado
    if (shouldHideField(entityType, fieldName)) {
      // Busca o tipo de máscara baseado no mapeamento
      const hideKey = `hide${entityType.charAt(0).toUpperCase() + entityType.slice(1)}${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)}`;
      const maskType = FIELD_MASK_MAPPING[hideKey];
      
      if (maskType) {
        if (maskType === 'hide') {
          return options.hideCompletely ? '' : '[OCULTO]';
        }
        return DataPrivacyUtils.maskValue(value, maskType);
      }
      
      return DataPrivacyUtils.hideCompletely();
    }

    return value;
  };

  /**
   * Aplica máscaras em um objeto completo baseado no tipo de entidade
   * @param {string} entityType - Tipo da entidade (user, client, patient) 
   * @param {object} data - Dados originais
   * @param {object} options - Opções adicionais
   * @returns {object} - Dados com máscaras aplicadas
   */
  const applyEntityPrivacyMasks = (entityType, data, options = {}) => {
    if (!data || typeof data !== 'object') return data;

    const privacySettings = getPrivacySettings(entityType);
    if (!privacySettings || Object.keys(privacySettings).length === 0) {
      return data;
    }

    const maskedData = { ...data };

    // Mapeamento de campos para cada tipo de entidade
    const fieldMappings = {
      user: {
        hideUserCpf: ['cpf'],
        hideUserCnpj: ['cnpj'],
        hideUserEmail: ['email'],
        hideUserPhone: ['phone'],
        hideUserAddress: ['address', 'city', 'state', 'postalCode', 'neighborhood'],
        hideUserBirthDate: ['birthDate'],
        hideUserLastLoginIp: ['lastLoginIp']
      },
      client: {
        hideClientEmail: ['email'],
        hideClientFullName: ['fullName']
      },
      patient: {
        hidePatientCpf: ['cpf'],
        hidePatientEmail: ['email'],
        hidePatientPhone: ['phone'],
        hidePatientAddress: ['address', 'city', 'state', 'postalCode', 'neighborhood'],
        hidePatientBirthDate: ['birthDate'],
        hidePatientNotes: ['notes'],
        hidePatientProfileImage: ['profileImageUrl']
      }
    };

    const entityMappings = fieldMappings[entityType];
    if (!entityMappings) return maskedData;

    // Aplica máscaras para cada configuração ativa
    Object.keys(privacySettings).forEach(privacyKey => {
      if (privacySettings[privacyKey] === true && entityMappings[privacyKey]) {
        const fieldsToMask = entityMappings[privacyKey];
        const maskType = FIELD_MASK_MAPPING[privacyKey];

        fieldsToMask.forEach(fieldName => {
          if (maskedData[fieldName] !== undefined) {
            if (maskType === 'hide') {
              maskedData[fieldName] = options.hideCompletely ? null : '[OCULTO]';
            } else if (maskType) {
              maskedData[fieldName] = DataPrivacyUtils.maskValue(maskedData[fieldName], maskType);
            } else {
              maskedData[fieldName] = DataPrivacyUtils.hideCompletely();
            }
          }
        });
      }
    });

    return maskedData;
  };

  /**
   * Aplica máscaras em uma lista de entidades
   * @param {string} entityType - Tipo da entidade
   * @param {array} dataList - Lista de dados
   * @param {object} options - Opções adicionais
   * @returns {array} - Lista com máscaras aplicadas
   */
  const applyListPrivacyMasks = (entityType, dataList, options = {}) => {
    if (!Array.isArray(dataList)) return dataList;

    return dataList.map(item => applyEntityPrivacyMasks(entityType, item, options));
  };

  /**
   * Verifica se alguma configuração de privacidade está ativa
   * @param {string} entityType - Tipo da entidade
   * @returns {boolean}
   */
  const hasActivePrivacySettings = (entityType) => {
    const privacySettings = getPrivacySettings(entityType);
    return Object.values(privacySettings).some(value => value === true);
  };

  /**
   * Obtém lista de campos que estão sendo ocultados
   * @param {string} entityType - Tipo da entidade  
   * @returns {array} - Lista de campos ocultados
   */
  const getHiddenFields = (entityType) => {
    const privacySettings = getPrivacySettings(entityType);
    return Object.keys(privacySettings).filter(key => privacySettings[key] === true);
  };

  /**
   * Verifica se um campo específico está sendo ocultado
   * @param {string} entityType - Tipo da entidade
   * @param {string} fieldName - Nome do campo
   * @returns {boolean}
   */
  const isFieldHidden = (entityType, fieldName) => {
    return shouldHideField(entityType, fieldName);
  };

  /**
   * Formata valor para exibição considerando privacidade
   * @param {string} entityType - Tipo da entidade
   * @param {string} fieldName - Nome do campo
   * @param {any} value - Valor original
   * @param {object} options - Opções de formatação
   * @returns {string} - Valor formatado
   */
  const formatSensitiveValue = (entityType, fieldName, value, options = {}) => {
    if (!value) return options.emptyText || '';

    const maskedValue = applyPrivacyMask(entityType, fieldName, value, options);
    
    // Se o valor foi mascarado, pode aplicar styling adicional
    if (maskedValue !== value && options.maskStyle) {
      return {
        value: maskedValue,
        isMasked: true,
        style: options.maskStyle
      };
    }

    return maskedValue;
  };

  return {
    // Funções principais
    applyPrivacyMask,
    applyEntityPrivacyMasks,
    applyListPrivacyMasks,
    
    // Utilitários de verificação
    hasActivePrivacySettings,
    getHiddenFields,
    isFieldHidden,
    
    // Formatação
    formatSensitiveValue,
    
    // Acesso às preferências
    preferences,
    shouldHideField,
    getPrivacySettings
  };
};

export default useDataPrivacy;