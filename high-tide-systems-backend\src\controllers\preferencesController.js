const prisma = require('../utils/prisma');
const DefaultPermissionService = require('../services/defaultPermissionService');

// Buscar preferências da empresa
exports.getPreferences = async (req, res) => {
  try {
    const { companyId } = req.query; // Permitir especificar companyId via query param
    const userCompanyId = req.user.companyId;
    
    // Determinar qual companyId usar
    let targetCompanyId = companyId || userCompanyId;
    
    // Se o usuário é SYSTEM_ADMIN e não especificou uma empresa, retornar preferências padrão
    if (req.user.role === 'SYSTEM_ADMIN' && !targetCompanyId) {
      // Retornar preferências padrão para SYSTEM_ADMIN
      const defaultPreferences = {
        scheduling: {
          allowPastScheduling: false,
          startHour: "08:00",
          endHour: "18:00",
          requiredFields: {
            insurance: true,
            specialty: true,
            serviceType: true,
            location: true,
            professional: true,
            notes: false,
            title: false,
            description: false,
          },
          allowMultipleServices: false,
          requireClientConfirmation: false,
          selectedWeekDays: [1, 2, 3, 4, 5,6],
          requireRecurrence: true,
          requireSequential: true,
          allowedHours: Array.from({ length: 24 }, (_, i) => i >= 8 && i <= 18),
          requireServiceValue: false,
          requireLocationAddress: false,
          requireLocationPhone: false,
          showWorkingHours: true,
          showServiceTypes: true,
          showLocations: true,
          showInsurance: true,
          showInsuranceLimit: true,
        },
        locations: {
          useAvailability: false,
          selectedWeekDays: [1, 2, 3, 4, 5]
        },
        userPrivacy: {
          hideUserCpf: false,
          hideUserCnpj: false,
          hideUserEmail: false,
          hideUserPhone: false,
          hideUserAddress: false,
          hideUserBirthDate: false,
          hideUserLastLoginIp: false
        },
        clientPrivacy: {
          hideClientEmail: false,
          hideClientFullName: false
        },
        patientPrivacy: {
          hidePatientCpf: false,
          hidePatientEmail: false,
          hidePatientPhone: false,
          hidePatientAddress: false,
          hidePatientBirthDate: false,
          hidePatientNotes: false,
          hidePatientProfileImage: false
        }
      };
      
      return res.json(defaultPreferences);
    }
    
    // Se não há companyId válido, retornar erro
    if (!targetCompanyId) {
      return res.status(400).json({ 
        error: 'Company ID é necessário para buscar preferências',
        message: 'Usuários SYSTEM_ADMIN devem especificar uma empresa via query parameter companyId'
      });
    }
    
    // Verificar se o usuário tem permissão para acessar as preferências desta empresa
    if (req.user.role !== 'SYSTEM_ADMIN' && userCompanyId !== targetCompanyId) {
      return res.status(403).json({ 
        error: 'Sem permissão',
        message: 'Você só pode acessar preferências da sua própria empresa'
      });
    }
    
    const pref = await prisma.companyPreference.findUnique({
      where: { companyId: targetCompanyId }
    });
    
    // Se não existem preferências salvas, retornar preferências padrão
    if (!pref || !pref.preferences) {
      const defaultPreferences = {
        scheduling: {
          allowPastScheduling: false,
          startHour: "08:00",
          endHour: "18:00",
          requiredFields: {
            insurance: true,
            specialty: true,
            serviceType: true,
            location: true,
            professional: true,
            notes: false,
            title: false,
            description: false,
          },
          allowMultipleServices: false,
          requireClientConfirmation: false,
          selectedWeekDays: [1, 2, 3, 4, 5],
          requireRecurrence: true,
          requireSequential: true,
          allowedHours: Array.from({ length: 24 }, (_, i) => i >= 8 && i <= 18),
          requireServiceValue: false,
          requireLocationAddress: false,
          requireLocationPhone: false,
          showWorkingHours: true,
          showServiceTypes: true,
          showLocations: true,
          showInsurance: true,
          showInsuranceLimit: true,
        },
        locations: {
          useAvailability: false,
          selectedWeekDays: [1, 2, 3, 4, 5]
        },
        userPrivacy: {
          hideUserCpf: false,
          hideUserCnpj: false,
          hideUserEmail: false,
          hideUserPhone: false,
          hideUserAddress: false,
          hideUserBirthDate: false,
          hideUserLastLoginIp: false
        },
        clientPrivacy: {
          hideClientEmail: false,
          hideClientFullName: false
        },
        patientPrivacy: {
          hidePatientCpf: false,
          hidePatientEmail: false,
          hidePatientPhone: false,
          hidePatientAddress: false,
          hidePatientBirthDate: false,
          hidePatientNotes: false,
          hidePatientProfileImage: false
        }
      };
      
      return res.json(defaultPreferences);
    }
    
    res.json(pref.preferences);
  } catch (error) {
    console.error('Erro ao buscar preferências:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Não foi possível buscar as preferências'
    });
  }
};

// Atualizar preferências da empresa
exports.updatePreferences = async (req, res) => {
  try {
    const { preferences, companyId } = req.body; // Permitir especificar companyId via body
    const userCompanyId = req.user.companyId;
    
    // Determinar qual companyId usar
    let targetCompanyId = companyId || userCompanyId;
    
    // Se o usuário é SYSTEM_ADMIN e não especificou uma empresa, retornar erro
    if (req.user.role === 'SYSTEM_ADMIN' && !targetCompanyId) {
      return res.status(400).json({ 
        error: 'Company ID é necessário',
        message: 'Usuários SYSTEM_ADMIN devem especificar uma empresa via body parameter companyId'
      });
    }
    
    // Se não há companyId válido, retornar erro
    if (!targetCompanyId) {
      return res.status(400).json({ 
        error: 'Company ID é necessário para atualizar preferências',
        message: 'Company ID é obrigatório'
      });
    }
    
    // Verificar se o usuário tem permissão para atualizar as preferências desta empresa
    if (req.user.role !== 'SYSTEM_ADMIN' && userCompanyId !== targetCompanyId) {
      return res.status(403).json({ 
        error: 'Sem permissão',
        message: 'Você só pode atualizar preferências da sua própria empresa'
      });
    }
    
    // Verificar se a empresa existe
    const company = await prisma.company.findUnique({
      where: { id: targetCompanyId }
    });
    
    if (!company) {
      return res.status(404).json({ 
        error: 'Empresa não encontrada',
        message: 'A empresa especificada não existe'
      });
    }
    
    // Buscar preferências antigas para comparação
    const oldPreferences = await prisma.companyPreference.findUnique({
      where: { companyId: targetCompanyId },
      select: { preferences: true, notifications: true }
    });

    const updated = await prisma.companyPreference.upsert({
      where: { companyId: targetCompanyId },
      update: { preferences },
      create: { companyId: targetCompanyId, preferences }
    });

    // Se as preferências de notificação mudaram, sincronizar permissões dos usuários
    if (preferences.notifications && 
        JSON.stringify(oldPreferences?.notifications) !== JSON.stringify(preferences.notifications)) {
      
      console.log('Preferências de notificação alteradas, sincronizando permissões dos usuários...');
      
      // Executar sincronização em background
      DefaultPermissionService.syncNotificationPermissions(targetCompanyId)
        .catch(error => {
          console.error('Erro ao sincronizar permissões de notificação:', error);
        });
    }
    
    res.json(updated.preferences);
  } catch (error) {
    console.error('Erro ao atualizar preferências:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Não foi possível atualizar as preferências'
    });
  }
};

// Buscar preferências de uma empresa específica (SYSTEM_ADMIN apenas)
exports.getPreferencesForCompany = async (req, res) => {
  try {
    const { companyId } = req.params;
    
    // Verificar se a empresa existe
    const company = await prisma.company.findUnique({
      where: { id: companyId }
    });
    
    if (!company) {
      return res.status(404).json({ 
        error: 'Empresa não encontrada',
        message: 'A empresa especificada não existe'
      });
    }
    
    const pref = await prisma.companyPreference.findUnique({
      where: { companyId }
    });
    
    // Se não existem preferências salvas, retornar preferências padrão
    if (!pref || !pref.preferences) {
      const defaultPreferences = {
        scheduling: {
          allowPastScheduling: false,
          startHour: "08:00",
          endHour: "18:00",
          requiredFields: {
            insurance: true,
            specialty: true,
            serviceType: true,
            location: true,
            professional: true,
            notes: false,
            title: false,
            description: false,
          },
          allowMultipleServices: false,
          requireClientConfirmation: false,
          selectedWeekDays: [1, 2, 3, 4, 5],
          requireRecurrence: true,
          requireSequential: true,
          allowedHours: Array.from({ length: 24 }, (_, i) => i >= 8 && i <= 18),
          requireServiceValue: false,
          requireLocationAddress: false,
          requireLocationPhone: false,
          showWorkingHours: true,
          showServiceTypes: true,
          showLocations: true,
          showInsurance: true,
          showInsuranceLimit: true,
        },
        locations: {
          useAvailability: false,
          selectedWeekDays: [1, 2, 3, 4, 5]
        },
        notifications: {
          enabled: {
            NEW_REGISTRATION: true,
            APPOINTMENT_COMING: true,
            NEW_ACCESS: true,
            NEW_BACKUP: true,
            NEW_EXPORT: true,
            SYSTEM_ALERT: true
          },
          required: {
            NEW_REGISTRATION: false,
            APPOINTMENT_COMING: false,
            NEW_ACCESS: false,
            NEW_BACKUP: false,
            NEW_EXPORT: false,
            SYSTEM_ALERT: false
          }
        },
        userPrivacy: {
          hideUserCpf: false,
          hideUserCnpj: false,
          hideUserEmail: false,
          hideUserPhone: false,
          hideUserAddress: false,
          hideUserBirthDate: false,
          hideUserLastLoginIp: false
        },
        clientPrivacy: {
          hideClientEmail: false,
          hideClientFullName: false
        },
        patientPrivacy: {
          hidePatientCpf: false,
          hidePatientEmail: false,
          hidePatientPhone: false,
          hidePatientAddress: false,
          hidePatientBirthDate: false,
          hidePatientNotes: false,
          hidePatientProfileImage: false
        }
      };
      
      return res.json(defaultPreferences);
    }
    
    res.json(pref.preferences);
  } catch (error) {
    console.error('Erro ao buscar preferências da empresa:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Não foi possível buscar as preferências da empresa'
    });
  }
};

// Atualizar preferências de uma empresa específica (SYSTEM_ADMIN apenas)
exports.updatePreferencesForCompany = async (req, res) => {
  try {
    const { companyId } = req.params;
    const { preferences } = req.body;
    
    // Verificar se a empresa existe
    const company = await prisma.company.findUnique({
      where: { id: companyId }
    });
    
    if (!company) {
      return res.status(404).json({ 
        error: 'Empresa não encontrada',
        message: 'A empresa especificada não existe'
      });
    }
    
    // Buscar preferências antigas para comparação
    const oldPreferences = await prisma.companyPreference.findUnique({
      where: { companyId },
      select: { preferences: true, notifications: true }
    });

    const updated = await prisma.companyPreference.upsert({
      where: { companyId },
      update: { preferences },
      create: { companyId, preferences }
    });

    // Se as preferências de notificação mudaram, sincronizar permissões dos usuários
    if (preferences.notifications && 
        JSON.stringify(oldPreferences?.notifications) !== JSON.stringify(preferences.notifications)) {
      
      console.log('Preferências de notificação alteradas, sincronizando permissões dos usuários...');
      
      // Executar sincronização em background
      DefaultPermissionService.syncNotificationPermissions(companyId)
        .catch(error => {
          console.error('Erro ao sincronizar permissões de notificação:', error);
        });
    }
    
    res.json(updated.preferences);
  } catch (error) {
    console.error('Erro ao atualizar preferências da empresa:', error);
    res.status(500).json({ 
      error: 'Erro interno do servidor',
      message: 'Não foi possível atualizar as preferências da empresa'
    });
  }
}; 