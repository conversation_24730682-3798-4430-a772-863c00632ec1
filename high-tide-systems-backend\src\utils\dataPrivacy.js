/**
 * Utilitários para mascarar dados sensíveis no backend
 * Baseado nas configurações de privacidade da empresa
 */

class DataPrivacyUtils {
  /**
   * Mascara CPF/CNPJ
   * @param {string} document - CPF ou CNPJ
   * @returns {string} - Documento mascarado
   */
  static maskDocument(document) {
    if (!document) return '';
    
    const cleanDoc = document.replace(/\D/g, '');
    
    if (cleanDoc.length === 11) {
      // CPF: 123.456.789-10 -> ***.***.***-10
      return cleanDoc.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '***.***.$3-$4');
    } else if (cleanDoc.length === 14) {
      // CNPJ: 12.345.678/0001-90 -> **.***.***/****-**
      return cleanDoc.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '**.***.***/****-**');
    }
    
    return '***';
  }

  /**
   * Mascara e-mail
   * @param {string} email - E-mail
   * @returns {string} - E-mail mascarado
   */
  static maskEmail(email) {
    if (!email || !email.includes('@')) return '***@***.***';
    
    const [localPart, domain] = email.split('@');
    const [domainName, extension] = domain.split('.');
    
    const maskedLocal = localPart.length > 2 
      ? localPart.substring(0, 2) + '*'.repeat(localPart.length - 2)
      : '***';
      
    const maskedDomain = domainName.length > 2
      ? domainName.substring(0, 1) + '*'.repeat(domainName.length - 2) + domainName.slice(-1)
      : '***';
    
    return `${maskedLocal}@${maskedDomain}.${extension || '***'}`;
  }

  /**
   * Mascara telefone
   * @param {string} phone - Telefone
   * @returns {string} - Telefone mascarado
   */
  static maskPhone(phone) {
    if (!phone) return '';
    
    const cleanPhone = phone.replace(/\D/g, '');
    
    if (cleanPhone.length === 11) {
      // Celular: (11) 99999-9999 -> (11) ****-9999
      return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) ****-$3');
    } else if (cleanPhone.length === 10) {
      // Fixo: (11) 9999-9999 -> (11) ****-9999
      return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) ****-$3');
    }
    
    return '(***) ****-****';
  }

  /**
   * Mascara endereço
   * @param {string} address - Endereço
   * @returns {string} - Endereço mascarado
   */
  static maskAddress(address) {
    if (!address) return '';
    
    // Mantém apenas os primeiros 10 caracteres e mascara o resto
    if (address.length <= 10) {
      return '*'.repeat(address.length);
    }
    
    return address.substring(0, 10) + '*'.repeat(Math.max(0, address.length - 10));
  }

  /**
   * Mascara data de nascimento
   * @param {string|Date} birthDate - Data de nascimento
   * @returns {string} - Data mascarada
   */
  static maskBirthDate(birthDate) {
    if (!birthDate) return '';
    
    const date = new Date(birthDate);
    if (isNaN(date.getTime())) return '***';
    
    // Mascara completamente a data
    return '**/**/**';
  }

  /**
   * Mascara IP
   * @param {string} ip - Endereço IP
   * @returns {string} - IP mascarado
   */
  static maskIp(ip) {
    if (!ip) return '';
    
    const parts = ip.split('.');
    if (parts.length === 4) {
      // Mantém apenas o primeiro octeto
      return `${parts[0]}.***.***.***`;
    }
    
    return '***.***.***.***';
  }

  /**
   * Mascara nome completo
   * @param {string} fullName - Nome completo
   * @returns {string} - Nome mascarado
   */
  static maskFullName(fullName) {
    if (!fullName) return '';
    
    const names = fullName.trim().split(' ');
    
    if (names.length === 1) {
      // Apenas um nome - mantém primeira e última letra
      if (names[0].length <= 2) return '*'.repeat(names[0].length);
      return names[0][0] + '*'.repeat(names[0].length - 2) + names[0].slice(-1);
    }
    
    // Múltiplos nomes - mantém primeiro nome e última inicial
    const firstName = names[0];
    const lastInitial = names[names.length - 1][0];
    
    return `${firstName} ${'*'.repeat(names.length - 2)} ${lastInitial}.`;
  }

  /**
   * Mascara observações/notas
   * @param {string} notes - Observações
   * @returns {string} - Observações mascaradas
   */
  static maskNotes(notes) {
    if (!notes) return '';
    
    // Mantém apenas os primeiros 20 caracteres se for muito longo
    if (notes.length <= 20) {
      return '*'.repeat(notes.length);
    }
    
    return notes.substring(0, 20) + '... [DADOS SENSÍVEIS OCULTOS]';
  }

  /**
   * Retorna string para ocultar completamente
   * @returns {string}
   */
  static hideCompletely() {
    return '[OCULTO]';
  }

  /**
   * Aplica máscara baseado no tipo
   * @param {any} value - Valor a ser mascarado
   * @param {string} type - Tipo de máscara
   * @returns {string} - Valor mascarado
   */
  static maskValue(value, type) {
    if (!value) return '';
    
    switch (type.toLowerCase()) {
      case 'cpf':
      case 'cnpj':
      case 'document':
        return this.maskDocument(value);
      case 'email':
        return this.maskEmail(value);
      case 'phone':
      case 'telefone':
        return this.maskPhone(value);
      case 'address':
      case 'endereco':
        return this.maskAddress(value);
      case 'birthdate':
      case 'nascimento':
        return this.maskBirthDate(value);
      case 'ip':
        return this.maskIp(value);
      case 'fullname':
      case 'nome':
        return this.maskFullName(value);
      case 'notes':
      case 'observacoes':
        return this.maskNotes(value);
      case 'hide':
        return this.hideCompletely();
      default:
        return this.hideCompletely();
    }
  }

  /**
   * Aplica máscaras de privacidade em um objeto baseado nas configurações
   * @param {string} entityType - Tipo da entidade (user, client, patient)
   * @param {object} data - Dados originais
   * @param {object} privacySettings - Configurações de privacidade
   * @returns {object} - Dados com máscaras aplicadas
   */
  static applyPrivacyMasks(entityType, data, privacySettings) {
    if (!data || typeof data !== 'object' || !privacySettings) {
      return data;
    }

    const privacySection = `${entityType}Privacy`;
    const entityPrivacySettings = privacySettings[privacySection];

    if (!entityPrivacySettings || Object.keys(entityPrivacySettings).length === 0) {
      return data;
    }

    const maskedData = { ...data };

    // Mapeamento de campos para cada tipo de entidade
    const fieldMappings = {
      user: {
        hideUserCpf: [{ fields: ['cpf'], type: 'cpf' }],
        hideUserCnpj: [{ fields: ['cnpj'], type: 'cnpj' }],
        hideUserEmail: [{ fields: ['email'], type: 'email' }],
        hideUserPhone: [{ fields: ['phone'], type: 'phone' }],
        hideUserAddress: [{ fields: ['address', 'city', 'state', 'postalCode'], type: 'address' }],
        hideUserBirthDate: [{ fields: ['birthDate'], type: 'birthdate' }],
        hideUserLastLoginIp: [{ fields: ['lastLoginIp'], type: 'ip' }]
      },
      client: {
        hideClientEmail: [{ fields: ['email'], type: 'email' }],
        hideClientFullName: [{ fields: ['fullName'], type: 'fullname' }]
      },
      patient: {
        hidePatientCpf: [{ fields: ['cpf'], type: 'cpf' }],
        hidePatientEmail: [{ fields: ['email'], type: 'email' }],
        hidePatientPhone: [{ fields: ['phone'], type: 'phone' }],
        hidePatientAddress: [{ fields: ['address', 'city', 'state', 'postalCode'], type: 'address' }],
        hidePatientBirthDate: [{ fields: ['birthDate'], type: 'birthdate' }],
        hidePatientNotes: [{ fields: ['notes'], type: 'notes' }],
        hidePatientProfileImage: [{ fields: ['profileImageUrl', 'profileImageFullUrl'], type: 'hide' }]
      }
    };

    const entityMappings = fieldMappings[entityType];
    if (!entityMappings) return maskedData;

    // Aplica máscaras para cada configuração ativa
    Object.keys(entityPrivacySettings).forEach(privacyKey => {
      if (entityPrivacySettings[privacyKey] === true && entityMappings[privacyKey]) {
        const mappings = entityMappings[privacyKey];

        mappings.forEach(mapping => {
          mapping.fields.forEach(fieldName => {
            if (maskedData[fieldName] !== undefined && maskedData[fieldName] !== null) {
              if (mapping.type === 'hide') {
                maskedData[fieldName] = null;
              } else {
                maskedData[fieldName] = this.maskValue(maskedData[fieldName], mapping.type);
              }
            }
          });
        });
      }
    });

    return maskedData;
  }

  /**
   * Aplica máscaras em uma lista de entidades
   * @param {string} entityType - Tipo da entidade
   * @param {array} dataList - Lista de dados
   * @param {object} privacySettings - Configurações de privacidade
   * @returns {array} - Lista com máscaras aplicadas
   */
  static applyPrivacyMasksToList(entityType, dataList, privacySettings) {
    if (!Array.isArray(dataList)) return dataList;

    return dataList.map(item => this.applyPrivacyMasks(entityType, item, privacySettings));
  }

  /**
   * Verifica se há configurações de privacidade ativas para uma entidade
   * @param {string} entityType - Tipo da entidade
   * @param {object} privacySettings - Configurações de privacidade
   * @returns {boolean}
   */
  static hasActivePrivacySettings(entityType, privacySettings) {
    if (!privacySettings) return false;
    
    const privacySection = `${entityType}Privacy`;
    const entityPrivacySettings = privacySettings[privacySection];
    
    if (!entityPrivacySettings) return false;
    
    return Object.values(entityPrivacySettings).some(value => value === true);
  }
}

module.exports = DataPrivacyUtils;