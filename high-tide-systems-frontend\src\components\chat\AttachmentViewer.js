'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  File, FileText, Image, FileVideo, FileAudio, Archive,
  Download, ExternalLink, X, Eye
} from 'lucide-react';
import { getApiUrl } from '@/utils/api';

const AttachmentViewer = ({ attachment, compact = false }) => {
  const [showImageModal, setShowImageModal] = useState(false);
  const [showPdfModal, setShowPdfModal] = useState(false);
  const [imageBlob, setImageBlob] = useState(null);
  const [pdfBlob, setPdfBlob] = useState(null);
  const [imageLoading, setImageLoading] = useState(false);
  const [pdfLoading, setPdfLoading] = useState(false);
  
  // Determinar o tipo de arquivo baseado no MIME type
  const getFileType = (mimetype) => {
    if (!mimetype) return 'unknown';
    
    if (mimetype.startsWith('image/')) return 'image';
    if (mimetype.startsWith('video/')) return 'video';
    if (mimetype.startsWith('audio/')) return 'audio';
    if (mimetype.includes('pdf')) return 'pdf';
    if (mimetype.includes('text') || mimetype.includes('json') || mimetype.includes('xml')) return 'text';
    if (mimetype.includes('zip') || mimetype.includes('rar') || mimetype.includes('tar') || mimetype.includes('gz')) return 'archive';
    if (mimetype.includes('word') || mimetype.includes('document')) return 'document';
    if (mimetype.includes('sheet') || mimetype.includes('excel')) return 'spreadsheet';
    if (mimetype.includes('presentation') || mimetype.includes('powerpoint')) return 'presentation';
    
    return 'unknown';
  };

  // Função para verificar se o arquivo pode ser visualizado no browser
  const canPreviewInBrowser = (mimetype) => {
    if (!mimetype) return false;
    
    const previewableTypes = [
      'image/', 'text/', 'application/pdf', 'application/json',
      'video/mp4', 'video/webm', 'audio/mp3', 'audio/wav', 'audio/ogg'
    ];
    
    return previewableTypes.some(type => mimetype.startsWith(type) || mimetype.includes(type));
  };

  // Função para formatar o tamanho do arquivo
  const formatFileSize = (bytes) => {
    if (!bytes) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // Função para obter o ícone baseado no tipo de arquivo
  const getFileIcon = (fileType, size = 20) => {
    const iconProps = { size, className: "text-gray-600 dark:text-gray-400" };
    
    switch (fileType) {
      case 'image':
        return <Image {...iconProps} className="text-green-600 dark:text-green-400" />;
      case 'video':
        return <FileVideo {...iconProps} className="text-red-600 dark:text-red-400" />;
      case 'audio':
        return <FileAudio {...iconProps} className="text-purple-600 dark:text-purple-400" />;
      case 'pdf':
        return <FileText {...iconProps} className="text-red-600 dark:text-red-400" />;
      case 'text':
        return <FileText {...iconProps} className="text-blue-600 dark:text-blue-400" />;
      case 'archive':
        return <Archive {...iconProps} className="text-yellow-600 dark:text-yellow-400" />;
      default:
        return <File {...iconProps} />;
    }
  };

  // Função para obter a cor de fundo baseada no tipo de arquivo
  const getBackgroundColor = (fileType) => {
    switch (fileType) {
      case 'image':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800';
      case 'video':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
      case 'audio':
        return 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800';
      case 'pdf':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
      case 'text':
        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800';
      case 'archive':
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
      default:
        return 'bg-gray-50 dark:bg-gray-800/50 border-gray-200 dark:border-gray-700';
    }
  };

  // Carregar imagem com autenticação
  const loadImageWithAuth = async (attachmentId) => {
    try {
      setImageLoading(true);
      const token = localStorage.getItem('token');
      const url = getApiUrl(`chat/documents/${attachmentId}/download`);

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao carregar imagem');
      }

      const blob = await response.blob();
      const imageUrl = URL.createObjectURL(blob);
      setImageBlob(imageUrl);
    } catch (error) {
      console.error('Erro ao carregar imagem:', error);
    } finally {
      setImageLoading(false);
    }
  };

  // Carregar PDF com autenticação
  const loadPdfWithAuth = async (attachmentId) => {
    try {
      setPdfLoading(true);
      const token = localStorage.getItem('token');
      const url = getApiUrl(`chat/documents/${attachmentId}/download`);

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao carregar PDF');
      }

      const blob = await response.blob();
      const pdfUrl = URL.createObjectURL(blob);
      setPdfBlob(pdfUrl);
    } catch (error) {
      console.error('Erro ao carregar PDF:', error);
    } finally {
      setPdfLoading(false);
    }
  };

  // Função para lidar com clique no anexo
  const handleAttachmentClick = () => {
    console.log('AttachmentViewer: Clique no anexo');

    const fileType = getFileType(attachment.mimetype);

    if (fileType === 'image') {
      console.log('AttachmentViewer: Abrindo modal de imagem');
      setShowImageModal(true);
      return;
    }

    if (fileType === 'pdf') {
      console.log('AttachmentViewer: Abrindo modal de PDF');
      setShowPdfModal(true);
      return;
    }

    if (canPreviewInBrowser(attachment.mimetype)) {
      // Para arquivos que podem ser visualizados no browser, fazer download com auth e abrir
      const token = localStorage.getItem('token');
      const url = getApiUrl(`chat/documents/${attachment.id}/download`);

      fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => response.blob())
      .then(blob => {
        const blobUrl = URL.createObjectURL(blob);
        window.open(blobUrl, '_blank');
      })
      .catch(error => {
        console.error('Erro ao abrir arquivo:', error);
      });
    } else {
      handleDownload();
    }
  };

  // Função para fazer download
  const handleDownload = async () => {
    try {
      const token = localStorage.getItem('token');
      const url = getApiUrl(`chat/documents/${attachment.id}/download`);

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Erro ao fazer download');
      }

      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = attachment.originalName || attachment.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Erro ao fazer download:', error);
    }
  };

  const fileType = getFileType(attachment.mimetype);
  const isImage = fileType === 'image';
  const isPdf = fileType === 'pdf';

  // Carregar imagem se for uma imagem
  useEffect(() => {
    if (isImage && !imageBlob) {
      loadImageWithAuth(attachment.id);
    }
  }, [isImage, attachment.id, imageBlob]);

  // Carregar PDF se for um PDF
  useEffect(() => {
    if (isPdf && !pdfBlob) {
      loadPdfWithAuth(attachment.id);
    }
  }, [isPdf, attachment.id, pdfBlob]);

  if (compact) {
    // Para imagens, mostrar preview compacto
    if (isImage) {
      return (
        <>
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="relative max-w-xs cursor-pointer group"
            onClick={handleAttachmentClick}
            title="Clique para visualizar em tela cheia"
          >
            <div className="relative rounded-lg overflow-hidden">
              {imageLoading ? (
                <div className="w-full h-32 bg-gray-200 dark:bg-gray-700 flex items-center justify-center rounded-lg">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-500"></div>
                </div>
              ) : imageBlob ? (
                <img
                  src={imageBlob}
                  alt={attachment.originalName || attachment.filename}
                  className="w-full h-32 object-cover rounded-lg transition-transform duration-200 group-hover:scale-105"
                />
              ) : (
                <div className="w-full h-32 bg-gray-200 dark:bg-gray-700 flex items-center justify-center rounded-lg">
                  <Image size={24} className="text-gray-400" />
                </div>
              )}
              
              {/* Overlay de hover */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100 rounded-lg">
                <div className="bg-white/90 dark:bg-gray-800/90 rounded-full p-2">
                  <Eye size={16} className="text-gray-700 dark:text-gray-300" />
                </div>
              </div>
            </div>
            
            {/* Nome do arquivo abaixo da imagem */}
            <div className="mt-2 px-1">
              <p className="text-xs font-medium text-gray-700 dark:text-gray-300 truncate">
                {attachment.originalName || attachment.filename}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formatFileSize(attachment.size)}
              </p>
            </div>
          </motion.div>
        </>
      );
    }

    // Para PDFs, mostrar preview compacto
    if (isPdf) {
      return (
        <>
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="relative max-w-xs cursor-pointer group"
            onClick={handleAttachmentClick}
            title="Clique para visualizar em tela cheia"
          >
            <div className="relative rounded-lg overflow-hidden">
              {pdfLoading ? (
                <div className="w-full h-32 bg-gray-200 dark:bg-gray-700 flex items-center justify-center rounded-lg">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-500"></div>
                </div>
              ) : pdfBlob ? (
                <div className="w-full h-32 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center relative overflow-hidden">
                  <iframe
                    src={`${pdfBlob}#toolbar=0&navpanes=0&scrollbar=0`}
                    className="w-full h-full scale-150 origin-top-left pointer-events-none"
                    style={{ transform: 'scale(1.5) translateX(-16.67%) translateY(-16.67%)' }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-gray-200/50 to-transparent dark:from-gray-800/50"></div>
                </div>
              ) : (
                <div className="w-full h-32 bg-gray-200 dark:bg-gray-700 flex items-center justify-center rounded-lg">
                  <FileText size={24} className="text-gray-400" />
                </div>
              )}

              {/* Overlay de hover */}
              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100 rounded-lg">
                <div className="bg-white/90 dark:bg-gray-800/90 rounded-full p-2">
                  <Eye size={16} className="text-gray-700 dark:text-gray-300" />
                </div>
              </div>
            </div>

            {/* Nome do arquivo abaixo do PDF */}
            <div className="mt-2 px-1">
              <p className="text-xs font-medium text-gray-700 dark:text-gray-300 truncate">
                {attachment.originalName || attachment.filename}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formatFileSize(attachment.size)}
              </p>
            </div>
          </motion.div>

          {/* Modal de visualização de PDF */}
          {showPdfModal && isPdf && (
            <AnimatePresence>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[9999] flex items-center justify-center p-4"
                onClick={() => setShowPdfModal(false)}
              >
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.8, opacity: 0 }}
                  className="relative w-full max-w-6xl h-full max-h-[90vh] bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-2xl"
                  onClick={(e) => e.stopPropagation()}
                >
                  {/* Header do modal */}
                  <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-3">
                      <FileText size={20} className="text-gray-500" />
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">
                          {attachment.originalName || attachment.filename}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {formatFileSize(attachment.size)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <button
                        onClick={handleDownload}
                        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        title="Download"
                      >
                        <Download size={18} />
                      </button>

                      <button
                        onClick={() => setShowPdfModal(false)}
                        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        title="Fechar"
                      >
                        <X size={18} />
                      </button>
                    </div>
                  </div>

                  {/* Conteúdo do PDF */}
                  <div className="relative h-full">
                    {pdfBlob ? (
                      <iframe
                        src={pdfBlob}
                        className="w-full h-full"
                        title={attachment.originalName || attachment.filename}
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-500"></div>
                      </div>
                    )}
                  </div>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          )}

          {/* Modal de visualização de imagem */}
          {showImageModal && isImage && (
            <AnimatePresence>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[9999] flex items-center justify-center p-4"
                onClick={() => setShowImageModal(false)}
              >
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.8, opacity: 0 }}
                  className="relative max-w-4xl max-h-full bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-2xl"
                  onClick={(e) => e.stopPropagation()}
                >
                  {/* Header do modal */}
                  <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-3">
                      <Image size={20} className="text-gray-500" />
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-gray-100">
                          {attachment.originalName || attachment.filename}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {formatFileSize(attachment.size)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <button
                        onClick={handleDownload}
                        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        title="Download"
                      >
                        <Download size={18} />
                      </button>

                      <button
                        onClick={() => setShowImageModal(false)}
                        className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        title="Fechar"
                      >
                        <X size={18} />
                      </button>
                    </div>
                  </div>

                  {/* Conteúdo da imagem */}
                  <div className="relative overflow-auto max-h-[70vh] flex items-center justify-center p-4">
                    {imageBlob ? (
                      <img
                        src={imageBlob}
                        alt={attachment.originalName || attachment.filename}
                        className="max-w-full max-h-full object-contain"
                      />
                    ) : (
                      <div className="flex items-center justify-center p-8">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-500"></div>
                      </div>
                    )}
                  </div>
                </motion.div>
              </motion.div>
            </AnimatePresence>
          )}
        </>
      );
    }

    // Para outros tipos de arquivo, manter o layout atual
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg border ${getBackgroundColor(fileType)} max-w-xs cursor-pointer hover:shadow-md transition-all duration-200`}
        onClick={handleAttachmentClick}
        title={
          canPreviewInBrowser(attachment.mimetype)
            ? 'Clique para abrir em nova aba'
            : 'Clique para fazer download'
        }
      >
        {getFileIcon(fileType, 16)}
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate">
            {attachment.originalName || attachment.filename}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {formatFileSize(attachment.size)}
          </p>
        </div>
        <div className="flex items-center gap-1">
          {canPreviewInBrowser(attachment.mimetype) && <ExternalLink size={12} className="text-blue-500" />}
          {!canPreviewInBrowser(attachment.mimetype) && <Download size={12} className="text-gray-500" />}
        </div>
      </motion.div>
    );
  }

  // Modo não-compact - layout padrão
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={`rounded-xl border p-4 ${getBackgroundColor(fileType)} max-w-sm`}
    >
      <div className="flex items-center gap-3">
        {getFileIcon(fileType, 20)}
        <div className="flex-1 min-w-0 cursor-pointer" onClick={handleAttachmentClick}>
          <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate hover:text-cyan-600 dark:hover:text-cyan-400 transition-colors">
            {attachment.originalName || attachment.filename}
          </h4>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {formatFileSize(attachment.size)}
          </p>
        </div>
        <div className="flex items-center gap-1">
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleAttachmentClick}
            className="p-2 hover:bg-white/50 dark:hover:bg-gray-700/50 rounded-lg transition-colors"
          >
            {canPreviewInBrowser(attachment.mimetype) ? <ExternalLink size={16} /> : <Download size={16} />}
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default AttachmentViewer;
