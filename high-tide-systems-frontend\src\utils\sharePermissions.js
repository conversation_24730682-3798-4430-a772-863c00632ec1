// src/utils/sharePermissions.js

/**
 * Utilitários para verificar permissões de compartilhamento
 */

import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';
import { personsService } from '@/app/modules/people/services/personsService';
import { insuranceServiceLimitService } from '@/app/modules/people/services/insuranceServiceLimitService';

/**
 * Obtém os IDs dos clientes relacionados a um item específico
 * @param {string} itemType - Tipo do item (appointment, person, client, etc.)
 * @param {string} itemId - ID do item
 * @returns {Promise<string[]>} - Array com IDs dos clientes relacionados
 */
export const getRelatedClientIds = async (itemType, itemId) => {
  try {
    switch (itemType) {
      case 'appointment':
        return await getAppointmentRelatedClients(itemId);
      
      case 'person':
        return await getPersonRelatedClients(itemId);
      
      case 'client':
        // O próprio cliente é o relacionado
        return [itemId];
      
      case 'insurance-limit':
        return await getInsuranceLimitRelatedClients(itemId);
      
      case 'user':
      case 'serviceType':
      case 'location':
      case 'workingHours':
      case 'insurance':
        // Estes tipos são recursos da empresa, não específicos de clientes
        // Retornar array vazio significa que não há restrição de cliente
        return [];
      
      default:
        console.warn(`Tipo de item não reconhecido para verificação de permissões: ${itemType}`);
        return [];
    }
  } catch (error) {
    console.error('Erro ao obter clientes relacionados ao item:', error);
    return [];
  }
};

/**
 * Obtém clientes relacionados a um agendamento
 */
const getAppointmentRelatedClients = async (appointmentId) => {
  try {
    const appointment = await appointmentService.getAppointmentById(appointmentId);
    if (!appointment) return [];

    const clientIds = [];

    // Cliente direto do agendamento
    if (appointment.clientId) {
      clientIds.push(appointment.clientId);
    }

    // Clientes relacionados às pessoas do agendamento
    if (appointment.Person && Array.isArray(appointment.Person)) {
      for (const person of appointment.Person) {
        if (person.clientPersons && Array.isArray(person.clientPersons)) {
          const personClientIds = person.clientPersons.map(cp => cp.clientId);
          clientIds.push(...personClientIds);
        }
      }
    }

    // Remover duplicatas
    return [...new Set(clientIds.filter(Boolean))];
  } catch (error) {
    console.error('Erro ao obter clientes do agendamento:', error);
    return [];
  }
};

/**
 * Obtém clientes relacionados a uma pessoa
 */
const getPersonRelatedClients = async (personId) => {
  try {
    const person = await personsService.getPerson(personId);
    if (!person || !person.clientPersons) return [];

    return person.clientPersons.map(cp => cp.clientId).filter(Boolean);
  } catch (error) {
    console.error('Erro ao obter clientes da pessoa:', error);
    return [];
  }
};

/**
 * Obtém clientes relacionados a um limite de convênio
 */
const getInsuranceLimitRelatedClients = async (limitId) => {
  try {
    const limit = await insuranceServiceLimitService.getInsuranceLimit(limitId);
    if (!limit || !limit.Person) return [];

    if (limit.Person.clientPersons && Array.isArray(limit.Person.clientPersons)) {
      return limit.Person.clientPersons.map(cp => cp.clientId).filter(Boolean);
    }

    return [];
  } catch (error) {
    console.error('Erro ao obter clientes do limite de convênio:', error);
    return [];
  }
};

/**
 * Filtra conversas baseado nos clientes relacionados ao item
 * REGRA: Usuários podem compartilhar qualquer coisa entre si, mas com clientes apenas itens relacionados
 * @param {Array} conversations - Lista de conversas
 * @param {string[]} relatedClientIds - IDs dos clientes relacionados ao item
 * @returns {Array} - Conversas filtradas
 */
export const filterConversationsByRelatedClients = (conversations, relatedClientIds) => {
  // Se não há clientes relacionados específicos, permitir todas as conversas
  // (recursos da empresa podem ser compartilhados livremente com qualquer um)
  if (!relatedClientIds || relatedClientIds.length === 0) {
    return conversations;
  }

  return conversations.filter(conversation => {
    // Obter clientes participantes da conversa
    const conversationClientIds = conversation.participants
      ?.filter(p => p.clientId && !p.leftAt)
      .map(p => p.clientId) || [];

    // Se a conversa NÃO tem clientes (só usuários internos), SEMPRE permitir
    // Usuários podem compartilhar qualquer coisa entre si
    if (conversationClientIds.length === 0) {
      return true;
    }

    // Se a conversa TEM clientes, verificar se há interseção entre clientes da conversa e relacionados ao item
    // Permitir se pelo menos um cliente da conversa está relacionado ao item
    return conversationClientIds.some(clientId => relatedClientIds.includes(clientId));
  });
};

/**
 * Verifica se um usuário pode ser adicionado a uma nova conversa para compartilhamento
 * REGRA: Usuários internos podem receber qualquer compartilhamento, clientes apenas itens relacionados
 * @param {Object} user - Usuário a ser verificado
 * @param {string[]} relatedClientIds - IDs dos clientes relacionados ao item
 * @returns {boolean} - Se o usuário pode ser adicionado
 */
export const canShareWithUser = (user, relatedClientIds) => {
  // Usuários internos (não-clientes) SEMPRE podem receber compartilhamentos
  // Não há restrição entre usuários da empresa
  if (user.role !== 'CLIENT' && !user.isClient) {
    return true;
  }
  
  // Se não há clientes relacionados específicos, permitir compartilhamento com qualquer cliente
  // (recursos da empresa podem ser compartilhados livremente)
  if (!relatedClientIds || relatedClientIds.length === 0) {
    return true;
  }

  // Se o usuário é um cliente, verificar se está na lista de clientes relacionados ao item
  return relatedClientIds.includes(user.id);
};