'use client';

import React, { useState, useEffect } from 'react';
import { Search, RefreshCw, Filter, Tag, Building, DollarSign } from 'lucide-react';
import { FilterButton } from '@/components/ui/ModuleHeader';
import MultiSelect from '@/components/ui/multi-select';
import { ModuleInput } from '@/components/ui';
import { serviceTypeService } from '@/app/modules/scheduler/services/serviceTypeService';
import { companyService } from '@/app/modules/admin/services/companyService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';

export const ServiceTypesFilters = ({
    filters = {},
    onFiltersChange,
    onSearch,
    isLoading = false
}) => {
    const [serviceTypes, setServiceTypes] = useState([]);
    const [companies, setCompanies] = useState([]);
    const [isLoadingOptions, setIsLoadingOptions] = useState(true);
    const [isFilterExpanded, setIsFilterExpanded] = useState(false);
    const [error, setError] = useState(null);

    const { user } = useAuth();
    const { toast_error } = useToast();
    const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

    useEffect(() => {
        const loadFilterData = async () => {
            try {
                setError(null);
                setIsLoadingOptions(true);

                // Carregar todas as opções necessárias
                const [
                    serviceTypesData,
                    companiesData
                ] = await Promise.all([
                    serviceTypeService.getServiceTypes({
                        companyId: !isSystemAdmin ? user?.companyId : undefined,
                        limit: 1000 // Pegar todos para o filtro
                    }),
                    isSystemAdmin ? companyService.getCompaniesForSelect() : Promise.resolve([])
                ]);

                // Formatar tipos de serviço
                const formattedServiceTypes = (serviceTypesData?.serviceTypes || [])
                    .filter(st => st && st.id && st.name)
                    .map(serviceType => ({
                        value: serviceType.id,
                        label: serviceType.name
                    }));
                setServiceTypes(formattedServiceTypes);

                // Formatar empresas
                if (isSystemAdmin) {
                    const formattedCompanies = (companiesData || [])
                        .filter(c => c && c.id && c.name)
                        .map(company => ({
                            value: company.id,
                            label: company.name
                        }));
                    setCompanies(formattedCompanies);
                }



            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                setError('Falha ao carregar opções. Por favor, tente novamente.');
                toast_error({
                    title: "Erro ao carregar filtros",
                    message: error.message || "Ocorreu um erro ao carregar os filtros"
                });
            } finally {
                setIsLoadingOptions(false);
            }
        };

        loadFilterData();
    }, [isSystemAdmin, user?.companyId]);

    const handleFilterChange = (newFilters) => {
        onFiltersChange(newFilters);
    };

    const handleSearch = (e) => {
        if (e) {
            e.preventDefault();
        }
        onSearch(filters);
    };

    const handleClearFilters = () => {
        const clearedFilters = {
            search: "",
            serviceTypes: [],
            companies: [],
            minValue: null,
            maxValue: null
        };
        onFiltersChange(clearedFilters);
        onSearch(clearedFilters);
    };

    const getActiveFiltersCount = () => {
        return Object.entries(filters).filter(([key, value]) => {
            if (key === 'search') return false;
            if (Array.isArray(value)) return value.length > 0;
            return value !== null && value !== '';
        }).length;
    };

    return (
        <div className="space-y-4">
            {error && (
                <div className="p-3 mb-4 bg-error-50 dark:bg-error-900/20 text-error-700 dark:text-error-300 rounded-lg border border-error-200 dark:border-error-700">
                    {error}
                </div>
            )}

            {/* Barra de pesquisa e botões */}
            <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-3 md:items-center">
                <div className="flex-1">
                    <div className="relative">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <Search className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                            type="text"
                            placeholder="Buscar tipos de serviço..."
                            value={filters.search || ""}
                            onChange={(e) => handleFilterChange({ ...filters, search: e.target.value })}
                            className="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-module-scheduler-primary focus:border-module-scheduler-primary dark:bg-gray-700 dark:text-white"
                        />
                    </div>
                </div>

                <div className="flex gap-2">
                    <FilterButton
                        type="button"
                        onClick={() => setIsFilterExpanded(!isFilterExpanded)}
                        moduleColor="scheduler"
                        variant="secondary"
                    >
                        <div className="flex items-center gap-2">
                            <Filter size={16} className="text-gray-600 dark:text-gray-400" />
                            <span>Filtros</span>
                            <span className="bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full text-xs">
                                {getActiveFiltersCount()}
                            </span>
                        </div>
                    </FilterButton>

                    <FilterButton
                        type="submit"
                        moduleColor="scheduler"
                        variant="primary"
                        disabled={isLoading}
                    >
                        <div className="flex items-center gap-2">
                            <Search size={16} />
                            <span>{isLoading ? "Buscando..." : "Buscar"}</span>
                        </div>
                    </FilterButton>
                </div>
            </form>

            {/* Filtros avançados (expansíveis) */}
            {isFilterExpanded && (
                <div className="pt-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
                    <div className={`grid grid-cols-1 gap-4 ${isSystemAdmin ? 'md:grid-cols-2' : 'md:grid-cols-1'}`}>
                        {isSystemAdmin && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                                    <Building size={16} className="text-purple-600 dark:text-purple-400" />
                                    Empresas
                                </label>
                                <MultiSelect
                                    options={companies}
                                    value={filters.companies || []}
                                    onChange={(selected) => handleFilterChange({ ...filters, companies: selected })}
                                    placeholder="Selecione as empresas"
                                    className="w-full"
                                    moduleOverride="scheduler"
                                    loading={isLoadingOptions}
                                />
                            </div>
                        )}

                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                                <Tag size={16} className="text-purple-600 dark:text-purple-400" />
                                Tipos de Serviço
                            </label>
                            <MultiSelect
                                options={serviceTypes}
                                value={filters.serviceTypes || []}
                                onChange={(selected) => handleFilterChange({ ...filters, serviceTypes: selected })}
                                placeholder="Selecione os tipos"
                                className="w-full"
                                moduleOverride="scheduler"
                                loading={isLoadingOptions}
                            />
                        </div>
                    </div>

                    {/* Filtro de Valor */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                            <DollarSign size={16} className="text-purple-600 dark:text-purple-400" />
                            Faixa de Valor
                        </label>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div>
                                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                                    Valor Mínimo
                                </label>
                                <ModuleInput
                                    moduleColor="scheduler"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    placeholder="0,00"
                                    value={filters.minValue || ""}
                                    onChange={(e) => handleFilterChange({ ...filters, minValue: e.target.value })}
                                    leftIcon={<span className="text-sm">R$</span>}
                                />
                            </div>
                            <div>
                                <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                                    Valor Máximo
                                </label>
                                <ModuleInput
                                    moduleColor="scheduler"
                                    type="number"
                                    step="0.01"
                                    min="0"
                                    placeholder="0,00"
                                    value={filters.maxValue || ""}
                                    onChange={(e) => handleFilterChange({ ...filters, maxValue: e.target.value })}
                                    leftIcon={<span className="text-sm">R$</span>}
                                />
                            </div>
                        </div>
                    </div>

                    <div>
                        <FilterButton
                            type="button"
                            onClick={handleClearFilters}
                            moduleColor="scheduler"
                            variant="secondary"
                        >
                            <div className="flex items-center gap-2">
                                <RefreshCw size={16} />
                                <span>Limpar Filtros</span>
                            </div>
                        </FilterButton>
                    </div>
                </div>
            )}
        </div>
    );
};
