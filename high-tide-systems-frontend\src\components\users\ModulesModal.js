"use client";

import React, { useState, useEffect } from "react";
import { X, Loader2, Shield, AlertCircle, Settings, Users, DollarSign, Calendar, CheckSquare, UserCheck, CheckCircle } from "lucide-react";
import { api } from "@/utils/api";
import { useAuth } from "@/contexts/AuthContext";
import { userService } from "@/app/modules/admin/services/userService";

const ModulesModal = ({ isOpen, onClose, user, onSuccess }) => {
  const { user: currentUser } = useAuth();
  const [selectedModules, setSelectedModules] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // Configuração dos módulos disponíveis
  const modules = [
    {
      id: "ADMIN",
      name: "Administração",
      description: "Acesso completo ao sistema, incluindo configurações e gerenciamento de usuários",
      icon: Settings,
      color: "bg-slate-100 dark:bg-slate-900/30 text-slate-700 dark:text-slate-400 border-slate-200 dark:border-slate-800/50",
      requiresAdmin: true
    },
    // RH e FINANCIAL temporariamente removidos
    {
      id: "SCHEDULING",
      name: "Agendamento",
      description: "Gerenciamento de compromissos, reuniões e alocação de recursos",
      icon: Calendar,
      color: "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800/50",
      requiresAdmin: false
    },
    {
      id: "PEOPLE",
      name: "Pessoas",
      description: "Cadastro e gerenciamento de pacientes, clientes e convênios",
      icon: UserCheck,
      color: "bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400 border-orange-200 dark:border-orange-800/50",
      requiresAdmin: false
    },
    {
      id: "BASIC",
      name: "Básico",
      description: "Acesso básico ao sistema, visualização limitada",
      icon: CheckSquare,
      color: "bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 border-neutral-200 dark:border-gray-600",
      requiresAdmin: false,
      disabled: true // Este módulo é obrigatório para todos os usuários
    }
  ];

  const handleSelectAll = () => {
    const availableModules = modules
      .filter(module => !module.requiresAdmin || isAdmin)
      .map(module => module.id);
    setSelectedModules(availableModules);
  };

  useEffect(() => {
    if (user && isOpen) {
      setSelectedModules(user.modules || []);
    }
  }, [user, isOpen]);

  const isAdmin = currentUser?.modules?.includes("ADMIN");

  const handleToggleModule = (moduleId) => {
    if (moduleId === "BASIC") return; // Não pode remover o módulo básico

    setSelectedModules(prev => {
      if (prev.includes(moduleId)) {
        return prev.filter(m => m !== moduleId);
      } else {
        return [...prev, moduleId];
      }
    });
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError("");

    try {
      // Garante que o módulo BASIC sempre esteja presente
      const modulesToSave = selectedModules.includes("BASIC")
        ? selectedModules
        : [...selectedModules, "BASIC"];

      await userService.updateModules(user.id, modulesToSave);

      onSuccess();
    } catch (error) {
      console.error("Erro ao atualizar módulos:", error);
      setError(error.response?.data?.message || "Erro ao atualizar módulos");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo escuro */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-2xl w-full max-h-[90vh] flex flex-col z-[11050]">
        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary-500 dark:text-primary-400" />
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-white">
              Gerenciar Módulos de Acesso
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        {/* Conteúdo */}
        <div className="overflow-y-auto p-6">
          {error && (
            <div className="mb-6 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg flex items-center gap-2">
              <AlertCircle size={18} />
              <span>{error}</span>
            </div>
          )}

          <div className="mb-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h4 className="text-lg font-medium text-neutral-800 dark:text-white mb-1">
                  {user?.fullName}
                </h4>
                <p className="text-sm text-neutral-600 dark:text-gray-300">
                  Selecione os módulos que este usuário terá acesso:
                </p>
              </div>
              <button
                onClick={handleSelectAll}
                className="flex items-center gap-2 px-3 py-2 text-sm bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 border border-primary-200 dark:border-primary-800 rounded-lg hover:bg-primary-100 dark:hover:bg-primary-900/30 transition-colors"
              >
                <CheckCircle size={16} />
                Selecionar Todos
              </button>
            </div>
          </div>

          <div className="space-y-4">
            {modules.map((module) => {
              const isSelected = selectedModules.includes(module.id);
              const Icon = module.icon;

              return (
                <div
                  key={module.id}
                  className={`p-4 rounded-lg border ${
                    isSelected ? module.color : "border-neutral-200 dark:border-gray-700"
                  } ${module.disabled ? "opacity-70" : "cursor-pointer hover:border-primary-300 dark:hover:border-primary-700"}`}
                  onClick={() => {
                    if (!module.disabled) {
                      handleToggleModule(module.id);
                    }
                  }}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => {}}
                        disabled={module.disabled || (module.requiresAdmin && !isAdmin)}
                        className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                      />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Icon className={`h-5 w-5 ${isSelected ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                        <h5 className="font-medium text-neutral-800 dark:text-white">
                          {module.name}
                          {module.id === "ADMIN" && (
                            <span className="ml-2 text-xs font-normal text-amber-600 dark:text-amber-500 bg-amber-50 dark:bg-amber-900/30 px-2 py-1 rounded">
                              Acesso Administrativo
                            </span>
                          )}
                        </h5>
                      </div>
                      <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                        {module.description}
                      </p>

                      {module.requiresAdmin && !isAdmin && (
                        <div className="mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1">
                          <AlertCircle size={12} />
                          <span>Apenas administradores podem conceder este acesso</span>
                        </div>
                      )}

                      {module.id === "BASIC" && (
                        <div className="mt-2 text-xs text-neutral-500 dark:text-gray-400 flex items-center gap-1">
                          <AlertCircle size={12} />
                          <span>Módulo obrigatório para todos os usuários</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            type="button"
            onClick={handleSave}
            className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 size={16} className="animate-spin" />
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <Shield size={16} />
                <span>Salvar Permissões</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ModulesModal;