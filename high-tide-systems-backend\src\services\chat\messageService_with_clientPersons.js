// src/services/chat/messageService.js
const prisma = require('../../utils/prisma');
const { NotFoundError, ForbiddenError } = require('../../utils/errors');
const chatService = require('./chatService');

// Client select with person data
const clientSelectWithPerson = {
  id: true,
  login: true,
  email: true,
  fullName: true,
  clientPersons: {
    take: 1,
    select: {
      person: {
        select: {
          fullName: true
        }
      }
    }
  }
};

/**
 * Cria uma nova mensagem
 * @param {Object} data - Dados da mensagem
 * @returns {Promise<Object>} - Mensagem criada
 */
const createMessage = async (data) => {
  try {
    console.log('[MessageService] Creating message:', data);
    console.log('[MessageService] ContentType received:', data.contentType);
    
    // Verificar se a conversa existe
    const conversation = await prisma.conversation.findUnique({
      where: { id: data.conversationId }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    // Verificar se o usuário é participante da conversa
    const isParticipant = await chatService.isConversationParticipant(
      data.conversationId,
      data.senderId
    );

    if (!isParticipant) {
      throw new ForbiddenError('Você não é participante desta conversa');
    }
    
    // Verificar se é cliente para usar senderClientId
    const isClient = await prisma.client.findUnique({ where: { id: data.senderId } });

    // Se houver uma mensagem referenciada, verificar se ela existe e pertence à mesma conversa
    if (data.referencedMessageId) {
      const referencedMessage = await prisma.message.findUnique({
        where: { id: data.referencedMessageId },
        select: { conversationId: true }
      });

      if (!referencedMessage) {
        throw new NotFoundError('Mensagem referenciada não encontrada');
      }

      if (referencedMessage.conversationId !== data.conversationId) {
        throw new ForbiddenError('Mensagem referenciada não pertence a esta conversa');
      }
    }

    // Criar a mensagem
    const message = await prisma.message.create({
      data: {
        conversationId: data.conversationId,
        senderId: isClient ? null : data.senderId,
        senderClientId: isClient ? data.senderId : null,
        content: data.content || (data.contentType === 'ATTACHMENT' ? 'Arquivo(s) anexado(s)' : ''),
        contentType: data.contentType || 'TEXT',
        referencedMessageId: data.referencedMessageId,
        metadata: data.metadata || {}
      },
      include: {
        sender: {
          select: {
            id: true,
            fullName: true,
            email: true,
            profileImageUrl: true
          }
        },
        senderClient: {
          select: clientSelectWithPerson
        },
        referencedMessage: {
          include: {
            sender: {
              select: {
                id: true,
                fullName: true
              }
            }
          }
        }
      }
    });

    // Criar status de mensagem para todos os participantes
    await createMessageStatusForParticipants(message.id, data.conversationId, data.senderId);

    return message;
  } catch (error) {
    console.error('Error creating message:', error);
    throw error;
  }
};

/**
 * Cria status de mensagem para todos os participantes de uma conversa
 * @param {string} messageId - ID da mensagem
 * @param {string} conversationId - ID da conversa
 * @param {string} senderId - ID do remetente
 * @returns {Promise<void>}
 */
const createMessageStatusForParticipants = async (messageId, conversationId, senderId) => {
  try {
    // Verificar se o remetente é cliente
    const isClient = await prisma.client.findUnique({ where: { id: senderId } });
    
    // Buscar todos os participantes ativos da conversa (exceto o remetente)
    const participants = await prisma.conversationParticipant.findMany({
      where: {
        conversationId,
        leftAt: null,
        NOT: {
          AND: [
            isClient ? { clientId: senderId } : { userId: senderId }
          ]
        }
      }
    });

    // Criar status de mensagem para cada participante
    const statusPromises = participants.map(participant => 
      prisma.messageStatus.create({
        data: {
          messageId,
          participantId: participant.id,
          status: 'SENT'
        }
      })
    );

    await Promise.all(statusPromises);
  } catch (error) {
    console.error('Error creating message status for participants:', error);
    throw error;
  }
};

/**
 * Busca mensagens de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} userId - ID do usuário que está buscando as mensagens
 * @param {Object} filters - Filtros de busca
 * @returns {Promise<Array>} - Lista de mensagens
 */
const getConversationMessages = async (conversationId, userId, filters = {}) => {
  try {
    const { limit = 50, before, after } = filters;

    // Verificar se o usuário é participante da conversa (suporta clientes)
    const isParticipant = await chatService.isConversationParticipant(
      conversationId,
      userId
    );

    if (!isParticipant) {
      throw new ForbiddenError('Você não é participante desta conversa');
    }

    // Construir a query base
    const where = {
      conversationId,
      isDeleted: false
    };

    // Adicionar filtros de paginação
    if (before) {
      where.createdAt = { lt: new Date(before) };
    } else if (after) {
      where.createdAt = { gt: new Date(after) };
    }

    // Buscar as mensagens
    const messages = await prisma.message.findMany({
      where,
      orderBy: {
        createdAt: after ? 'asc' : 'desc'
      },
      take: limit,
      include: {
        sender: {
          select: {
            id: true,
            fullName: true,
            email: true,
            profileImageUrl: true
          }
        },
        senderClient: {
          select: clientSelectWithPerson
        },
        referencedMessage: {
          include: {
            sender: {
              select: {
                id: true,
                fullName: true
              }
            },
            senderClient: {
              select: clientSelectWithPerson
            }
          }
        },
        statuses: {
          where: {
            participant: {
              userId: { not: userId } // Excluir o próprio usuário
            }
          },
          include: {
            participant: {
              select: {
                userId: true
              }
            }
          }
        }
      }
    });

    // Se a busca foi feita com 'after', inverter a ordem para manter consistência
    if (after) {
      messages.reverse();
    }

    return messages;
  } catch (error) {
    console.error('Error getting conversation messages:', error);
    throw error;
  }
};

/**
 * Atualiza o status de uma mensagem para um participante
 * @param {string} messageId - ID da mensagem
 * @param {string} participantId - ID do participante
 * @param {string} status - Novo status
 * @returns {Promise<Object>} - Status atualizado
 */
const updateMessageStatus = async (messageId, participantId, status) => {
  try {
    // Verificar se o status já existe
    const existingStatus = await prisma.messageStatus.findUnique({
      where: {
        messageId_participantId: {
          messageId,
          participantId
        }
      }
    });

    if (existingStatus) {
      // Atualizar o status existente
      return prisma.messageStatus.update({
        where: {
          id: existingStatus.id
        },
        data: {
          status,
          timestamp: new Date()
        }
      });
    } else {
      // Criar um novo status
      return prisma.messageStatus.create({
        data: {
          messageId,
          participantId,
          status
        }
      });
    }
  } catch (error) {
    console.error('Error updating message status:', error);
    throw error;
  }
};

/**
 * Marca uma mensagem como excluída
 * @param {string} messageId - ID da mensagem
 * @param {string} userId - ID do usuário que está excluindo a mensagem
 * @returns {Promise<Object>} - Mensagem atualizada
 */
const deleteMessage = async (messageId, userId) => {
  try {
    // Buscar a mensagem
    const message = await prisma.message.findUnique({
      where: { id: messageId },
      include: {
        conversation: {
          include: {
            participants: {
              where: {
                userId,
                isAdmin: true,
                leftAt: null
              }
            }
          }
        }
      }
    });

    if (!message) {
      throw new NotFoundError('Mensagem não encontrada');
    }

    // Verificar se o usuário é o remetente da mensagem ou administrador da conversa
    const isAdmin = message.conversation.participants.length > 0;
    const isSender = message.senderId === userId;

    if (!isSender && !isAdmin) {
      throw new ForbiddenError('Você não tem permissão para excluir esta mensagem');
    }

    // Marcar a mensagem como excluída
    const updatedMessage = await prisma.message.update({
      where: { id: messageId },
      data: {
        isDeleted: true,
        content: isSender ? 'Esta mensagem foi excluída pelo remetente' : 'Esta mensagem foi excluída por um administrador',
        metadata: null
      }
    });

    return updatedMessage;
  } catch (error) {
    console.error('Error deleting message:', error);
    throw error;
  }
};

/**
 * Busca mensagens não lidas de um usuário
 * @param {string} userId - ID do usuário
 * @returns {Promise<Object>} - Contagem de mensagens não lidas por conversa
 */
const getUnreadMessages = async (userId) => {
  try {
    // Buscar todas as participações ativas do usuário
    const participations = await prisma.conversationParticipant.findMany({
      where: {
        userId,
        leftAt: null
      },
      include: {
        conversation: {
          include: {
            messages: {
              orderBy: {
                createdAt: 'desc'
              },
              take: 1
            }
          }
        }
      }
    });

    // Calcular mensagens não lidas para cada conversa
    const unreadCounts = await Promise.all(
      participations.map(async (participation) => {
        // Se não houver última mensagem lida, todas as mensagens são não lidas
        if (!participation.lastReadMessageId) {
          const count = await prisma.message.count({
            where: {
              conversationId: participation.conversationId,
              senderId: { not: userId },
              isDeleted: false
            }
          });

          return {
            conversationId: participation.conversationId,
            unreadCount: count
          };
        }

        // Buscar a última mensagem lida
        const lastReadMessage = await prisma.message.findUnique({
          where: { id: participation.lastReadMessageId },
          select: { createdAt: true }
        });

        if (!lastReadMessage) {
          return {
            conversationId: participation.conversationId,
            unreadCount: 0
          };
        }

        // Contar mensagens mais recentes que a última lida
        const count = await prisma.message.count({
          where: {
            conversationId: participation.conversationId,
            senderId: { not: userId },
            createdAt: { gt: lastReadMessage.createdAt },
            isDeleted: false
          }
        });

        return {
          conversationId: participation.conversationId,
          unreadCount: count
        };
      })
    );

    // Calcular o total de mensagens não lidas
    const totalUnread = unreadCounts.reduce((total, item) => total + item.unreadCount, 0);

    return {
      conversations: unreadCounts.filter(item => item.unreadCount > 0),
      totalUnread
    };
  } catch (error) {
    console.error('Error getting unread messages:', error);
    throw error;
  }
};

module.exports = {
  createMessage,
  getConversationMessages,
  updateMessageStatus,
  deleteMessage,
  getUnreadMessages
};