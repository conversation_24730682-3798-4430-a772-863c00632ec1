"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/scheduler/introduction/page",{

/***/ "(app-pages-browser)/./src/app/modules/scheduler/introduction/IntroductionPage.js":
/*!********************************************************************!*\
  !*** ./src/app/modules/scheduler/introduction/IntroductionPage.js ***!
  \********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst IntroductionPage = ()=>{\n    _s();\n    const [selectedTutorial, setSelectedTutorial] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isVideoPlaying, setIsVideoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [totalSlides, setTotalSlides] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(8);\n    // Configuração dos tutoriais disponíveis\n    const tutorials = [\n        {\n            id: 'calendar',\n            title: 'Calendário de Agendamentos',\n            description: 'Aprenda a visualizar, criar e gerenciar agendamentos no calendário',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 20\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                lineNumber: 51,\n                columnNumber: 13\n            }, undefined),\n            color: 'from-purple-600 to-purple-500',\n            darkColor: 'from-purple-700 to-purple-600',\n            slides: 8,\n            features: [\n                'Visualizar calendário',\n                'Criar agendamentos',\n                'Filtrar por profissional',\n                'Gerenciar status',\n                'Exportar dados'\n            ]\n        },\n        {\n            id: 'working-hours',\n            title: 'Horários de Trabalho',\n            description: 'Configure os horários de disponibilidade dos profissionais',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                size: 20\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                lineNumber: 67,\n                columnNumber: 13\n            }, undefined),\n            color: 'from-blue-600 to-blue-500',\n            darkColor: 'from-blue-700 to-blue-600',\n            slides: 8,\n            features: [\n                'Configurar horários',\n                'Seleção por arraste',\n                'Copiar horários',\n                'Visualizar por profissional',\n                'Definir exceções'\n            ]\n        },\n        {\n            id: 'locations',\n            title: 'Locais e Salas',\n            description: 'Gerencie os locais e salas disponíveis para agendamentos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                size: 20\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                lineNumber: 83,\n                columnNumber: 13\n            }, undefined),\n            color: 'from-green-600 to-green-500',\n            darkColor: 'from-green-700 to-green-600',\n            slides: 8,\n            features: [\n                'Cadastrar locais',\n                'Vincular com unidades',\n                'Configurar capacidade',\n                'Status de disponibilidade',\n                'Organizar por tipo'\n            ]\n        },\n        {\n            id: 'service-types',\n            title: 'Tipos de Serviço',\n            description: 'Configure os diferentes tipos de serviços oferecidos',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                size: 20\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                lineNumber: 99,\n                columnNumber: 13\n            }, undefined),\n            color: 'from-orange-600 to-orange-500',\n            darkColor: 'from-orange-700 to-orange-600',\n            slides: 8,\n            features: [\n                'Cadastrar serviços',\n                'Definir preços',\n                'Configurar duração',\n                'Vincular profissionais',\n                'Gerenciar cores'\n            ]\n        },\n        {\n            id: 'reports',\n            title: 'Relatórios e Dashboard',\n            description: 'Analise dados de agendamentos e ocupação através de relatórios',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: 20\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                lineNumber: 115,\n                columnNumber: 13\n            }, undefined),\n            color: 'from-red-600 to-red-500',\n            darkColor: 'from-red-700 to-red-600',\n            slides: 8,\n            features: [\n                'Relatórios personalizáveis',\n                'Dashboard interativo',\n                'Análise de ocupação',\n                'Exportação de dados',\n                'Filtros avançados'\n            ]\n        }\n    ];\n    // Auto-advance slides\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"IntroductionPage.useEffect\": ()=>{\n            if (!isVideoPlaying) return;\n            const interval = setInterval({\n                \"IntroductionPage.useEffect.interval\": ()=>{\n                    setCurrentSlide({\n                        \"IntroductionPage.useEffect.interval\": (prev)=>(prev + 1) % totalSlides\n                    }[\"IntroductionPage.useEffect.interval\"]);\n                }\n            }[\"IntroductionPage.useEffect.interval\"], 7000);\n            return ({\n                \"IntroductionPage.useEffect\": ()=>clearInterval(interval)\n            })[\"IntroductionPage.useEffect\"];\n        }\n    }[\"IntroductionPage.useEffect\"], [\n        isVideoPlaying,\n        totalSlides\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"IntroductionPage.useEffect\": ()=>{\n            if (selectedTutorial) {\n                setTotalSlides(selectedTutorial.slides);\n                setCurrentSlide(0);\n            }\n        }\n    }[\"IntroductionPage.useEffect\"], [\n        selectedTutorial\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            size: 24,\n                            className: \"mr-2 text-purple-600 dark:text-purple-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Introdu\\xe7\\xe3o\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-xl border border-module-scheduler-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-purple-600 to-purple-400 dark:from-purple-700 dark:to-purple-600 px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"mr-3 text-white\",\n                                    size: 24,\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"M\\xf3dulo de Agendamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 dark:text-gray-300 mb-6\",\n                                                children: \"Bem-vindo ao M\\xf3dulo de Agendamento do High Tide Systems. Este m\\xf3dulo \\xe9 o centro de gerenciamento de agendamentos, permitindo organizar consultas, reuni\\xf5es e compromissos de forma eficiente. Aqui voc\\xea encontrar\\xe1 todas as ferramentas necess\\xe1rias para gerenciar sua agenda e a de seus profissionais.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 171,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-50 dark:bg-gray-700 rounded-lg p-4 border border-purple-200 dark:border-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-purple-800 dark:text-white mb-3 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                                size: 18\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            \"Principais Recursos\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2 text-sm text-gray-700 dark:text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0\",\n                                                                        children: \"1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Visualiza\\xe7\\xe3o de calend\\xe1rio di\\xe1rio, semanal e mensal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 186,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0\",\n                                                                        children: \"2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 189,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Agendamento r\\xe1pido com verifica\\xe7\\xe3o de disponibilidade\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 190,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0\",\n                                                                        children: \"3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Configura\\xe7\\xe3o de hor\\xe1rios de trabalho dos profissionais\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0\",\n                                                                        children: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 197,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Gerenciamento de locais e salas de atendimento\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 198,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0\",\n                                                                        children: \"5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 201,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Relat\\xf3rios detalhados de agendamentos\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-purple-700 to-purple-500 dark:from-purple-800 dark:to-purple-600 rounded-lg overflow-hidden shadow-lg h-80 relative\",\n                                            children: isVideoPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"absolute inset-0 bg-black/80 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setIsVideoPlaying(false),\n                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"absolute top-4 right-4 bg-white/20 hover:bg-white/30 rounded-full p-2 text-white transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"w-full h-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"ai-video-content w-4/5 h-4/5 relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                \"data-slide\": \"1\",\n                                                                className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"absolute inset-0 flex flex-col items-center justify-center ai-slide\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"text-white text-xl font-bold mb-6\",\n                                                                        children: \"M\\xf3dulo de Agendamento\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 225,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"flex space-x-8 mb-8\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"flex flex-col items-center transition-all duration-500 hover:scale-110\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"w-16 h-16 rounded-lg bg-purple-500/30 flex items-center justify-center mb-2\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                            size: 32,\n                                                                                            className: \"text-purple-300\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                            lineNumber: 229,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 228,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"text-purple-200 text-sm\",\n                                                                                        children: \"Calend\\xe1rio\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 231,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 227,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"flex flex-col items-center transition-all duration-500 hover:scale-110\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"w-16 h-16 rounded-lg bg-purple-500/30 flex items-center justify-center mb-2\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                            size: 32,\n                                                                                            className: \"text-purple-300\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                            lineNumber: 235,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 234,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"text-purple-200 text-sm\",\n                                                                                        children: \"Hor\\xe1rios\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 237,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 233,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"flex flex-col items-center transition-all duration-500 hover:scale-110\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"w-16 h-16 rounded-lg bg-purple-500/30 flex items-center justify-center mb-2\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                            size: 32,\n                                                                                            className: \"text-purple-300\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                            lineNumber: 241,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 240,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"text-purple-200 text-sm\",\n                                                                                        children: \"Locais\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 243,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 239,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 226,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        id: \"4c8f95eb38bb8561\",\n                                                        children: \".ai-video-content.jsx-4c8f95eb38bb8561{position:relative;overflow:hidden}.ai-slide.jsx-4c8f95eb38bb8561{-webkit-animation:fadeIn.5s ease-in-out;-moz-animation:fadeIn.5s ease-in-out;-o-animation:fadeIn.5s ease-in-out;animation:fadeIn.5s ease-in-out}@-webkit-keyframes fadeIn{from{opacity:0;-webkit-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fadeIn{from{opacity:0;-moz-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fadeIn{from{opacity:0;-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fadeIn{from{opacity:0;-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}\"\n                                                    }, void 0, false, void 0, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-20 h-20 rounded-full bg-primary-500/20 flex items-center justify-center mx-auto mb-4 hover:bg-primary-500/30 transition-colors cursor-pointer\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 36,\n                                                            className: \"text-primary-500 ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-sm mb-2\",\n                                                        children: \"Clique para iniciar a demonstra\\xe7\\xe3o interativa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-purple-200 text-xs\",\n                                                        children: \"Visualize as principais funcionalidades do m\\xf3dulo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 265,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"mr-2 text-purple-500\",\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Se\\xe7\\xf5es do M\\xf3dulo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Calend\\xe1rio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 287,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Visualize e gerencie todos os agendamentos em uma interface intuitiva de calend\\xe1rio. Alterne entre visualiza\\xe7\\xf5es di\\xe1rias, semanais e mensais para melhor organiza\\xe7\\xe3o.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Visualiza\\xe7\\xe3o de calend\\xe1rio; Cria\\xe7\\xe3o r\\xe1pida de agendamentos; Filtros avan\\xe7ados; Exporta\\xe7\\xe3o de dados.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Hor\\xe1rios de Trabalho\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Configure os hor\\xe1rios de disponibilidade dos profissionais para agendamentos. Defina hor\\xe1rios personalizados para cada dia da semana.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 333,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Configura\\xe7\\xe3o de hor\\xe1rios; Sele\\xe7\\xe3o por arraste; C\\xf3pia de hor\\xe1rios; Visualiza\\xe7\\xe3o por profissional.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 332,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 340,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Locais e Salas\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 349,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Gerencie os locais e salas dispon\\xedveis para agendamentos. Organize os atendimentos por unidade, andar ou tipo de sala.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 364,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Cadastro de locais; Vincula\\xe7\\xe3o com unidades; Configura\\xe7\\xe3o de capacidade; Status de disponibilidade.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 357,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 355,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 348,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Tipos de Servi\\xe7o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 380,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Gerencie os diferentes tipos de servi\\xe7os oferecidos pela sua cl\\xednica. Configure pre\\xe7os, dura\\xe7\\xe3o e profissionais habilitados para cada servi\\xe7o.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 395,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Cadastro de servi\\xe7os; Defini\\xe7\\xe3o de pre\\xe7os; Configura\\xe7\\xe3o de dura\\xe7\\xe3o; Vincula\\xe7\\xe3o com profissionais.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 401,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 379,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Ocupa\\xe7\\xe3o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Analise detalhadamente a ocupa\\xe7\\xe3o dos profissionais, salas e hor\\xe1rios. Identifique per\\xedodos de maior demanda e otimize a agenda.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 420,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 426,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" An\\xe1lise de ocupa\\xe7\\xe3o; Gr\\xe1ficos por per\\xedodo; Filtros por profissional; Exporta\\xe7\\xe3o de dados.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 417,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Relat\\xf3rios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 442,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Acesse relat\\xf3rios detalhados sobre agendamentos, ocupa\\xe7\\xe3o de salas, produtividade de profissionais e muito mais.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 451,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 457,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Relat\\xf3rios personaliz\\xe1veis; Exporta\\xe7\\xe3o em diversos formatos; Listagem de agendamentos; Filtros avan\\xe7ados.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 456,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 448,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 441,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 473,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Visualize estat\\xedsticas e indicadores de desempenho dos agendamentos. Acompanhe m\\xe9tricas importantes para a gest\\xe3o da sua cl\\xednica.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 488,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Gr\\xe1ficos interativos; Indicadores de desempenho; An\\xe1lise de tend\\xeancias; Filtros por per\\xedodo.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 487,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 479,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 472,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-purple-800 dark:text-white mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 506,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Come\\xe7ando\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 505,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300 mb-4\",\n                                        children: \"Para come\\xe7ar a utilizar o m\\xf3dulo de agendamento, recomendamos seguir estes passos:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 509,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"space-y-3 text-gray-600 dark:text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0\",\n                                                        children: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Verifique os \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"hor\\xe1rios de trabalho\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 36\n                                                            }, undefined),\n                                                            \" dos profissionais para garantir que estejam corretamente configurados.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 513,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Confira os \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"locais e salas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 34\n                                                            }, undefined),\n                                                            \" dispon\\xedveis para agendamentos.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 517,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Acesse o \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"calend\\xe1rio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 32\n                                                            }, undefined),\n                                                            \" para visualizar e criar novos agendamentos.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 521,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Utilize os \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"relat\\xf3rios\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 34\n                                                            }, undefined),\n                                                            \" para acompanhar e analisar os agendamentos realizados.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 525,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 512,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 flex justify-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/dashboard',\n                                                className: \"px-5 py-2.5 bg-white/20 hover:bg-white/30 text-purple-600 border border-purple-300 rounded-lg shadow transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/logo_horizontal_sem_fundo.png\",\n                                                        alt: \"High Tide\",\n                                                        className: \"h-4 w-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Dashboard\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 531,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/dashboard/scheduler/calendar',\n                                                className: \"px-5 py-2.5 bg-purple-600 hover:bg-purple-700 text-white rounded-lg shadow transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 546,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ir para o Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 542,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 530,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                lineNumber: 504,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, undefined);\n};\n_s(IntroductionPage, \"lpD0eUdzqIn08w9FxvEwGUTGm88=\");\n_c = IntroductionPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IntroductionPage);\nvar _c;\n$RefreshReg$(_c, \"IntroductionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/scheduler/introduction/IntroductionPage.js\n"));

/***/ })

});