const { PrismaClient } = require('@prisma/client');
const { randomUUID } = require('crypto');
const bcrypt = require('bcryptjs');
const axios = require('axios');
const prisma = new PrismaClient();

// Utilitários
function getRandomItemSafe(list) {
  if (!Array.isArray(list) || list.length === 0) return null;
  return list[Math.floor(Math.random() * list.length)];
}
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
function generateCPF() {
  const n = Array.from({ length: 9 }, () => Math.floor(Math.random() * 10));
  let d1 = n.reduce((acc, v, i) => acc + v * (10 - i), 0);
  d1 = 11 - (d1 % 11); if (d1 >= 10) d1 = 0;
  let d2 = d1 * 2 + n.reduce((acc, v, i) => acc + v * (11 - i), 0);
  d2 = 11 - (d2 % 11); if (d2 >= 10) d2 = 0;
  return `${n.join('')}${d1}${d2}`;
}
async function getAddressByCEP(cep) {
  try {
    const response = await axios.get(`https://viacep.com.br/ws/${cep}/json/`);
    if (response.data.erro) throw new Error('CEP não encontrado');
    return response.data;
  } catch {
    return {
      logradouro: 'Rua Exemplo', bairro: 'Centro', localidade: 'São Paulo', uf: 'SP', cep: cep
    };
  }
}
function getRandomGender() {
  return Math.random() > 0.5 ? 'M' : 'F';
}
function getRandomName() {
  const firstNames = ['Ana','João','Maria','Pedro','Carla','Lucas','Juliana','Rafael','Fernanda','Bruno','Mariana','Carlos','Patrícia','Ricardo','Camila','Gustavo','Aline','Rodrigo','Daniela','Felipe','Beatriz','André','Larissa','Marcelo','Natália','Thiago','Amanda','Eduardo','Letícia','Vinícius'];
  const lastNames = ['Silva','Santos','Oliveira','Souza','Pereira','Costa','Rodrigues','Almeida','Nascimento','Lima','Araújo','Fernandes','Carvalho','Gomes','Martins','Rocha','Ribeiro','Alves','Monteiro','Mendes','Barros','Freitas','Barbosa','Pinto','Moura','Cavalcanti','Dias','Castro','Campos','Cardoso'];
  return `${getRandomItemSafe(firstNames)} ${getRandomItemSafe(lastNames)} ${getRandomItemSafe(lastNames)}`;
}
function generateEmail(name, domain) {
  const normalized = name.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').replace(/\s+/g, '.');
  return `${normalized}@${domain}`;
}
function generatePhone() {
  const ddd = getRandomInt(11, 99);
  const part1 = getRandomInt(1000, 9999);
  const part2 = getRandomInt(1000, 9999);
  return `(${ddd}) 9${part1}-${part2}`;
}
function getCurrentMonthBusinessDays() {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth();
  const days = [];
  for (let d = 1; d <= 31; d++) {
    const date = new Date(year, month, d);
    if (date.getMonth() !== month) break;
    const dow = date.getDay();
    if (dow >= 1 && dow <= 5) days.push(date);
  }
  return days;
}

async function main() {
  console.log('ALL SEED: Iniciando...');

  // 1. Empresas
  const companiesData = [
    {
      name: 'Clínica Vida',
      tradingName: 'Clínica Vida',
      legalName: 'Clínica Vida Saúde Ltda',
      industry: 'Saúde',
      contactEmail: '<EMAIL>',
      cnpj: '11111111000191',
      phone: '(11) 4002-8922',
      address: 'Av. Brasil, 100',
      city: 'São Paulo',
      state: 'SP',
      postalCode: '01310-200',
      website: 'www.clinicavida.com.br',
      primaryColor: '#4285F4',
      secondaryColor: '#34A853',
      description: 'Clínica de saúde completa',
      socialMedia: { instagram: '@clinicavida' },
      businessHours: { monday: { start: '08:00', end: '18:00' }, tuesday: { start: '08:00', end: '18:00' }, wednesday: { start: '08:00', end: '18:00' }, thursday: { start: '08:00', end: '18:00' }, friday: { start: '08:00', end: '18:00' } },
      professions: ['Médico', 'Enfermeiro', 'Recepcionista', 'Psicólogo'],
      insuranceNames: ['Unimed', 'Bradesco Saúde', 'SulAmérica'],
      serviceTypes: ['Consulta Clínica', 'Exame Laboratorial', 'Sessão Psicoterapia'],
      locationNames: ['Consultório 1', 'Consultório 2', 'Sala de Exames']
    },
    {
      name: 'Odonto Sorriso',
      tradingName: 'Odonto Sorriso',
      legalName: 'Odonto Sorriso Ltda',
      industry: 'Odontologia',
      contactEmail: '<EMAIL>',
      cnpj: '22222222000192',
      phone: '(21) 3003-1234',
      address: 'Rua das Flores, 200',
      city: 'Rio de Janeiro',
      state: 'RJ',
      postalCode: '20031-050',
      website: 'www.odontosorriso.com.br',
      primaryColor: '#F44336',
      secondaryColor: '#FFC107',
      description: 'Clínica odontológica moderna',
      socialMedia: { instagram: '@odontosorriso' },
      businessHours: { monday: { start: '09:00', end: '19:00' }, tuesday: { start: '09:00', end: '19:00' }, wednesday: { start: '09:00', end: '19:00' }, thursday: { start: '09:00', end: '19:00' }, friday: { start: '09:00', end: '19:00' } },
      professions: ['Dentista', 'Auxiliar de Saúde Bucal', 'Recepcionista'],
      insuranceNames: ['OdontoPrev', 'Amil Dental', 'SulAmérica Dental'],
      serviceTypes: ['Consulta Odontológica', 'Limpeza', 'Clareamento'],
      locationNames: ['Consultório 1', 'Consultório 2', 'Sala de Raio-X']
    },
    {
      name: 'PsicoBem',
      tradingName: 'PsicoBem',
      legalName: 'PsicoBem Serviços de Psicologia Ltda',
      industry: 'Psicologia',
      contactEmail: '<EMAIL>',
      cnpj: '33333333000193',
      phone: '(31) 4004-5678',
      address: 'Av. Afonso Pena, 300',
      city: 'Belo Horizonte',
      state: 'MG',
      postalCode: '30130-003',
      website: 'www.psicobem.com.br',
      primaryColor: '#9C27B0',
      secondaryColor: '#00BCD4',
      description: 'Clínica de psicologia e bem-estar',
      socialMedia: { instagram: '@psicobem' },
      businessHours: { monday: { start: '10:00', end: '20:00' }, tuesday: { start: '10:00', end: '20:00' }, wednesday: { start: '10:00', end: '20:00' }, thursday: { start: '10:00', end: '20:00' }, friday: { start: '10:00', end: '20:00' } },
      professions: ['Psicólogo', 'Terapeuta Ocupacional', 'Recepcionista'],
      insuranceNames: ['Unimed', 'Amil', 'Bradesco Saúde'],
      serviceTypes: ['Sessão Psicoterapia', 'Avaliação Psicológica', 'Terapia Ocupacional'],
      locationNames: ['Sala 1', 'Sala 2', 'Sala de Terapia']
    },
    {
      name: 'FisioMov',
      tradingName: 'FisioMov',
      legalName: 'FisioMov Fisioterapia Ltda',
      industry: 'Fisioterapia',
      contactEmail: '<EMAIL>',
      cnpj: '44444444000194',
      phone: '(41) 5005-6789',
      address: 'Rua XV de Novembro, 400',
      city: 'Curitiba',
      state: 'PR',
      postalCode: '80020-310',
      website: 'www.fisiomov.com.br',
      primaryColor: '#4CAF50',
      secondaryColor: '#FF9800',
      description: 'Clínica de fisioterapia e reabilitação',
      socialMedia: { instagram: '@fisiomov' },
      businessHours: { monday: { start: '07:00', end: '17:00' }, tuesday: { start: '07:00', end: '17:00' }, wednesday: { start: '07:00', end: '17:00' }, thursday: { start: '07:00', end: '17:00' }, friday: { start: '07:00', end: '17:00' } },
      professions: ['Fisioterapeuta', 'Recepcionista', 'Massoterapeuta'],
      insuranceNames: ['Unimed', 'Amil', 'Bradesco Saúde'],
      serviceTypes: ['Consulta Fisioterapia', 'Sessão Reabilitação', 'Massagem'],
      locationNames: ['Sala 1', 'Sala 2', 'Sala de Reabilitação']
    },
    {
      name: 'NutriVida',
      tradingName: 'NutriVida',
      legalName: 'NutriVida Nutrição Ltda',
      industry: 'Nutrição',
      contactEmail: '<EMAIL>',
      cnpj: '55555555000195',
      phone: '(51) 6006-7890',
      address: 'Av. Ipiranga, 500',
      city: 'Porto Alegre',
      state: 'RS',
      postalCode: '90610-000',
      website: 'www.nutrividanutricao.com.br',
      primaryColor: '#FF5722',
      secondaryColor: '#8BC34A',
      description: 'Consultório de nutrição e saúde alimentar',
      socialMedia: { instagram: '@nutrividanutricao' },
      businessHours: { monday: { start: '08:00', end: '18:00' }, tuesday: { start: '08:00', end: '18:00' }, wednesday: { start: '08:00', end: '18:00' }, thursday: { start: '08:00', end: '18:00' }, friday: { start: '08:00', end: '18:00' } },
      professions: ['Nutricionista', 'Recepcionista', 'Coach Alimentar'],
      insuranceNames: ['Unimed', 'Amil', 'Bradesco Saúde'],
      serviceTypes: ['Consulta Nutricional', 'Avaliação Alimentar', 'Coaching Alimentar'],
      locationNames: ['Sala 1', 'Sala 2', 'Sala de Avaliação']
    }
  ];
  const createdCompanies = [];
  for (const companyData of companiesData) {
    // Separar campos auxiliares
    const { professions, insuranceNames, serviceTypes, locationNames, ...companyCreateData } = companyData;
    const company = await prisma.company.upsert({
      where: { cnpj: companyData.cnpj },
      update: {},
      create: companyCreateData
    });
    // Salvar os arrays auxiliares junto com o objeto company para uso posterior
    createdCompanies.push({ ...company, professions, insuranceNames, serviceTypes, locationNames });
  }

  // 2. Unidades (branches)
  const createdBranches = [];
  for (const company of createdCompanies) {
    for (const branchType of ['Matriz', 'Filial']) {
      if (!company || !company.id) { console.warn('Empresa indefinida ao criar unidade, pulando.'); continue; }
      // Upsert para evitar duplicidade
      const branch = await prisma.branch.upsert({
        where: {
          companyId_code: {
            companyId: company.id,
            code: branchType.toUpperCase()
          }
        },
        update: {},
        create: {
          name: `${company.name} - ${branchType}`,
          code: branchType.toUpperCase(),
          description: branchType === 'Matriz' ? 'Unidade matriz' : 'Unidade filial',
          address: company.address,
          neighborhood: 'Centro',
          city: company.city,
          state: company.state,
          postalCode: company.postalCode,
          phone: company.phone,
          email: `${branchType.toLowerCase()}@${company.website.replace('www.', '')}`,
          isHeadquarters: branchType === 'Matriz',
          company: { connect: { id: company.id } }
        },
        include: { company: true }
      });
      createdBranches.push(branch);
    }
  }

  // 3. Grupos de Profissão
  const professionGroups = [];
  for (const company of createdCompanies) {
    if (!company || !company.id) { console.warn('Empresa indefinida ao criar grupo de profissão, pulando.'); continue; }
    const group = await prisma.professionGroup.create({
      data: {
        name: 'Profissionais da Saúde',
        description: 'Grupo padrão',
        company: { connect: { id: company.id } },
        defaultModules: ['BASIC', 'SCHEDULING'],
        defaultPermissions: ['view_appointments', 'create_appointments', 'edit_appointments', 'admin.settings.view', 'basic.introductions.view']
      },
      include: { company: true }
    });
    professionGroups.push(group);
  }

  // 4. Profissões
  const professions = [];
  for (const group of professionGroups) {
    if (!group || !group.id || !group.company || !group.company.id) { console.warn('Grupo de profissão indefinido ao criar profissão, pulando.'); continue; }
    const company = createdCompanies.find(c => c.id === group.company.id);
    for (const profName of (company?.professions || [])) {
      const prof = await prisma.profession.create({
        data: {
          name: profName,
          description: `Profissão ${profName}`,
          group: { connect: { id: group.id } },
          company: { connect: { id: group.company.id } },
          active: true
        },
        include: { company: true }
      });
      professions.push(prof);
    }
  }
//123456 senha porra
  // 5. Usuários (incluindo pelo menos um COMPANY_ADMIN por empresa)
  const users = [];
  for (const company of createdCompanies) {
    if (!company || !company.id) { console.warn('Empresa indefinida ao criar usuário, pulando.'); continue; }
    // Company admin
    const admin = await prisma.user.upsert({
      where: { email: `admin@${company.website.replace('www.', '')}` },
      update: {},
      create: {
        email: `admin@${company.website.replace('www.', '')}`,
        login: `admin.${company.name.toLowerCase().replace(/\s/g, '')}`,
        password: '$2a$12$ylk0bHSNHX17CW1.SMoWz.v97H3kZq.7kyXhJ9Mh0f1Z1I8m67P3O',
        fullName: `Administrador ${company.name}`,
        role: 'COMPANY_ADMIN',
        active: true,
        modules: ['ADMIN', 'RH', 'FINANCIAL', 'SCHEDULING', 'PEOPLE', 'ABAPLUS', 'BASIC'],
        permissions: [
          // Agendamentos básicos
          'view_appointments', 'create_appointments', 'edit_appointments', 
          'view_patients', 'edit_patients',
          
          // Módulo ADMIN - Administração
          'admin.users.view', 'admin.users.create', 'admin.users.edit', 'admin.users.delete',
          'admin.permissions.manage', 'admin.logs.view', 'admin.config.edit',
          'admin.professions.view', 'admin.professions.create', 'admin.professions.edit', 'admin.professions.delete',
          'admin.profession-groups.view', 'admin.profession-groups.create', 'admin.profession-groups.edit', 'admin.profession-groups.delete',
          'admin.plans.view', 'admin.notifications.view', 'admin.notifications.manage', 'admin.notifications.permissions.manage',
          'admin.settings.view', 'admin.settings.manage', 'admin.introductions.view', 'admin.introductions.manage',
          
          // Módulo RH - Recursos Humanos
          'rh.dashboard.view', 'rh.employees.view', 'rh.employees.create', 'rh.employees.edit',
          'rh.payroll.view', 'rh.payroll.manage', 'rh.benefits.view', 'rh.benefits.manage',
          'rh.attendance.view', 'rh.attendance.manage', 'rh.introductions.view', 'rh.introductions.manage',
          
          // Módulo FINANCIAL - Financeiro
          'financial.dashboard.view', 'financial.invoices.view', 'financial.invoices.create', 'financial.invoices.edit',
          'financial.payments.view', 'financial.payments.process', 'financial.expenses.view', 'financial.expenses.manage',
          'financial.reports.view', 'financial.reports.export', 'financial.introductions.view', 'financial.introductions.manage',
          
          // Módulo SCHEDULING - Agendamento
          'scheduling.calendar.view', 'scheduling.calendar.create', 'scheduling.calendar.edit', 'scheduling.calendar.delete',
          'scheduling.working-hours.view', 'scheduling.working-hours.manage',
          'scheduling.service-types.view', 'scheduling.service-types.create', 'scheduling.service-types.edit', 'scheduling.service-types.delete',
          'scheduling.locations.view', 'scheduling.locations.create', 'scheduling.locations.edit', 'scheduling.locations.delete',
          'scheduling.occupancy.view', 'scheduling.appointments-report.view', 'scheduling.appointments-report.export',
          'scheduling.appointments-dashboard.view', 'scheduling.rooms.manage', 'scheduling.resources.manage',
          'scheduling.introductions.view', 'scheduling.introductions.manage',
          
          // Módulo PEOPLE - Pessoas
          'people.clients.view', 'people.clients.create', 'people.clients.edit', 'people.clients.delete',
          'people.persons.view', 'people.persons.create', 'people.persons.edit', 'people.persons.delete',
          'people.insurances.view', 'people.insurances.create', 'people.insurances.edit', 'people.insurances.delete',
          'people.insurance-limits.view', 'people.insurance-limits.create', 'people.insurance-limits.edit', 'people.insurance-limits.delete',
          'people.introductions.view', 'people.introductions.manage',
          
          // Módulo ABAPLUS - ABA+
          'abaplus.dashboard.view', 'abaplus.introduction.view', 'abaplus.skills.view', 'abaplus.skills.create', 'abaplus.skills.edit', 'abaplus.skills.delete',
          'abaplus.programs.view', 'abaplus.programs.create', 'abaplus.programs.edit', 'abaplus.programs.delete',
          'abaplus.evaluations.view', 'abaplus.evaluations.create', 'abaplus.evaluations.edit', 'abaplus.evaluations.delete',
          'abaplus.standard-criteria.view', 'abaplus.standard-criteria.create', 'abaplus.standard-criteria.edit', 'abaplus.standard-criteria.delete',
          'abaplus.curriculum-folders.view', 'abaplus.curriculum-folders.create', 'abaplus.curriculum-folders.edit', 'abaplus.curriculum-folders.delete',
          'abaplus.anamnese.view', 'abaplus.anamnese.create', 'abaplus.anamnese.edit', 'abaplus.anamnese.delete',
          'abaplus.evolucoes-diarias.view', 'abaplus.evolucoes-diarias.create', 'abaplus.evolucoes-diarias.edit', 'abaplus.evolucoes-diarias.delete',
          'abaplus.sessao.view', 'abaplus.sessao.create', 'abaplus.sessao.edit', 'abaplus.sessao.delete',
          'abaplus.introductions.view', 'abaplus.introductions.manage',
          
          // Módulo BASIC - Básico
          'basic.profile.view', 'basic.profile.edit', 'basic.introductions.view',
          
          // Notificações
          'notifications.new_registration.receive', 'notifications.appointment_coming.receive',
          'notifications.new_access.receive', 'notifications.new_backup.receive',
          'notifications.new_export.receive', 'notifications.system_alert.receive'
        ],
        company: { connect: { id: company.id } }
      },
      include: { company: true }
    });
    users.push(admin);
    // Employees
    for (const prof of professions.filter(p => p.companyId === company.id)) {
      if (!prof || !prof.id) { console.warn('Profissão indefinida ao criar usuário, pulando.'); continue; }
      for (let i = 0; i < 3; i++) {
        const name = getRandomName();
        const email = generateEmail(name, company.website.replace('www.', ''));
        const user = await prisma.user.create({
          data: {
            email,
            login: `${name.toLowerCase().replace(/\s/g, '')}${i}`,
            password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm',
            fullName: name,
            role: 'EMPLOYEE',
            active: true,
            modules: ['BASIC', 'SCHEDULING'],
            permissions: ['view_appointments', 'create_appointments', 'admin.settings.view', 'basic.introductions.view'],
            company: { connect: { id: company.id } },
            professionObj: { connect: { id: prof.id } }
          },
          include: { company: true }
        });
        users.push(user);
      }
    }
  }

  // 6. Clientes e Pacientes (titular e dependentes)
  const clients = [];
  const patients = [];
  const clientPersons = [];
  const emailsUsados = new Set();
  for (const company of createdCompanies) {
    if (!company || !company.id) { console.warn('Empresa indefinida ao criar cliente, pulando.'); continue; }
    // Criar de 3 a 7 clientes por empresa
    const numClients = getRandomInt(3, 7);
    for (let i = 0; i < numClients; i++) {
      // Dados únicos para cliente
      let name, email, login;
      do {
        name = getRandomName();
        email = generateEmail(name, company.website.replace('www.', ''));
        login = name.toLowerCase().replace(/\s/g, '') + getRandomInt(100, 999);
      } while (emailsUsados.has(email));
      emailsUsados.add(email);
      // Criar cliente (titular)
      const client = await prisma.client.create({
        data: {
          login,
          email,
          password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm',
          active: true,
          createdById: users.find(u => u.companyId === company.id && u.role === 'COMPANY_ADMIN').id,
          companyId: company.id
        }
      });
      clients.push(client);
      // Criar paciente titular (mesmos dados do cliente)
      const titular = await prisma.person.create({
        data: {
          fullName: name,
          cpf: generateCPF(),
          birthDate: new Date(1980 + getRandomInt(0, 30), getRandomInt(0, 11), getRandomInt(1, 28)),
          address: company.address,
          neighborhood: 'Centro',
          city: company.city,
          state: company.state,
          postalCode: company.postalCode,
          phone: generatePhone(),
          email: email,
          gender: getRandomGender(),
          notes: 'Paciente titular gerado automaticamente',
          relationship: 'Titular',
          createdById: users.find(u => u.companyId === company.id && u.role === 'COMPANY_ADMIN').id
        }
      });
      patients.push(titular);
      clientPersons.push({ clientId: client.id, personId: titular.id, isPrimary: true, relationship: 'Titular' });
      // Criar de 1 a 3 dependentes para cada cliente
      const numDependentes = getRandomInt(1, 3);
      for (let d = 0; d < numDependentes; d++) {
        const depName = getRandomName();
        const dep = await prisma.person.create({
          data: {
            fullName: depName,
            cpf: generateCPF(),
            birthDate: new Date(2010 + getRandomInt(0, 10), getRandomInt(0, 11), getRandomInt(1, 28)),
            address: company.address,
            neighborhood: 'Centro',
            city: company.city,
            state: company.state,
            postalCode: company.postalCode,
            phone: generatePhone(),
            email: generateEmail(depName, 'gmail.com'),
            gender: getRandomGender(),
            notes: 'Dependente gerado automaticamente',
            relationship: 'Dependente',
            createdById: users.find(u => u.companyId === company.id && u.role === 'COMPANY_ADMIN').id
          }
        });
        patients.push(dep);
        // Para cada dependente, inicialmente associe como principal à primeira cliente (mãe ou pai), os demais serão secundários
        // Aqui, por padrão, o primeiro cliente será o principal (isPrimary: true, relationship: 'Mãe' ou 'Pai')
        const rels = ['Mãe', 'Pai', 'Responsável'];
        const mainRelationship = getRandomItemSafe(rels);
        clientPersons.push({ clientId: client.id, personId: dep.id, isPrimary: true, relationship: mainRelationship });
      }
    }
  }
  // Associar alguns dependentes a mais de um cliente (pais separados)
  const dependentes = patients.filter(p => p.relationship === 'Dependente');
  for (const dep of dependentes) {
    // 30% de chance de ter outro responsável
    if (Math.random() < 0.3) {
      const possiveisClientes = clients.filter(c => !clientPersons.some(cp => cp.clientId === c.id && cp.personId === dep.id));
      const outroCliente = getRandomItemSafe(possiveisClientes);
      if (outroCliente) {
        // Para associações extras, sempre como secundário
        const rels = ['Pai', 'Mãe', 'Responsável'];
        const secRelationship = getRandomItemSafe(rels);
        clientPersons.push({ clientId: outroCliente.id, personId: dep.id, isPrimary: false, relationship: secRelationship });
      }
    }
  }
  // Persistir as associações clientPersons no banco
  if (clientPersons.length) {
    await prisma.clientPerson.createMany({
      data: clientPersons,
      skipDuplicates: true
    });
  }

  // 8. Convênios (insurance)
  const insurances = [];
  for (const company of createdCompanies) {
    if (!company || !company.id) { console.warn('Empresa indefinida ao criar convênio, pulando.'); continue; }
    for (const name of company.insuranceNames) {
      const insurance = await prisma.insurance.upsert({
        where: {
          name_companyId: {
            name,
            companyId: company.id
          }
        },
        update: {},
        create: {
          name,
          company: { connect: { id: company.id } }
        }
      });
      insurances.push(insurance);
    }
  }

  // 9. Associação paciente-convênio (personInsurance)
  const personInsurances = [];
  for (const person of patients) {
    if (!person || !person.id) { console.warn('Paciente indefinido ao associar convênio, pulando.'); continue; }
    // Buscar convênios da empresa do paciente
    // Encontrar o clientId do paciente via clientPersons
    const cp = clientPersons.find(cp => cp.personId === person.id);
    if (!cp) { console.warn('Paciente sem clientPerson, pulando associação de convênio.'); continue; }
    const client = clients.find(c => c.id === cp.clientId);
    if (!client || !client.companyId) { console.warn('Cliente ou empresa indefinidos ao associar convênio, pulando.'); continue; }
    const insurancesForCompany = insurances.filter(i => i.companyId === client.companyId);
    if (!insurancesForCompany.length) { console.warn('Nenhum convênio disponível para a empresa do paciente, pulando.'); continue; }
    const insurance = getRandomItemSafe(insurancesForCompany);
    if (!insurance || !insurance.id) { console.warn('Insurance indefinida, pulando.'); continue; }
    const pi = await prisma.personInsurance.create({
      data: {
        personId: person.id,
        insuranceId: insurance.id,
        policyNumber: `${getRandomInt(100000,999999)}`,
        validUntil: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
        notes: 'Associação automática'
      }
    });
    personInsurances.push(pi);
  }

  // 10. Tipos de serviço (serviceType)
  const serviceTypes = [];
  for (const company of createdCompanies) {
    if (!company || !company.id) { console.warn('Empresa indefinida ao criar tipo de serviço, pulando.'); continue; }
    for (const name of company.serviceTypes) {
      const st = await prisma.serviceType.upsert({
        where: {
          name_companyId: {
            name,
            companyId: company.id
          }
        },
        update: {},
        create: {
          name,
          value: getRandomInt(100, 500),
          company: { connect: { id: company.id } }
        }
      });
      serviceTypes.push(st);
    }
  }

  // 11. Limites de convênio (personInsuranceServiceLimit)
  const personInsuranceServiceLimits = [];
  for (const pi of personInsurances) {
    if (!pi || !pi.insuranceId || !pi.personId) { console.warn('Associação paciente-convênio indefinida ao criar limite, pulando.'); continue; }
    const insurance = insurances.find(i => i.id === pi.insuranceId);
    if (!insurance || !insurance.companyId) { console.warn('Convênio indefinido ao criar limite, pulando.'); continue; }
    for (const st of serviceTypes.filter(s => s.companyId === insurance.companyId)) {
      if (!st || !st.id) { console.warn('Tipo de serviço indefinido ao criar limite, pulando.'); continue; }
      const limit = await prisma.personInsuranceServiceLimit.create({
        data: {
          id: randomUUID(),
          personId: pi.personId,
          insuranceId: pi.insuranceId,
          serviceTypeId: st.id,
          monthlyLimit: getRandomInt(4, 12),
          yearlyLimit: getRandomInt(40, 120),
          notes: 'Limite automático'
        }
      });
      personInsuranceServiceLimits.push(limit);
    }
  }

  // 12. Localizações (location)
  const locations = [];
  for (const company of createdCompanies) {
    if (!company || !company.id) { console.warn('Empresa indefinida ao criar localização, pulando.'); continue; }
    for (const branch of createdBranches.filter(b => b.company && b.company.id === company.id)) {
      if (!branch || !branch.id) { console.warn('Unidade indefinida ao criar localização, pulando.'); continue; }
      for (const name of company.locationNames) {
        const loc = await prisma.location.create({
          data: {
            name,
            address: branch.address,
            phone: branch.phone,
            company: { connect: { id: company.id } },
            branch: { connect: { id: branch.id } }
          }
        });
        locations.push(loc);
      }
    }
  }

  // 13. Horários de trabalho (workingHours)
  for (const user of users.filter(u => u.modules && u.modules.includes('SCHEDULING'))) {
    if (!user || !user.id) { console.warn('Usuário indefinido ao criar horário de trabalho, pulando.'); continue; }
    for (const dayOfWeek of [1,2,3,4,5]) {
      await prisma.workingHours.create({
        data: {
          userId: user.id,
          dayOfWeek,
          startTimeMinutes: 8*60,
          endTimeMinutes: 17*60,
          breakStartMinutes: 12*60,
          breakEndMinutes: 13*60,
          isActive: true
        }
      });
    }
  }

  // 14. Agendamentos (scheduling) para o mês atual
  const businessDays = getCurrentMonthBusinessDays();
  const possibleStatuses = ['CONFIRMED', 'PENDING', 'CANCELLED', 'COMPLETED'];
  for (const user of users.filter(u => u.modules && u.modules.includes('SCHEDULING'))) {
    if (!user || !user.id || !user.company || !user.company.id) { console.warn('Usuário ou empresa indefinidos ao criar agendamento, pulando.'); continue; }
    // Filtrar entidades por empresa do usuário
    const companyId = user.company.id;
    const companyClients = clients.filter(c => c.companyId === companyId);
    // Pacientes associados a clientes da empresa via clientPersons
    const companyPatientIds = clientPersons
      .filter(cp => companyClients.some(c => c.id === cp.clientId))
      .map(cp => cp.personId);
    const companyPatients = patients.filter(p => companyPatientIds.includes(p.id));
    const companyInsurances = insurances.filter(i => i.companyId === companyId);
    const companyServiceTypes = serviceTypes.filter(s => s.companyId === companyId);
    const companyLocations = locations.filter(l => l.companyId === companyId);
    if (companyClients.length === 0 || companyPatients.length === 0 || companyInsurances.length === 0 || companyServiceTypes.length === 0 || companyLocations.length === 0) {
      console.warn(`Empresa ${companyId}: clientes=${companyClients.length}, pacientes=${companyPatients.length}, convênios=${companyInsurances.length}, tipos_serviço=${companyServiceTypes.length}, localizações=${companyLocations.length}`);
      continue;
    }
    // Distribuição desigual: cada profissional terá entre 5 e 25 agendamentos no mês
    const agendamentosPorProfissional = getRandomInt(5, 25);
    let agendamentosCriados = 0;
    for (const date of businessDays) {
      const whStart = 8*60;
      const whEnd = 17*60;
      // Para cada dia, criar de 0 a 3 agendamentos aleatórios para este profissional
      const agendamentosNoDia = getRandomInt(0, 3);
      for (let i = 0; i < agendamentosNoDia && agendamentosCriados < agendamentosPorProfissional; i++) {
        // Horário aleatório dentro do expediente
        const startMinutes = getRandomInt(whStart, whEnd - 60);
        const startDate = new Date(date);
        startDate.setHours(Math.floor(startMinutes/60), startMinutes%60, 0, 0);
        const endDate = new Date(startDate.getTime() + 60*60000); // 1h depois
        const client = getRandomItemSafe(companyClients);
        const patient = getRandomItemSafe(companyPatients);
        // Buscar convênios do paciente
        const personInsurancesForPatient = personInsurances.filter(pi => pi.personId === patient.id && companyInsurances.some(ci => ci.id === pi.insuranceId));
        const insurance = getRandomItemSafe(personInsurancesForPatient);
        if (!insurance) continue;
        // Buscar limites de serviço para o paciente e convênio
        const limits = personInsuranceServiceLimits.filter(lim =>
          lim.personId === patient.id && lim.insuranceId === insurance.insuranceId && companyServiceTypes.some(st => st.id === lim.serviceTypeId)
        );
        const serviceType = getRandomItemSafe(limits.map(lim => companyServiceTypes.find(st => st.id === lim.serviceTypeId)).filter(Boolean));
        if (!serviceType) continue;
        const location = getRandomItemSafe(companyLocations);
        const status = getRandomItemSafe(possibleStatuses);
        if (!client || !client.id || !patient || !patient.id || !insurance || !insurance.insuranceId || !serviceType || !serviceType.id || !location || !location.id) {
          console.warn(`Dados insuficientes para criar agendamento para empresa ${companyId}, user ${user.id}`);
          continue;
        }
        await prisma.scheduling.create({
          data: {
            userId: user.id,
            clientId: client.id,
            creatorId: user.id,
            locationId: location.id,
            title: `Consulta ${serviceType.name}`,
            description: `Agendamento automático para paciente`,
            startDate,
            endDate,
            serviceTypeId: serviceType.id,
            insuranceId: insurance.insuranceId,
            status,
            companyId: companyId,
            branchId: location.branchId,
            Person: { connect: [{ id: patient.id }] }
          }
        });
        agendamentosCriados++;
      }
    }
  }

  // 15. Planos/Assinaturas (subscriptions)
  console.log('ALL SEED: Criando planos para as empresas...');
  
  const subscriptions = [];
  const subscriptionModules = [];
  
  for (const company of createdCompanies) {
    if (!company || !company.id) { 
      console.warn('Empresa indefinida ao criar planos, pulando.'); 
      continue; 
    }

    // Configurações diferentes para cada empresa para variedade
    const companyIndex = createdCompanies.indexOf(company);
    const subscriptionConfigs = [
      // Configuração 1: Plano básico mensal
      {
        billingCycle: 'MONTHLY',
        status: 'ACTIVE',
        userLimit: 5,
        pricePerMonth: 99.50,
        modules: ['BASIC', 'ADMIN', 'SCHEDULING', 'PEOPLE']
      },
      // Configuração 2: Plano completo anual em trial
      {
        billingCycle: 'YEARLY', 
        status: 'TRIAL',
        userLimit: 20,
        pricePerMonth: 298.50,
        trialEndDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 dias
        modules: ['BASIC', 'ADMIN', 'SCHEDULING', 'PEOPLE', 'RH', 'FINANCIAL', 'ABAPLUS']
      },
      // Configuração 3: Plano intermediário mensal com cupom
      {
        billingCycle: 'MONTHLY',
        status: 'ACTIVE', 
        userLimit: 10,
        pricePerMonth: 159.20,
        couponCode: 'DESCONTO10',
        couponDiscount: 10.0,
        modules: ['BASIC', 'ADMIN', 'SCHEDULING', 'PEOPLE', 'RH']
      },
      // Configuração 4: Plano grande anual
      {
        billingCycle: 'YEARLY',
        status: 'ACTIVE',
        userLimit: 50, 
        pricePerMonth: 597.00,
        modules: ['BASIC', 'ADMIN', 'SCHEDULING', 'PEOPLE', 'RH', 'FINANCIAL']
      },
      // Configuração 5: Plano pequeno cancelado
      {
        billingCycle: 'MONTHLY',
        status: 'CANCELED',
        userLimit: 3,
        pricePerMonth: 59.70,
        cancelAtPeriodEnd: true,
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 dias
        modules: ['BASIC', 'ADMIN', 'SCHEDULING']
      }
    ];

    const config = subscriptionConfigs[companyIndex % subscriptionConfigs.length];
    
    // Definir datas baseadas no status
    let startDate = new Date();
    let nextBillingDate = null;
    let lastBillingDate = null;
    
    if (config.status === 'ACTIVE') {
      // Para assinaturas ativas, definir último pagamento há 15-30 dias e próximo em 15-30 dias
      lastBillingDate = new Date(Date.now() - getRandomInt(15, 30) * 24 * 60 * 60 * 1000);
      if (config.billingCycle === 'MONTHLY') {
        nextBillingDate = new Date(lastBillingDate.getTime() + 30 * 24 * 60 * 60 * 1000);
      } else {
        nextBillingDate = new Date(lastBillingDate.getTime() + 365 * 24 * 60 * 60 * 1000);
      }
    } else if (config.status === 'TRIAL') {
      // Para trials, próximo pagamento é após o fim do trial
      nextBillingDate = config.trialEndDate;
    }

    // Criar assinatura
    const subscription = await prisma.subscription.create({
      data: {
        companyId: company.id,
        billingCycle: config.billingCycle,
        status: config.status,
        active: config.status !== 'CANCELED',
        userLimit: config.userLimit,
        pricePerMonth: config.pricePerMonth,
        startDate,
        endDate: config.endDate || null,
        trialEndDate: config.trialEndDate || null,
        lastBillingDate,
        nextBillingDate,
        cancelAtPeriodEnd: config.cancelAtPeriodEnd || false,
        couponCode: config.couponCode || null,
        couponDiscount: config.couponDiscount || null,
        // Stripe fields serão null para dados de seed
        stripeCustomerId: null,
        stripeSubscriptionId: null,
        stripeCurrentPeriodEnd: null
      }
    });

    subscriptions.push(subscription);

    // Criar módulos da assinatura
    const modulesPrices = {
      BASIC: 0, // Básico é gratuito
      ADMIN: 0, // Admin é gratuito  
      SCHEDULING: 0, // Scheduling é gratuito
      PEOPLE: 0, // People é gratuito
      RH: 50.00, // RH é pago
      FINANCIAL: 75.00, // Financial é pago
      ABAPLUS: 100.00 // ABA+ é pago
    };

    for (const moduleType of config.modules) {
      const subscriptionModule = await prisma.subscriptionModule.create({
        data: {
          subscriptionId: subscription.id,
          moduleType,
          active: true,
          pricePerMonth: modulesPrices[moduleType] || 0,
          stripePriceId: null // Para dados de seed
        }
      });
      subscriptionModules.push(subscriptionModule);
    }

    console.log(`ALL SEED: Plano criado para empresa ${company.name}: ${config.modules.length} módulos, ${config.userLimit} usuários, R$ ${config.pricePerMonth}`);
  }

  console.log(`ALL SEED: ${subscriptions.length} planos criados com ${subscriptionModules.length} módulos no total.`);

  // Adicionar SYSTEM_ADMIN
  // Excluir usuário SYSTEM_ADMIN existente, se houver
  // Primeiro deletar logs de auditoria associados
  const existingSystemAdmin = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });
  if (existingSystemAdmin) {
    await prisma.auditLog.deleteMany({ where: { userId: existingSystemAdmin.id } });
    await prisma.user.delete({ where: { email: '<EMAIL>' } });
  }
  const systemAdminPasswordHash = await bcrypt.hash('Super@123', 12);
  await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      login: 'superadmin',
      password: systemAdminPasswordHash, // hash de Super@123 com 12 rounds
      fullName: 'Super Admin',
      role: 'SYSTEM_ADMIN',
      active: true,
      modules: ['ADMIN', 'SCHEDULING', 'BASIC', 'RH', 'FINANCIAL'],
      permissions: ['*']
    }
  });

  console.log('ALL SEED: Finalizado com sucesso!');
}

main()
  .catch((e) => { console.error('Erro durante o ALL SEED:', e); process.exit(1); })
  .finally(async () => { await prisma.$disconnect(); }); 