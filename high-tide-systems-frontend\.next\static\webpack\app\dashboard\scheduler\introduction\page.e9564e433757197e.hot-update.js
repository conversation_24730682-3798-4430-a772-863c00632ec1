"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/scheduler/introduction/page",{

/***/ "(app-pages-browser)/./src/app/modules/scheduler/introduction/IntroductionPage.js":
/*!********************************************************************!*\
  !*** ./src/app/modules/scheduler/introduction/IntroductionPage.js ***!
  \********************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,ArrowRight,BarChart4,Briefcase,Building,Calendar,CheckCircle,ChevronRight,Clock,Download,Edit,Eye,FileText,Filter,Info,LayoutDashboard,LineChart,MapPin,Pause,PieChart,Play,Plus,Power,RefreshCw,Send,Settings,SlidersHorizontal,Tag,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst IntroductionPage = ()=>{\n    _s();\n    const [selectedTutorial, setSelectedTutorial] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [isVideoPlaying, setIsVideoPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentSlide, setCurrentSlide] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [totalSlides, setTotalSlides] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(8);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            size: 24,\n                            className: \"mr-2 text-purple-600 dark:text-purple-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Introdu\\xe7\\xe3o\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-xl border border-module-scheduler-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-purple-600 to-purple-400 dark:from-purple-700 dark:to-purple-600 px-6 py-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"mr-3 text-white\",\n                                    size: 24,\n                                    \"aria-hidden\": \"true\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-white\",\n                                    children: \"M\\xf3dulo de Agendamento\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 dark:text-gray-300 mb-6\",\n                                                children: \"Bem-vindo ao M\\xf3dulo de Agendamento do High Tide Systems. Este m\\xf3dulo \\xe9 o centro de gerenciamento de agendamentos, permitindo organizar consultas, reuni\\xf5es e compromissos de forma eficiente. Aqui voc\\xea encontrar\\xe1 todas as ferramentas necess\\xe1rias para gerenciar sua agenda e a de seus profissionais.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 69,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-50 dark:bg-gray-700 rounded-lg p-4 border border-purple-200 dark:border-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-purple-800 dark:text-white mb-3 flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                                size: 18\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 78,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            \"Principais Recursos\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        className: \"space-y-2 text-sm text-gray-700 dark:text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0\",\n                                                                        children: \"1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 83,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Visualiza\\xe7\\xe3o de calend\\xe1rio di\\xe1rio, semanal e mensal\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 84,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0\",\n                                                                        children: \"2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 87,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Agendamento r\\xe1pido com verifica\\xe7\\xe3o de disponibilidade\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 88,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 86,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0\",\n                                                                        children: \"3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 91,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Configura\\xe7\\xe3o de hor\\xe1rios de trabalho dos profissionais\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 92,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0\",\n                                                                        children: \"4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 95,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Gerenciamento de locais e salas de atendimento\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 96,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 94,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0\",\n                                                                        children: \"5\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 99,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Relat\\xf3rios detalhados de agendamentos\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 100,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-purple-700 to-purple-500 dark:from-purple-800 dark:to-purple-600 rounded-lg overflow-hidden shadow-lg h-80 relative\",\n                                            children: isVideoPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"absolute inset-0 bg-black/80 flex items-center justify-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setIsVideoPlaying(false),\n                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"absolute top-4 right-4 bg-white/20 hover:bg-white/30 rounded-full p-2 text-white transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"w-full h-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"ai-video-content w-4/5 h-4/5 relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                \"data-slide\": \"1\",\n                                                                className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"absolute inset-0 flex flex-col items-center justify-center ai-slide\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"text-white text-xl font-bold mb-6\",\n                                                                        children: \"M\\xf3dulo de Agendamento\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 123,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"flex space-x-8 mb-8\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"flex flex-col items-center transition-all duration-500 hover:scale-110\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"w-16 h-16 rounded-lg bg-purple-500/30 flex items-center justify-center mb-2\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                            size: 32,\n                                                                                            className: \"text-purple-300\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                            lineNumber: 127,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 126,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"text-purple-200 text-sm\",\n                                                                                        children: \"Calend\\xe1rio\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 129,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 125,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"flex flex-col items-center transition-all duration-500 hover:scale-110\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"w-16 h-16 rounded-lg bg-purple-500/30 flex items-center justify-center mb-2\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                            size: 32,\n                                                                                            className: \"text-purple-300\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                            lineNumber: 133,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 132,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"text-purple-200 text-sm\",\n                                                                                        children: \"Hor\\xe1rios\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 135,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 131,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"flex flex-col items-center transition-all duration-500 hover:scale-110\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"w-16 h-16 rounded-lg bg-purple-500/30 flex items-center justify-center mb-2\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                            size: 32,\n                                                                                            className: \"text-purple-300\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                            lineNumber: 139,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 138,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-4c8f95eb38bb8561\" + \" \" + \"text-purple-200 text-sm\",\n                                                                                        children: \"Locais\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                        lineNumber: 141,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 137,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 124,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 122,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        id: \"4c8f95eb38bb8561\",\n                                                        children: \".ai-video-content.jsx-4c8f95eb38bb8561{position:relative;overflow:hidden}.ai-slide.jsx-4c8f95eb38bb8561{-webkit-animation:fadeIn.5s ease-in-out;-moz-animation:fadeIn.5s ease-in-out;-o-animation:fadeIn.5s ease-in-out;animation:fadeIn.5s ease-in-out}@-webkit-keyframes fadeIn{from{opacity:0;-webkit-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fadeIn{from{opacity:0;-moz-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fadeIn{from{opacity:0;-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fadeIn{from{opacity:0;-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}\"\n                                                    }, void 0, false, void 0, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 110,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-20 h-20 rounded-full bg-primary-500/20 flex items-center justify-center mx-auto mb-4 hover:bg-primary-500/30 transition-colors cursor-pointer\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            size: 36,\n                                                            className: \"text-primary-500 ml-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white text-sm mb-2\",\n                                                        children: \"Clique para iniciar a demonstra\\xe7\\xe3o interativa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-purple-200 text-xs\",\n                                                        children: \"Visualize as principais funcionalidades do m\\xf3dulo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"mr-2 text-purple-500\",\n                                        size: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Se\\xe7\\xf5es do M\\xf3dulo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Calend\\xe1rio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 185,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Visualize e gerencie todos os agendamentos em uma interface intuitiva de calend\\xe1rio. Alterne entre visualiza\\xe7\\xf5es di\\xe1rias, semanais e mensais para melhor organiza\\xe7\\xe3o.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 194,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 200,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Visualiza\\xe7\\xe3o de calend\\xe1rio; Cria\\xe7\\xe3o r\\xe1pida de agendamentos; Filtros avan\\xe7ados; Exporta\\xe7\\xe3o de dados.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 199,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 198,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Hor\\xe1rios de Trabalho\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 216,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Configure os hor\\xe1rios de disponibilidade dos profissionais para agendamentos. Defina hor\\xe1rios personalizados para cada dia da semana.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 225,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 231,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Configura\\xe7\\xe3o de hor\\xe1rios; Sele\\xe7\\xe3o por arraste; C\\xf3pia de hor\\xe1rios; Visualiza\\xe7\\xe3o por profissional.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 230,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 238,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Locais e Salas\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Gerencie os locais e salas dispon\\xedveis para agendamentos. Organize os atendimentos por unidade, andar ou tipo de sala.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 256,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Cadastro de locais; Vincula\\xe7\\xe3o com unidades; Configura\\xe7\\xe3o de capacidade; Status de disponibilidade.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Tipos de Servi\\xe7o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Gerencie os diferentes tipos de servi\\xe7os oferecidos pela sua cl\\xednica. Configure pre\\xe7os, dura\\xe7\\xe3o e profissionais habilitados para cada servi\\xe7o.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 293,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Cadastro de servi\\xe7os; Defini\\xe7\\xe3o de pre\\xe7os; Configura\\xe7\\xe3o de dura\\xe7\\xe3o; Vincula\\xe7\\xe3o com profissionais.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 284,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Ocupa\\xe7\\xe3o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 309,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Analise detalhadamente a ocupa\\xe7\\xe3o dos profissionais, salas e hor\\xe1rios. Identifique per\\xedodos de maior demanda e otimize a agenda.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 324,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" An\\xe1lise de ocupa\\xe7\\xe3o; Gr\\xe1ficos por per\\xedodo; Filtros por profissional; Exporta\\xe7\\xe3o de dados.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Relat\\xf3rios\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Acesse relat\\xf3rios detalhados sobre agendamentos, ocupa\\xe7\\xe3o de salas, produtividade de profissionais e muito mais.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 355,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Relat\\xf3rios personaliz\\xe1veis; Exporta\\xe7\\xe3o em diversos formatos; Listagem de agendamentos; Filtros avan\\xe7ados.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 354,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                            size: 20\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-semibold text-purple-800 dark:text-white\",\n                                                            children: \"Dashboard\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col md:flex-row gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 dark:text-gray-300 text-sm\",\n                                                                    children: \"Visualize estat\\xedsticas e indicadores de desempenho dos agendamentos. Acompanhe m\\xe9tricas importantes para a gest\\xe3o da sua cl\\xednica.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500 dark:text-slate-400\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-semibold\",\n                                                                                children: \"Funcionalidades:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                                lineNumber: 386,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \" Gr\\xe1ficos interativos; Indicadores de desempenho; An\\xe1lise de tend\\xeancias; Filtros por per\\xedodo.\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"md:w-1/3 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    size: 32,\n                                                                    className: \"text-purple-500 dark:text-purple-300\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                            lineNumber: 391,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-purple-800 dark:text-white mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-2 text-purple-600 dark:text-purple-300\",\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 404,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Come\\xe7ando\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-300 mb-4\",\n                                        children: \"Para come\\xe7ar a utilizar o m\\xf3dulo de agendamento, recomendamos seguir estes passos:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                        className: \"space-y-3 text-gray-600 dark:text-gray-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0\",\n                                                        children: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Verifique os \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"hor\\xe1rios de trabalho\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 36\n                                                            }, undefined),\n                                                            \" dos profissionais para garantir que estejam corretamente configurados.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 411,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0\",\n                                                        children: \"2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Confira os \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"locais e salas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 34\n                                                            }, undefined),\n                                                            \" dispon\\xedveis para agendamentos.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 415,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0\",\n                                                        children: \"3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 420,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Acesse o \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"calend\\xe1rio\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 32\n                                                            }, undefined),\n                                                            \" para visualizar e criar novos agendamentos.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 419,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0\",\n                                                        children: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Utilize os \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: \"relat\\xf3rios\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 34\n                                                            }, undefined),\n                                                            \" para acompanhar e analisar os agendamentos realizados.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 423,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 410,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 flex justify-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/dashboard',\n                                                className: \"px-5 py-2.5 bg-white/20 hover:bg-white/30 text-purple-600 border border-purple-300 rounded-lg shadow transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: \"/logo_horizontal_sem_fundo.png\",\n                                                        alt: \"High Tide\",\n                                                        className: \"h-4 w-auto\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Dashboard\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 429,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/dashboard/scheduler/calendar',\n                                                className: \"px-5 py-2.5 bg-purple-600 hover:bg-purple-700 text-white rounded-lg shadow transition-colors flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_ArrowRight_BarChart4_Briefcase_Building_Calendar_CheckCircle_ChevronRight_Clock_Download_Edit_Eye_FileText_Filter_Info_LayoutDashboard_LineChart_MapPin_Pause_PieChart_Play_Plus_Power_RefreshCw_Send_Settings_SlidersHorizontal_Tag_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ir para o Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\scheduler\\\\introduction\\\\IntroductionPage.js\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n_s(IntroductionPage, \"LNRxbAouXl3oydskAmSwn9hqwvE=\");\n_c = IntroductionPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IntroductionPage);\nvar _c;\n$RefreshReg$(_c, \"IntroductionPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/scheduler/introduction/IntroductionPage.js\n"));

/***/ })

});