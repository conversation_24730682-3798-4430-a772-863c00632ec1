import React, { useState } from 'react';
import { Eye, EyeOff, Shield } from 'lucide-react';
import { useDataPrivacy } from '@/hooks/useDataPrivacy';

/**
 * Componente para exibir campos sensíveis com controle de privacidade
 */
const SensitiveField = ({ 
  entityType, 
  fieldName, 
  value, 
  label,
  className = '',
  showToggle = false,
  showIcon = true,
  emptyText = '-',
  copyable = false,
  ...props 
}) => {
  const { applyPrivacyMask, isFieldHidden } = useDataPrivacy();
  const [showOriginal, setShowOriginal] = useState(false);
  
  const isHidden = isFieldHidden(entityType, fieldName);
  const displayValue = showOriginal && showToggle 
    ? value 
    : applyPrivacyMask(entityType, fieldName, value);

  const isEmpty = !value || value === '';
  const isMasked = isHidden && !showOriginal;

  const handleCopy = async () => {
    if (copyable && value) {
      try {
        await navigator.clipboard.writeText(value);
        // Aqui você pode adicionar um toast de sucesso se tiver
      } catch (err) {
        console.error('Erro ao copiar:', err);
      }
    }
  };

  const toggleVisibility = () => {
    setShowOriginal(!showOriginal);
  };

  if (isEmpty) {
    return (
      <span className={`text-gray-400 dark:text-gray-500 ${className}`} {...props}>
        {emptyText}
      </span>
    );
  }

  return (
    <div className={`inline-flex items-center gap-2 ${className}`}>
      {/* Ícone de campo sensível */}
      {showIcon && isMasked && (
        <Shield 
          size={14} 
          className="text-amber-500 dark:text-amber-400 flex-shrink-0" 
          title="Campo sensível - dados mascarados"
        />
      )}
      
      {/* Valor */}
      <span 
        className={`
          ${isMasked ? 'text-gray-600 dark:text-gray-400 font-mono' : ''}
          ${copyable ? 'cursor-pointer hover:underline' : ''}
        `}
        onClick={copyable ? handleCopy : undefined}
        title={copyable ? 'Clique para copiar' : undefined}
        {...props}
      >
        {displayValue || emptyText}
      </span>

      {/* Toggle de visibilidade */}
      {showToggle && isHidden && (
        <button
          type="button"
          onClick={toggleVisibility}
          className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          title={showOriginal ? 'Ocultar dados sensíveis' : 'Mostrar dados originais'}
        >
          {showOriginal ? <EyeOff size={14} /> : <Eye size={14} />}
        </button>
      )}
    </div>
  );
};

/**
 * Componente especializado para CPF/CNPJ
 */
export const SensitiveCpfCnpj = ({ entityType, value, ...props }) => (
  <SensitiveField 
    entityType={entityType}
    fieldName="cpf"
    value={value}
    copyable={true}
    {...props}
  />
);

/**
 * Componente especializado para E-mail
 */
export const SensitiveEmail = ({ entityType, value, ...props }) => (
  <SensitiveField 
    entityType={entityType}
    fieldName="email"
    value={value}
    copyable={true}
    {...props}
  />
);

/**
 * Componente especializado para Telefone
 */
export const SensitivePhone = ({ entityType, value, ...props }) => (
  <SensitiveField 
    entityType={entityType}
    fieldName="phone"
    value={value}
    copyable={true}
    {...props}
  />
);

/**
 * Componente especializado para Endereço
 */
export const SensitiveAddress = ({ entityType, value, ...props }) => (
  <SensitiveField 
    entityType={entityType}
    fieldName="address"
    value={value}
    showToggle={true}
    {...props}
  />
);

/**
 * Componente especializado para Data de Nascimento
 */
export const SensitiveBirthDate = ({ entityType, value, ...props }) => {
  const formatDate = (date) => {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('pt-BR');
  };

  return (
    <SensitiveField 
      entityType={entityType}
      fieldName="birthDate"
      value={formatDate(value)}
      {...props}
    />
  );
};

/**
 * Componente especializado para Nome Completo
 */
export const SensitiveFullName = ({ entityType, value, ...props }) => (
  <SensitiveField 
    entityType={entityType}
    fieldName="fullName"
    value={value}
    showToggle={true}
    {...props}
  />
);

/**
 * Componente especializado para IP
 */
export const SensitiveIp = ({ entityType, value, ...props }) => (
  <SensitiveField 
    entityType={entityType}
    fieldName="lastLoginIp"
    value={value}
    copyable={true}
    {...props}
  />
);

/**
 * Componente especializado para Observações/Notas
 */
export const SensitiveNotes = ({ entityType, value, maxLength = 100, ...props }) => {
  const truncatedValue = value && value.length > maxLength 
    ? value.substring(0, maxLength) + '...'
    : value;

  return (
    <SensitiveField 
      entityType={entityType}
      fieldName="notes"
      value={truncatedValue}
      showToggle={true}
      {...props}
    />
  );
};

/**
 * Componente para exibir avatar/imagem de perfil sensível
 */
export const SensitiveAvatar = ({ 
  entityType, 
  src, 
  alt, 
  size = 40,
  className = '',
  fallbackIcon = null,
  ...props 
}) => {
  const { isFieldHidden } = useDataPrivacy();
  const isHidden = isFieldHidden(entityType, 'profileImage');

  if (isHidden) {
    return (
      <div 
        className={`
          flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded-full
          ${className}
        `}
        style={{ width: size, height: size }}
        title="Imagem de perfil oculta"
        {...props}
      >
        {fallbackIcon || <Shield size={size * 0.6} className="text-gray-500 dark:text-gray-400" />}
      </div>
    );
  }

  if (!src) {
    return (
      <div 
        className={`
          flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded-full text-gray-600 dark:text-gray-300
          ${className}
        `}
        style={{ width: size, height: size }}
        {...props}
      >
        {alt ? alt.charAt(0).toUpperCase() : '?'}
      </div>
    );
  }

  return (
    <img
      src={src}
      alt={alt}
      className={`rounded-full object-cover ${className}`}
      style={{ width: size, height: size }}
      {...props}
    />
  );
};

export default SensitiveField;