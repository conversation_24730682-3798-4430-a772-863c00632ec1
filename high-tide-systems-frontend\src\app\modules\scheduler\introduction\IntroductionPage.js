"use client";

import React, { useState, useEffect } from "react";
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  FileText,
  Settings,
  LayoutDashboard,
  Info,
  Building,
  CheckCircle,
  ArrowRight,
  PieChart,
  Activity,
  Play,
  Pause,
  BarChart4,
  LineChart,
  Tag,
  Briefcase,
  ChevronRight,
  Plus,
  Filter,
  Edit,
  Trash,
  Power,
  RefreshCw,
  XCircle,
  Send,
  Download,
  SlidersHorizontal,
  Eye
} from "lucide-react";
import { ModuleHeader } from "@/components/ui";

const IntroductionPage = () => {
  const [selectedTutorial, setSelectedTutorial] = useState(null);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [totalSlides, setTotalSlides] = useState(8);

  // Configuração dos tutoriais disponíveis
  const tutorials = [
    {
      id: 'calendar',
      title: 'Calendário de Agendamentos',
      description: 'Aprenda a visualizar, criar e gerenciar agendamentos no calendário',
      icon: <Calendar size={20} />,
      color: 'from-purple-600 to-purple-500',
      darkColor: 'from-purple-700 to-purple-600',
      slides: 8,
      features: [
        'Visualizar calendário',
        'Criar agendamentos',
        'Filtrar por profissional',
        'Gerenciar status',
        'Exportar dados'
      ]
    },
    {
      id: 'working-hours',
      title: 'Horários de Trabalho',
      description: 'Configure os horários de disponibilidade dos profissionais',
      icon: <Clock size={20} />,
      color: 'from-blue-600 to-blue-500',
      darkColor: 'from-blue-700 to-blue-600',
      slides: 8,
      features: [
        'Configurar horários',
        'Seleção por arraste',
        'Copiar horários',
        'Visualizar por profissional',
        'Definir exceções'
      ]
    },
    {
      id: 'locations',
      title: 'Locais e Salas',
      description: 'Gerencie os locais e salas disponíveis para agendamentos',
      icon: <MapPin size={20} />,
      color: 'from-green-600 to-green-500',
      darkColor: 'from-green-700 to-green-600',
      slides: 8,
      features: [
        'Cadastrar locais',
        'Vincular com unidades',
        'Configurar capacidade',
        'Status de disponibilidade',
        'Organizar por tipo'
      ]
    },
    {
      id: 'service-types',
      title: 'Tipos de Serviço',
      description: 'Configure os diferentes tipos de serviços oferecidos',
      icon: <Tag size={20} />,
      color: 'from-orange-600 to-orange-500',
      darkColor: 'from-orange-700 to-orange-600',
      slides: 8,
      features: [
        'Cadastrar serviços',
        'Definir preços',
        'Configurar duração',
        'Vincular profissionais',
        'Gerenciar cores'
      ]
    },
    {
      id: 'reports',
      title: 'Relatórios e Dashboard',
      description: 'Analise dados de agendamentos e ocupação através de relatórios',
      icon: <FileText size={20} />,
      color: 'from-red-600 to-red-500',
      darkColor: 'from-red-700 to-red-600',
      slides: 8,
      features: [
        'Relatórios personalizáveis',
        'Dashboard interativo',
        'Análise de ocupação',
        'Exportação de dados',
        'Filtros avançados'
      ]
    }
  ];

  // Auto-advance slides
  useEffect(() => {
    if (!isVideoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % totalSlides);
    }, 7000);

    return () => clearInterval(interval);
  }, [isVideoPlaying, totalSlides]);

  useEffect(() => {
    if (selectedTutorial) {
      setTotalSlides(selectedTutorial.slides);
      setCurrentSlide(0);
    }
  }, [selectedTutorial]);

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Info size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Introdução
        </h1>
      </div>

      {/* Main content */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-scheduler-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-purple-600 to-purple-400 dark:from-purple-700 dark:to-purple-600 px-6 py-4">
          <div className="flex items-center">
            <Calendar className="mr-3 text-white" size={24} aria-hidden="true" />
            <h2 className="text-xl font-bold text-white">Módulo de Agendamento</h2>
          </div>
        </div>

        {/* Introduction text and tutorials */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div>
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                Bem-vindo ao Módulo de Agendamento do High Tide Systems. Este módulo é o centro de gerenciamento de agendamentos,
                permitindo organizar consultas, reuniões e compromissos de forma eficiente.
                Aqui você encontrará todas as ferramentas necessárias para gerenciar sua agenda e a de seus profissionais.
              </p>

            </div>

            {/* Lista de Tutoriais */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-lg">
              <div className="bg-gradient-to-r from-purple-600 to-purple-500 dark:from-purple-700 dark:to-purple-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-white flex items-center">
                    <Play className="mr-2" size={18} />
                    {selectedTutorial ? `Tutorial: ${selectedTutorial.title}` : 'Tutoriais Disponíveis'}
                  </h3>
                  {selectedTutorial && (
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => {
                          setSelectedTutorial(null);
                          setIsVideoPlaying(false);
                        }}
                        className="text-white hover:text-purple-200 transition-colors p-1 rounded"
                        title="Voltar à lista"
                      >
                        <ArrowRight size={18} className="rotate-180" />
                      </button>
                      <button
                        onClick={() => setIsVideoPlaying(!isVideoPlaying)}
                        className="text-white hover:text-purple-200 transition-colors p-1 rounded"
                        aria-label={isVideoPlaying ? "Pausar tutorial" : "Iniciar tutorial"}
                      >
                        {isVideoPlaying ? <Pause size={18} /> : <Play size={18} />}
                      </button>
                    </div>
                  )}
                </div>
              </div>
              <div className="relative aspect-video bg-gradient-to-br from-purple-900 to-purple-800 overflow-hidden">
                {!selectedTutorial ? (
                  // Lista de tutoriais
                  <div className="p-6 h-full overflow-y-auto">
                    <div className="grid grid-cols-1 gap-4 h-full">
                      {tutorials.map((tutorial) => (
                        <div
                          key={tutorial.id}
                          onClick={() => setSelectedTutorial(tutorial)}
                          className="bg-white/10 backdrop-blur-sm rounded-lg p-4 cursor-pointer hover:bg-white/20 transition-all duration-200 border border-white/20 hover:border-white/30"
                        >
                          <div className="flex items-start gap-3">
                            <div className={`p-2 rounded-lg bg-gradient-to-r ${tutorial.color} dark:${tutorial.darkColor}`}>
                              {tutorial.icon}
                            </div>
                            <div className="flex-1">
                              <h4 className="text-white font-semibold text-sm mb-1">{tutorial.title}</h4>
                              <p className="text-purple-200 text-xs mb-3">{tutorial.description}</p>
                              <div className="grid grid-cols-2 gap-1">
                                {tutorial.features.map((feature, index) => (
                                  <div key={index} className="flex items-center gap-1 text-xs text-green-300">
                                    <div className="w-1 h-1 rounded-full bg-green-400"></div>
                                    <span>{feature}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                            <div className="text-white/60">
                              <ChevronRight size={16} />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : isVideoPlaying ? (
                  <div className="w-full h-full relative">
                    {/* Slideshow navigation */}
                    <div className="absolute bottom-0 left-0 right-0 bg-black/50 p-3 z-20">
                      <div className="flex items-center justify-center gap-2">
                        {Array.from({length: totalSlides}).map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentSlide(index)}
                            className={`w-2 h-2 rounded-full transition-all duration-300 ${
                              currentSlide === index ? 'bg-purple-500 w-6' : 'bg-gray-400 hover:bg-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>

                    {/* Conteúdo do tutorial */}
                    <div className="w-full h-full flex items-center justify-center relative">
                      <div className="tutorial-content w-full h-full relative overflow-hidden">

                        {/* Tutorial de Calendário */}
                        {selectedTutorial?.id === 'calendar' && (
                          <>
                            {/* Slide 1: Página do Calendário */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                    <Calendar size={20} className="mr-2 text-slate-600" />
                                    Calendário
                                  </h1>
                                </div>
                                <div className="p-3">
                                  <div className="bg-purple-50 rounded p-4 mb-3">
                                    <div className="grid grid-cols-7 gap-1 text-xs">
                                      <div className="text-center font-semibold">Dom</div>
                                      <div className="text-center font-semibold">Seg</div>
                                      <div className="text-center font-semibold">Ter</div>
                                      <div className="text-center font-semibold">Qua</div>
                                      <div className="text-center font-semibold">Qui</div>
                                      <div className="text-center font-semibold">Sex</div>
                                      <div className="text-center font-semibold">Sáb</div>
                                      {Array.from({length: 35}).map((_, i) => (
                                        <div key={i} className="aspect-square flex items-center justify-center text-xs border rounded hover:bg-purple-100">
                                          {i + 1 <= 30 ? i + 1 : ''}
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="flex items-center gap-2 p-2 bg-blue-50 rounded border-l-4 border-blue-400">
                                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                                      <div className="text-xs">
                                        <div className="font-medium">Consulta - Dr. Silva</div>
                                        <div className="text-gray-500">09:00 - 10:00</div>
                                      </div>
                                    </div>
                                    <div className="flex items-center gap-2 p-2 bg-green-50 rounded border-l-4 border-green-400">
                                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                                      <div className="text-xs">
                                        <div className="font-medium">Terapia - Dra. Ana</div>
                                        <div className="text-gray-500">14:00 - 15:00</div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 2: Criando Agendamento */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Novo Agendamento</h1>
                                </div>
                                <div className="p-3 space-y-3">
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Paciente</label>
                                    <select className="w-full p-2 border rounded text-xs">
                                      <option>Maria Silva</option>
                                    </select>
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Profissional</label>
                                    <select className="w-full p-2 border rounded text-xs">
                                      <option>Dr. João Santos</option>
                                    </select>
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Data e Hora</label>
                                    <input type="datetime-local" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Tipo de Serviço</label>
                                    <select className="w-full p-2 border rounded text-xs">
                                      <option>Consulta Médica</option>
                                    </select>
                                  </div>
                                  <div className="flex gap-2 pt-2">
                                    <button className="flex-1 px-3 py-2 bg-purple-500 text-white rounded text-xs">
                                      Agendar
                                    </button>
                                    <button className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-xs">
                                      Cancelar
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 3: Filtros do Calendário */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                    <Filter size={20} className="mr-2 text-slate-600" />
                                    Filtros
                                  </h1>
                                </div>
                                <div className="p-3 space-y-3">
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Profissionais</label>
                                    <div className="space-y-1">
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" checked />
                                        Dr. João Santos
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" checked />
                                        Dra. Ana Costa
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" />
                                        Dr. Pedro Lima
                                      </label>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Status</label>
                                    <div className="space-y-1">
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" checked />
                                        Agendado
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" checked />
                                        Confirmado
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" />
                                        Cancelado
                                      </label>
                                    </div>
                                  </div>
                                  <button className="w-full px-3 py-2 bg-purple-500 text-white rounded text-xs">
                                    Aplicar Filtros
                                  </button>
                                </div>
                              </div>
                            </div>

                            {/* Slides adicionais para completar o tutorial do calendário */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Calendar size={64} className="mx-auto mb-4 text-purple-300" />
                                <h3 className="text-xl font-bold mb-2">Visualizações do Calendário</h3>
                                <p className="text-purple-200">Alterne entre visualizações diária, semanal e mensal para melhor organização</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Eye size={64} className="mx-auto mb-4 text-purple-300" />
                                <h3 className="text-xl font-bold mb-2">Detalhes do Agendamento</h3>
                                <p className="text-purple-200">Clique em qualquer agendamento para ver detalhes completos e fazer alterações</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <RefreshCw size={64} className="mx-auto mb-4 text-purple-300" />
                                <h3 className="text-xl font-bold mb-2">Atualização Automática</h3>
                                <p className="text-purple-200">O calendário se atualiza automaticamente quando novos agendamentos são criados</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Download size={64} className="mx-auto mb-4 text-purple-300" />
                                <h3 className="text-xl font-bold mb-2">Exportação de Dados</h3>
                                <p className="text-purple-200">Exporte seus agendamentos em diversos formatos para análise externa</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <CheckCircle size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Tutorial Concluído!</h3>
                                <p className="text-purple-200">Agora você está pronto para usar o calendário de agendamentos</p>
                              </div>
                            </div>
                          </>
                        )}

                        {/* Tutorial de Horários de Trabalho */}
                        {selectedTutorial?.id === 'working-hours' && (
                          <>
                            {/* Slide 1: Grade de Horários */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                    <Clock size={20} className="mr-2 text-slate-600" />
                                    Horários de Trabalho
                                  </h1>
                                </div>
                                <div className="p-3">
                                  <div className="mb-3">
                                    <select className="w-full p-2 border rounded text-xs">
                                      <option>Dr. João Santos</option>
                                    </select>
                                  </div>
                                  <div className="bg-blue-50 rounded p-2">
                                    <table className="w-full text-xs">
                                      <thead>
                                        <tr>
                                          <th className="text-left p-1">Horário</th>
                                          <th className="text-center p-1">Seg</th>
                                          <th className="text-center p-1">Ter</th>
                                          <th className="text-center p-1">Qua</th>
                                          <th className="text-center p-1">Qui</th>
                                          <th className="text-center p-1">Sex</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        <tr>
                                          <td className="p-1">09:00</td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-gray-200 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                        </tr>
                                        <tr>
                                          <td className="p-1">10:00</td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-gray-200 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                        </tr>
                                        <tr>
                                          <td className="p-1">14:00</td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-gray-200 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                  <div className="mt-3 text-xs text-gray-600">
                                    <div className="flex items-center gap-2 mb-1">
                                      <div className="w-3 h-3 bg-green-400 rounded"></div>
                                      <span>Disponível</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <div className="w-3 h-3 bg-gray-200 rounded"></div>
                                      <span>Indisponível</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 2: Configurando Horários */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <SlidersHorizontal size={64} className="mx-auto mb-4 text-blue-300" />
                                <h3 className="text-xl font-bold mb-2">Configuração Flexível</h3>
                                <p className="text-purple-200">Clique e arraste para selecionar múltiplos horários de uma vez</p>
                              </div>
                            </div>

                            {/* Slide 3: Copiando Horários */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <RefreshCw size={64} className="mx-auto mb-4 text-blue-300" />
                                <h3 className="text-xl font-bold mb-2">Copiar Horários</h3>
                                <p className="text-purple-200">Copie horários de um dia para outros dias da semana rapidamente</p>
                              </div>
                            </div>

                            {/* Slide 4: Múltiplos Profissionais */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Users size={64} className="mx-auto mb-4 text-blue-300" />
                                <h3 className="text-xl font-bold mb-2">Múltiplos Profissionais</h3>
                                <p className="text-purple-200">Configure horários para vários profissionais simultaneamente</p>
                              </div>
                            </div>

                            {/* Slide 5: Exceções */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <XCircle size={64} className="mx-auto mb-4 text-blue-300" />
                                <h3 className="text-xl font-bold mb-2">Exceções e Feriados</h3>
                                <p className="text-purple-200">Defina exceções para feriados e dias específicos</p>
                              </div>
                            </div>

                            {/* Slide 6: Visualização */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Eye size={64} className="mx-auto mb-4 text-blue-300" />
                                <h3 className="text-xl font-bold mb-2">Visualização Clara</h3>
                                <p className="text-purple-200">Interface intuitiva com cores para identificar rapidamente a disponibilidade</p>
                              </div>
                            </div>

                            {/* Slide 7: Integração */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Calendar size={64} className="mx-auto mb-4 text-blue-300" />
                                <h3 className="text-xl font-bold mb-2">Integração com Calendário</h3>
                                <p className="text-purple-200">Os horários configurados são automaticamente aplicados no calendário</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <CheckCircle size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Tutorial Concluído!</h3>
                                <p className="text-purple-200">Agora você pode configurar horários de trabalho eficientemente</p>
                              </div>
                            </div>
                          </>
                        )}

                        {/* Tutorial de Locais */}
                        {selectedTutorial?.id === 'locations' && (
                          <>
                            {/* Slide 1: Lista de Locais */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <div className="flex justify-between items-center">
                                    <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                      <MapPin size={20} className="mr-2 text-slate-600" />
                                      Locais
                                    </h1>
                                    <button className="flex items-center gap-1 px-2 py-1 bg-green-500 text-white rounded text-xs">
                                      <Plus size={12} />
                                      Novo
                                    </button>
                                  </div>
                                </div>
                                <div className="p-3">
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between p-2 bg-green-50 rounded border">
                                      <div className="flex items-center gap-2">
                                        <div className="w-8 h-8 bg-green-200 rounded-full flex items-center justify-center">
                                          <MapPin size={14} className="text-green-600" />
                                        </div>
                                        <div>
                                          <div className="text-sm font-medium">Sala 101</div>
                                          <div className="text-xs text-gray-500">Consultório Principal</div>
                                        </div>
                                      </div>
                                      <div className="flex gap-1">
                                        <button className="p-1 text-blue-600"><Edit size={12} /></button>
                                        <button className="p-1 text-red-600"><Trash size={12} /></button>
                                      </div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-green-50 rounded border">
                                      <div className="flex items-center gap-2">
                                        <div className="w-8 h-8 bg-green-200 rounded-full flex items-center justify-center">
                                          <MapPin size={14} className="text-green-600" />
                                        </div>
                                        <div>
                                          <div className="text-sm font-medium">Sala 102</div>
                                          <div className="text-xs text-gray-500">Sala de Terapia</div>
                                        </div>
                                      </div>
                                      <div className="flex gap-1">
                                        <button className="p-1 text-blue-600"><Edit size={12} /></button>
                                        <button className="p-1 text-red-600"><Trash size={12} /></button>
                                      </div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded border">
                                      <div className="flex items-center gap-2">
                                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                          <MapPin size={14} className="text-gray-600" />
                                        </div>
                                        <div>
                                          <div className="text-sm font-medium">Sala 103</div>
                                          <div className="text-xs text-gray-500">Em Manutenção</div>
                                        </div>
                                      </div>
                                      <div className="flex gap-1">
                                        <button className="p-1 text-blue-600"><Edit size={12} /></button>
                                        <button className="p-1 text-red-600"><Trash size={12} /></button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 2: Cadastrando Local */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Novo Local</h1>
                                </div>
                                <div className="p-3 space-y-3">
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Nome do Local</label>
                                    <input type="text" placeholder="Ex: Sala 104" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Descrição</label>
                                    <input type="text" placeholder="Ex: Consultório Médico" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Capacidade</label>
                                    <input type="number" placeholder="1" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Unidade</label>
                                    <select className="w-full p-2 border rounded text-xs">
                                      <option>Unidade Central</option>
                                    </select>
                                  </div>
                                  <div>
                                    <label className="flex items-center gap-2 text-xs">
                                      <input type="checkbox" className="rounded" checked />
                                      Local ativo
                                    </label>
                                  </div>
                                  <div className="flex gap-2 pt-2">
                                    <button className="flex-1 px-3 py-2 bg-green-500 text-white rounded text-xs">
                                      Salvar
                                    </button>
                                    <button className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-xs">
                                      Cancelar
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slides adicionais para locais */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Building size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Organização por Unidades</h3>
                                <p className="text-purple-200">Organize seus locais por unidades, andares ou tipos de atendimento</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Settings size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Configuração de Capacidade</h3>
                                <p className="text-purple-200">Defina quantas pessoas podem ser atendidas simultaneamente em cada local</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Power size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Status de Disponibilidade</h3>
                                <p className="text-purple-200">Ative ou desative locais conforme necessário (manutenção, reforma, etc.)</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Filter size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Filtros e Busca</h3>
                                <p className="text-purple-200">Encontre rapidamente locais específicos usando filtros e busca</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Calendar size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Integração com Agendamentos</h3>
                                <p className="text-purple-200">Os locais aparecem automaticamente nas opções de agendamento</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <CheckCircle size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Tutorial Concluído!</h3>
                                <p className="text-purple-200">Agora você pode gerenciar locais e salas eficientemente</p>
                              </div>
                            </div>
                          </>
                        )}

                        {/* Tutorial de Tipos de Serviço */}
                        {selectedTutorial?.id === 'service-types' && (
                          <>
                            {/* Slide 1: Lista de Tipos de Serviço */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <div className="flex justify-between items-center">
                                    <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                      <Tag size={20} className="mr-2 text-slate-600" />
                                      Tipos de Serviço
                                    </h1>
                                    <button className="flex items-center gap-1 px-2 py-1 bg-orange-500 text-white rounded text-xs">
                                      <Plus size={12} />
                                      Novo
                                    </button>
                                  </div>
                                </div>
                                <div className="p-3">
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between p-2 bg-blue-50 rounded border-l-4 border-blue-400">
                                      <div className="flex items-center gap-2">
                                        <div className="w-4 h-4 bg-blue-400 rounded"></div>
                                        <div>
                                          <div className="text-sm font-medium">Consulta Médica</div>
                                          <div className="text-xs text-gray-500">60 min - R$ 150,00</div>
                                        </div>
                                      </div>
                                      <div className="flex gap-1">
                                        <button className="p-1 text-blue-600"><Edit size={12} /></button>
                                        <button className="p-1 text-red-600"><Trash size={12} /></button>
                                      </div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-green-50 rounded border-l-4 border-green-400">
                                      <div className="flex items-center gap-2">
                                        <div className="w-4 h-4 bg-green-400 rounded"></div>
                                        <div>
                                          <div className="text-sm font-medium">Terapia</div>
                                          <div className="text-xs text-gray-500">45 min - R$ 120,00</div>
                                        </div>
                                      </div>
                                      <div className="flex gap-1">
                                        <button className="p-1 text-blue-600"><Edit size={12} /></button>
                                        <button className="p-1 text-red-600"><Trash size={12} /></button>
                                      </div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-purple-50 rounded border-l-4 border-purple-400">
                                      <div className="flex items-center gap-2">
                                        <div className="w-4 h-4 bg-purple-400 rounded"></div>
                                        <div>
                                          <div className="text-sm font-medium">Avaliação</div>
                                          <div className="text-xs text-gray-500">90 min - R$ 200,00</div>
                                        </div>
                                      </div>
                                      <div className="flex gap-1">
                                        <button className="p-1 text-blue-600"><Edit size={12} /></button>
                                        <button className="p-1 text-red-600"><Trash size={12} /></button>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 2: Cadastrando Tipo de Serviço */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Novo Tipo de Serviço</h1>
                                </div>
                                <div className="p-3 space-y-3">
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Nome do Serviço</label>
                                    <input type="text" placeholder="Ex: Consulta Psicológica" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Duração (minutos)</label>
                                    <input type="number" placeholder="60" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Preço</label>
                                    <input type="number" placeholder="150.00" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Cor</label>
                                    <div className="flex gap-2">
                                      <div className="w-8 h-8 bg-blue-400 rounded cursor-pointer border-2 border-blue-600"></div>
                                      <div className="w-8 h-8 bg-green-400 rounded cursor-pointer"></div>
                                      <div className="w-8 h-8 bg-purple-400 rounded cursor-pointer"></div>
                                      <div className="w-8 h-8 bg-orange-400 rounded cursor-pointer"></div>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Profissionais Habilitados</label>
                                    <div className="space-y-1">
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" checked />
                                        Dr. João Santos
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" />
                                        Dra. Ana Costa
                                      </label>
                                    </div>
                                  </div>
                                  <div className="flex gap-2 pt-2">
                                    <button className="flex-1 px-3 py-2 bg-orange-500 text-white rounded text-xs">
                                      Salvar
                                    </button>
                                    <button className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-xs">
                                      Cancelar
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slides adicionais para tipos de serviço */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Clock size={64} className="mx-auto mb-4 text-orange-300" />
                                <h3 className="text-xl font-bold mb-2">Duração Personalizada</h3>
                                <p className="text-purple-200">Configure durações específicas para cada tipo de serviço</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Activity size={64} className="mx-auto mb-4 text-orange-300" />
                                <h3 className="text-xl font-bold mb-2">Cores Identificadoras</h3>
                                <p className="text-purple-200">Use cores para identificar rapidamente tipos de serviço no calendário</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Users size={64} className="mx-auto mb-4 text-orange-300" />
                                <h3 className="text-xl font-bold mb-2">Profissionais Habilitados</h3>
                                <p className="text-purple-200">Defina quais profissionais podem realizar cada tipo de serviço</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <BarChart4 size={64} className="mx-auto mb-4 text-orange-300" />
                                <h3 className="text-xl font-bold mb-2">Controle de Preços</h3>
                                <p className="text-purple-200">Gerencie preços e gere relatórios financeiros por tipo de serviço</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Calendar size={64} className="mx-auto mb-4 text-orange-300" />
                                <h3 className="text-xl font-bold mb-2">Integração Automática</h3>
                                <p className="text-purple-200">Tipos de serviço aparecem automaticamente nas opções de agendamento</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <CheckCircle size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Tutorial Concluído!</h3>
                                <p className="text-purple-200">Agora você pode gerenciar tipos de serviço eficientemente</p>
                              </div>
                            </div>
                          </>
                        )}

                        {/* Tutorial de Relatórios */}
                        {selectedTutorial?.id === 'reports' && (
                          <>
                            {/* Slide 1: Dashboard de Relatórios */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                    <LayoutDashboard size={20} className="mr-2 text-slate-600" />
                                    Dashboard
                                  </h1>
                                </div>
                                <div className="p-3">
                                  <div className="grid grid-cols-2 gap-3 mb-4">
                                    <div className="bg-blue-50 p-3 rounded border">
                                      <div className="text-xs text-gray-600">Agendamentos Hoje</div>
                                      <div className="text-lg font-bold text-blue-600">24</div>
                                    </div>
                                    <div className="bg-green-50 p-3 rounded border">
                                      <div className="text-xs text-gray-600">Taxa de Ocupação</div>
                                      <div className="text-lg font-bold text-green-600">85%</div>
                                    </div>
                                    <div className="bg-orange-50 p-3 rounded border">
                                      <div className="text-xs text-gray-600">Receita Mensal</div>
                                      <div className="text-lg font-bold text-orange-600">R$ 45k</div>
                                    </div>
                                    <div className="bg-purple-50 p-3 rounded border">
                                      <div className="text-xs text-gray-600">Profissionais Ativos</div>
                                      <div className="text-lg font-bold text-purple-600">8</div>
                                    </div>
                                  </div>
                                  <div className="bg-gray-50 p-3 rounded">
                                    <div className="text-xs font-medium text-gray-700 mb-2">Agendamentos por Dia</div>
                                    <div className="flex items-end gap-1 h-16">
                                      <div className="bg-blue-400 w-4" style={{height: '60%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '80%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '40%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '90%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '70%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '50%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '30%'}}></div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 2: Relatório de Agendamentos */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <div className="flex justify-between items-center">
                                    <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                      <FileText size={20} className="mr-2 text-slate-600" />
                                      Relatório de Agendamentos
                                    </h1>
                                    <button className="flex items-center gap-1 px-2 py-1 bg-red-500 text-white rounded text-xs">
                                      <Download size={12} />
                                      Exportar
                                    </button>
                                  </div>
                                </div>
                                <div className="p-3">
                                  <div className="mb-3 flex gap-2">
                                    <select className="flex-1 p-1 border rounded text-xs">
                                      <option>Últimos 30 dias</option>
                                    </select>
                                    <select className="flex-1 p-1 border rounded text-xs">
                                      <option>Todos os profissionais</option>
                                    </select>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                                      <div>
                                        <div className="font-medium">Maria Silva</div>
                                        <div className="text-gray-500">Dr. João - 15/03 09:00</div>
                                      </div>
                                      <div className="text-green-600 font-medium">Realizado</div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                                      <div>
                                        <div className="font-medium">Pedro Santos</div>
                                        <div className="text-gray-500">Dra. Ana - 15/03 10:00</div>
                                      </div>
                                      <div className="text-blue-600 font-medium">Agendado</div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                                      <div>
                                        <div className="font-medium">Ana Costa</div>
                                        <div className="text-gray-500">Dr. João - 15/03 11:00</div>
                                      </div>
                                      <div className="text-red-600 font-medium">Cancelado</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slides adicionais para relatórios */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <PieChart size={64} className="mx-auto mb-4 text-red-300" />
                                <h3 className="text-xl font-bold mb-2">Gráficos Interativos</h3>
                                <p className="text-purple-200">Visualize dados em gráficos de pizza, barras e linhas</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Filter size={64} className="mx-auto mb-4 text-red-300" />
                                <h3 className="text-xl font-bold mb-2">Filtros Avançados</h3>
                                <p className="text-purple-200">Filtre por período, profissional, paciente, status e muito mais</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Download size={64} className="mx-auto mb-4 text-red-300" />
                                <h3 className="text-xl font-bold mb-2">Exportação Flexível</h3>
                                <p className="text-purple-200">Exporte relatórios em PDF, Excel e outros formatos</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <Activity size={64} className="mx-auto mb-4 text-red-300" />
                                <h3 className="text-xl font-bold mb-2">Análise de Ocupação</h3>
                                <p className="text-purple-200">Analise a ocupação de profissionais, salas e horários</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <BarChart4 size={64} className="mx-auto mb-4 text-red-300" />
                                <h3 className="text-xl font-bold mb-2">Indicadores de Performance</h3>
                                <p className="text-purple-200">Acompanhe KPIs importantes para sua clínica</p>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <CheckCircle size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Tutorial Concluído!</h3>
                                <p className="text-purple-200">Agora você pode gerar relatórios e analisar dados eficientemente</p>
                              </div>
                            </div>
                          </>
                        )}

                      </div>
                    </div>
                  </div>
                ) : (
                  // Tela de seleção quando tutorial está selecionado mas não está tocando
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center text-white p-8">
                      <div className={`w-20 h-20 rounded-full bg-gradient-to-r ${selectedTutorial.color} dark:${selectedTutorial.darkColor} flex items-center justify-center mx-auto mb-4`}>
                        {selectedTutorial.icon}
                      </div>
                      <h3 className="text-xl font-bold mb-2">{selectedTutorial.title}</h3>
                      <p className="text-purple-200 text-sm mb-4">{selectedTutorial.description}</p>
                      <button
                        onClick={() => setIsVideoPlaying(true)}
                        className="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors flex items-center gap-2 mx-auto"
                      >
                        <Play size={16} />
                        Iniciar Tutorial
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>



          {/* Section cards */}
          <h3 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center">
            <Info className="mr-2 text-purple-500" size={20} />
            Seções do Módulo
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            {/* Calendar section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Calendar className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Calendário</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Visualize e gerencie todos os agendamentos em uma interface intuitiva de calendário.
                      Alterne entre visualizações diárias, semanais e mensais para melhor organização.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Visualização de calendário;
                        Criação rápida de agendamentos; Filtros avançados; Exportação de dados.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Calendar size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Working Hours section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Clock className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Horários de Trabalho</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Configure os horários de disponibilidade dos profissionais para agendamentos.
                      Defina horários personalizados para cada dia da semana.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Configuração de horários;
                        Seleção por arraste; Cópia de horários; Visualização por profissional.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Clock size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Locations section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <MapPin className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Locais e Salas</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie os locais e salas disponíveis para agendamentos.
                      Organize os atendimentos por unidade, andar ou tipo de sala.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro de locais;
                        Vinculação com unidades; Configuração de capacidade; Status de disponibilidade.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <MapPin size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Service Types section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Tag className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Tipos de Serviço</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie os diferentes tipos de serviços oferecidos pela sua clínica.
                      Configure preços, duração e profissionais habilitados para cada serviço.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro de serviços;
                        Definição de preços; Configuração de duração; Vinculação com profissionais.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Tag size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Occupancy section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Briefcase className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Ocupação</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Analise detalhadamente a ocupação dos profissionais, salas e horários.
                      Identifique períodos de maior demanda e otimize a agenda.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Análise de ocupação;
                        Gráficos por período; Filtros por profissional; Exportação de dados.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Briefcase size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Reports section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <FileText className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Relatórios</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Acesse relatórios detalhados sobre agendamentos, ocupação de salas,
                      produtividade de profissionais e muito mais.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Relatórios personalizáveis;
                        Exportação em diversos formatos; Listagem de agendamentos; Filtros avançados.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <FileText size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Dashboard section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <LayoutDashboard className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Dashboard</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Visualize estatísticas e indicadores de desempenho dos agendamentos.
                      Acompanhe métricas importantes para a gestão da sua clínica.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Gráficos interativos;
                        Indicadores de desempenho; Análise de tendências; Filtros por período.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <BarChart4 size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Getting started section */}
          <div className="mt-8 bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 p-6">
            <h3 className="text-lg font-semibold text-purple-800 dark:text-white mb-4 flex items-center">
              <LayoutDashboard className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
              Começando
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Para começar a utilizar o módulo de agendamento, recomendamos seguir estes passos:
            </p>
            <ol className="space-y-3 text-gray-600 dark:text-gray-300">
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">1</span>
                <span>Verifique os <strong>horários de trabalho</strong> dos profissionais para garantir que estejam corretamente configurados.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">2</span>
                <span>Confira os <strong>locais e salas</strong> disponíveis para agendamentos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">3</span>
                <span>Acesse o <strong>calendário</strong> para visualizar e criar novos agendamentos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">4</span>
                <span>Utilize os <strong>relatórios</strong> para acompanhar e analisar os agendamentos realizados.</span>
              </li>
            </ol>
            <div className="mt-6 flex justify-center gap-4">
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="px-5 py-2.5 bg-white/20 hover:bg-white/30 text-purple-600 border border-purple-300 rounded-lg shadow transition-colors flex items-center gap-2"
              >
                <img
                  src="/logo_horizontal_sem_fundo.png"
                  alt="High Tide"
                  className="h-4 w-auto"
                />
                Dashboard
              </button>
              <button
                onClick={() => window.location.href = '/dashboard/scheduler/calendar'}
                className="px-5 py-2.5 bg-purple-600 hover:bg-purple-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
              >
                <Calendar size={18} />
                Ir para o Calendário
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntroductionPage;
