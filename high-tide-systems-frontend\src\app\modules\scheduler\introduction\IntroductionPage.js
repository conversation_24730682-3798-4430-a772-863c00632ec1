"use client";

import React, { useState, useEffect } from "react";
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  FileText,
  Settings,
  LayoutDashboard,
  Info,
  Building,
  CheckCircle,
  ArrowRight,
  PieChart,
  Activity,
  Play,
  Pause,
  BarChart4,
  LineChart,
  Tag,
  Briefcase,
  ChevronRight,
  Plus,
  Filter,
  Edit,
  Trash,
  Power,
  RefreshCw,
  XCircle,
  Send,
  Download,
  SlidersHorizontal,
  Eye,
  Search,
  Share2
} from "lucide-react";
import { ModuleHeader } from "@/components/ui";

const IntroductionPage = () => {
  const [selectedTutorial, setSelectedTutorial] = useState(null);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [totalSlides, setTotalSlides] = useState(8);

  // Configuração dos tutoriais disponíveis
  const tutorials = [
    {
      id: 'calendar',
      title: 'Calendário de Agendamentos',
      description: 'Aprenda a visualizar, criar e gerenciar agendamentos no calendário',
      icon: <Calendar size={20} />,
      color: 'from-purple-600 to-purple-500',
      darkColor: 'from-purple-700 to-purple-600',
      slides: 8,
      features: [
        'Visualizar calendário',
        'Criar agendamentos',
        'Filtrar por profissional',
        'Gerenciar status',
        'Exportar dados'
      ]
    },
    {
      id: 'working-hours',
      title: 'Horários de Trabalho',
      description: 'Configure os horários de disponibilidade dos profissionais',
      icon: <Clock size={20} />,
      color: 'from-blue-600 to-blue-500',
      darkColor: 'from-blue-700 to-blue-600',
      slides: 8,
      features: [
        'Configurar horários',
        'Seleção por arraste',
        'Copiar horários',
        'Visualizar por profissional',
        'Definir exceções'
      ]
    },
    {
      id: 'locations',
      title: 'Locais e Salas',
      description: 'Gerencie os locais e salas disponíveis para agendamentos',
      icon: <MapPin size={20} />,
      color: 'from-green-600 to-green-500',
      darkColor: 'from-green-700 to-green-600',
      slides: 8,
      features: [
        'Cadastrar locais',
        'Vincular com unidades',
        'Configurar capacidade',
        'Status de disponibilidade',
        'Organizar por tipo'
      ]
    },
    {
      id: 'service-types',
      title: 'Tipos de Serviço',
      description: 'Configure os diferentes tipos de serviços oferecidos',
      icon: <Tag size={20} />,
      color: 'from-orange-600 to-orange-500',
      darkColor: 'from-orange-700 to-orange-600',
      slides: 8,
      features: [
        'Cadastrar serviços',
        'Definir preços',
        'Configurar duração',
        'Vincular profissionais',
        'Gerenciar cores'
      ]
    },
    {
      id: 'reports',
      title: 'Relatórios e Dashboard',
      description: 'Analise dados de agendamentos e ocupação através de relatórios',
      icon: <FileText size={20} />,
      color: 'from-red-600 to-red-500',
      darkColor: 'from-red-700 to-red-600',
      slides: 8,
      features: [
        'Relatórios personalizáveis',
        'Dashboard interativo',
        'Análise de ocupação',
        'Exportação de dados',
        'Filtros avançados'
      ]
    }
  ];

  // Auto-advance slides
  useEffect(() => {
    if (!isVideoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % totalSlides);
    }, 7000);

    return () => clearInterval(interval);
  }, [isVideoPlaying, totalSlides]);

  useEffect(() => {
    if (selectedTutorial) {
      setTotalSlides(selectedTutorial.slides);
      setCurrentSlide(0);
    }
  }, [selectedTutorial]);

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Info size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Introdução
        </h1>
      </div>

      {/* Main content */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-scheduler-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-purple-600 to-purple-400 dark:from-purple-700 dark:to-purple-600 px-6 py-4">
          <div className="flex items-center">
            <Calendar className="mr-3 text-white" size={24} aria-hidden="true" />
            <h2 className="text-xl font-bold text-white">Módulo de Agendamento</h2>
          </div>
        </div>

        {/* Introduction text and tutorials */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div>
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                Bem-vindo ao Módulo de Agendamento do High Tide Systems. Este módulo é o centro de gerenciamento de agendamentos,
                permitindo organizar consultas, reuniões e compromissos de forma eficiente.
                Aqui você encontrará todas as ferramentas necessárias para gerenciar sua agenda e a de seus profissionais.
              </p>

            </div>

            {/* Lista de Tutoriais */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-lg">
              <div className="bg-gradient-to-r from-purple-600 to-purple-500 dark:from-purple-700 dark:to-purple-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-white flex items-center">
                    <Play className="mr-2" size={18} />
                    {selectedTutorial ? `Tutorial: ${selectedTutorial.title}` : 'Tutoriais Disponíveis'}
                  </h3>
                  {selectedTutorial && (
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => {
                          setSelectedTutorial(null);
                          setIsVideoPlaying(false);
                        }}
                        className="text-white hover:text-purple-200 transition-colors p-1 rounded"
                        title="Voltar à lista"
                      >
                        <ArrowRight size={18} className="rotate-180" />
                      </button>
                      <button
                        onClick={() => setIsVideoPlaying(!isVideoPlaying)}
                        className="text-white hover:text-purple-200 transition-colors p-1 rounded"
                        aria-label={isVideoPlaying ? "Pausar tutorial" : "Iniciar tutorial"}
                      >
                        {isVideoPlaying ? <Pause size={18} /> : <Play size={18} />}
                      </button>
                    </div>
                  )}
                </div>
              </div>
              <div className="relative aspect-video bg-gradient-to-br from-purple-900 to-purple-800 overflow-hidden">
                {!selectedTutorial ? (
                  // Lista de tutoriais
                  <div className="p-6 h-full overflow-y-auto">
                    <div className="grid grid-cols-1 gap-4 h-full">
                      {tutorials.map((tutorial) => (
                        <div
                          key={tutorial.id}
                          onClick={() => setSelectedTutorial(tutorial)}
                          className="bg-white/10 backdrop-blur-sm rounded-lg p-4 cursor-pointer hover:bg-white/20 transition-all duration-200 border border-white/20 hover:border-white/30"
                        >
                          <div className="flex items-start gap-3">
                            <div className={`p-2 rounded-lg bg-gradient-to-r ${tutorial.color} dark:${tutorial.darkColor}`}>
                              {tutorial.icon}
                            </div>
                            <div className="flex-1">
                              <h4 className="text-white font-semibold text-sm mb-1">{tutorial.title}</h4>
                              <p className="text-purple-200 text-xs mb-3">{tutorial.description}</p>
                              <div className="grid grid-cols-2 gap-1">
                                {tutorial.features.map((feature, index) => (
                                  <div key={index} className="flex items-center gap-1 text-xs text-green-300">
                                    <div className="w-1 h-1 rounded-full bg-green-400"></div>
                                    <span>{feature}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                            <div className="text-white/60">
                              <ChevronRight size={16} />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : isVideoPlaying ? (
                  <div className="w-full h-full relative">
                    {/* Slideshow navigation */}
                    <div className="absolute bottom-0 left-0 right-0 bg-black/50 p-3 z-20">
                      <div className="flex items-center justify-center gap-2">
                        {Array.from({length: totalSlides}).map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentSlide(index)}
                            className={`w-2 h-2 rounded-full transition-all duration-300 ${
                              currentSlide === index ? 'bg-purple-500 w-6' : 'bg-gray-400 hover:bg-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>

                    {/* Conteúdo do tutorial */}
                    <div className="w-full h-full flex items-center justify-center relative">
                      <div className="tutorial-content w-full h-full relative overflow-hidden">

                        {/* Tutorial de Calendário */}
                        {selectedTutorial?.id === 'calendar' && (
                          <>
                            {/* Slide 1: Interface Real do Calendário */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-5xl h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                {/* Header da página */}
                                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                                  <div className="flex justify-between items-center mb-4">
                                    <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
                                      <Calendar size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
                                      Calendário
                                    </h1>
                                    <div className="flex items-center gap-2">
                                      <button className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white rounded-lg hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700 transition-all">
                                        <Plus size={16} />
                                        Novo Agendamento
                                      </button>
                                    </div>
                                  </div>
                                </div>

                                {/* Área de filtros */}
                                <div className="p-6 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                                  <div className="bg-white dark:bg-gray-800 border border-purple-200 dark:border-purple-800/30 rounded-lg p-4">
                                    <div className="flex items-center justify-between mb-4">
                                      <h3 className="text-lg font-semibold text-purple-700 dark:text-purple-300">Calendário de Agendamentos</h3>
                                      <div className="flex gap-2">
                                        <button className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-800/30 rounded text-xs">Hoje</button>
                                        <button className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-800/30 rounded text-xs">Semana</button>
                                        <button className="px-3 py-1 bg-purple-600 dark:bg-purple-700 text-white rounded text-xs font-medium">Mês</button>
                                      </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                                      <div className="md:col-span-2">
                                        <div className="relative">
                                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={16} />
                                          <input
                                            type="text"
                                            placeholder="Buscar no calendário (título, profissional, paciente, serviço)..."
                                            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400"
                                          />
                                        </div>
                                      </div>
                                      <div>
                                        <button className="w-full px-3 py-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-800/30 rounded-lg text-sm hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors">
                                          <Filter size={16} className="inline mr-2" />
                                          Filtros
                                        </button>
                                      </div>
                                      <div>
                                        <button className="w-full px-3 py-2 bg-purple-600 dark:bg-purple-700 text-white rounded-lg text-sm hover:bg-purple-700 dark:hover:bg-purple-800 transition-colors">
                                          <Download size={16} className="inline mr-2" />
                                          Exportar
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Calendário */}
                                <div className="px-6 pb-6 flex-1 overflow-auto">
                                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
                                    {/* Header do calendário */}
                                    <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                                      <div className="flex items-center justify-between">
                                        <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Março 2024</h4>
                                        <div className="flex gap-2">
                                          <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400">
                                            <ArrowRight size={16} className="rotate-180" />
                                          </button>
                                          <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400">
                                            <ArrowRight size={16} />
                                          </button>
                                        </div>
                                      </div>
                                    </div>

                                    {/* Grid do calendário */}
                                    <div className="p-4">
                                      <div className="grid grid-cols-7 gap-1 text-xs mb-2">
                                        <div className="text-center font-semibold p-2 text-gray-600 dark:text-gray-400">Dom</div>
                                        <div className="text-center font-semibold p-2 text-gray-600 dark:text-gray-400">Seg</div>
                                        <div className="text-center font-semibold p-2 text-gray-600 dark:text-gray-400">Ter</div>
                                        <div className="text-center font-semibold p-2 text-gray-600 dark:text-gray-400">Qua</div>
                                        <div className="text-center font-semibold p-2 text-gray-600 dark:text-gray-400">Qui</div>
                                        <div className="text-center font-semibold p-2 text-gray-600 dark:text-gray-400">Sex</div>
                                        <div className="text-center font-semibold p-2 text-gray-600 dark:text-gray-400">Sáb</div>
                                      </div>
                                      <div className="grid grid-cols-7 gap-1 text-xs">
                                        {Array.from({length: 35}).map((_, i) => (
                                          <div key={i} className="aspect-square flex flex-col items-center justify-start p-2 border border-gray-200 dark:border-gray-600 rounded hover:bg-purple-50 dark:hover:bg-purple-900/20 cursor-pointer transition-colors">
                                            <span className="font-medium text-gray-900 dark:text-gray-100">{i + 1 <= 30 ? i + 1 : ''}</span>
                                            {i === 14 && (
                                              <div className="w-full mt-1 space-y-1">
                                                <div className="bg-blue-500 text-white text-xs px-1 py-0.5 rounded truncate">09:00</div>
                                                <div className="bg-green-500 text-white text-xs px-1 py-0.5 rounded truncate">14:00</div>
                                              </div>
                                            )}
                                            {i === 16 && (
                                              <div className="w-full mt-1">
                                                <div className="bg-orange-500 text-white text-xs px-1 py-0.5 rounded truncate">10:00</div>
                                              </div>
                                            )}
                                            {i === 20 && (
                                              <div className="w-full mt-1">
                                                <div className="bg-purple-500 text-white text-xs px-1 py-0.5 rounded truncate">15:00</div>
                                              </div>
                                            )}
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </div>

                                  {/* Legenda */}
                                  <div className="mt-4 flex flex-wrap gap-4 text-xs">
                                    <div className="flex items-center gap-2">
                                      <div className="w-3 h-3 bg-blue-500 rounded"></div>
                                      <span className="text-gray-700 dark:text-gray-300">Consulta Médica</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <div className="w-3 h-3 bg-green-500 rounded"></div>
                                      <span className="text-gray-700 dark:text-gray-300">Terapia</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <div className="w-3 h-3 bg-orange-500 rounded"></div>
                                      <span className="text-gray-700 dark:text-gray-300">Avaliação</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <div className="w-3 h-3 bg-purple-500 rounded"></div>
                                      <span className="text-gray-700 dark:text-gray-300">Retorno</span>
                                    </div>
                                  </div>
                                </div>

                                {/* Indicador de ação */}
                                <div className="absolute top-20 right-4 bg-yellow-400 text-black px-3 py-2 rounded-lg shadow-lg text-xs font-medium animate-pulse">
                                  👆 Clique em uma data para criar agendamento
                                </div>
                              </div>
                            </div>

                            {/* Slide 2: Modal Real de Criação de Agendamento */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center bg-black/50`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-2xl h-4/5 overflow-hidden rounded-lg shadow-xl relative border border-gray-200 dark:border-gray-700">
                                {/* Header do modal */}
                                <div className="bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                                  <div className="flex items-center justify-between">
                                    <h1 className="text-xl font-bold text-white flex items-center">
                                      <Plus size={24} className="mr-3" />
                                      Novo Agendamento
                                    </h1>
                                    <button className="text-white hover:text-gray-200 transition-colors">
                                      <XCircle size={24} />
                                    </button>
                                  </div>
                                </div>

                                {/* Formulário */}
                                <div className="p-6 space-y-6 overflow-auto bg-white dark:bg-gray-900">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Paciente *
                                      </label>
                                      <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent">
                                        <option>Selecione o paciente...</option>
                                        <option selected>Maria Silva Santos</option>
                                        <option>João Pedro Lima</option>
                                        <option>Ana Costa Oliveira</option>
                                      </select>
                                    </div>
                                    <div>
                                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Profissional *
                                      </label>
                                      <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent">
                                        <option>Selecione o profissional...</option>
                                        <option selected>Dr. João Santos</option>
                                        <option>Dra. Ana Costa</option>
                                        <option>Dr. Pedro Lima</option>
                                      </select>
                                    </div>
                                  </div>

                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Data *
                                      </label>
                                      <input
                                        type="date"
                                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent"
                                        defaultValue="2024-03-15"
                                      />
                                    </div>
                                    <div>
                                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Horário *
                                      </label>
                                      <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent">
                                        <option>Selecione o horário...</option>
                                        <option selected>09:00 - 10:00</option>
                                        <option>10:00 - 11:00</option>
                                        <option>11:00 - 12:00</option>
                                        <option>14:00 - 15:00</option>
                                      </select>
                                    </div>
                                  </div>

                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                      Tipo de Serviço *
                                    </label>
                                    <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent">
                                      <option>Selecione o tipo de serviço...</option>
                                      <option selected>Consulta Médica - R$ 150,00 (60 min)</option>
                                      <option>Terapia - R$ 120,00 (45 min)</option>
                                      <option>Avaliação - R$ 200,00 (90 min)</option>
                                    </select>
                                  </div>

                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                      Local
                                    </label>
                                    <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent">
                                      <option>Selecione o local...</option>
                                      <option selected>Sala 101 - Consultório Principal</option>
                                      <option>Sala 102 - Sala de Terapia</option>
                                      <option>Sala 103 - Sala de Reuniões</option>
                                    </select>
                                  </div>

                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                      Observações
                                    </label>
                                    <textarea
                                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400 focus:border-transparent resize-none"
                                      rows="3"
                                      placeholder="Observações adicionais sobre o agendamento..."
                                    ></textarea>
                                  </div>
                                </div>

                                {/* Footer com botões */}
                                <div className="border-t border-gray-200 dark:border-gray-700 p-6 bg-gray-50 dark:bg-gray-800">
                                  <div className="flex gap-3 justify-end">
                                    <button className="px-6 py-3 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors">
                                      Cancelar
                                    </button>
                                    <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white rounded-lg text-sm font-medium hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700 transition-all shadow-lg">
                                      Criar Agendamento
                                    </button>
                                  </div>
                                </div>

                                {/* Indicador de ação */}
                                <div className="absolute top-20 right-6 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg text-sm font-medium animate-bounce">
                                  ✓ Preencha os campos obrigatórios
                                </div>
                              </div>
                            </div>

                            {/* Slide 3: Painel de Filtros Avançados */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-2xl h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                {/* Header */}
                                <div className="bg-gradient-to-r from-purple-600 to-purple-500 dark:from-purple-700 dark:to-purple-600 px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-white flex items-center">
                                    <Filter size={20} className="mr-2" />
                                    Filtros Avançados
                                  </h1>
                                </div>

                                {/* Barra de pesquisa */}
                                <div className="p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                                  <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={16} />
                                    <input
                                      type="text"
                                      placeholder="Buscar no calendário (título, profissional, paciente, serviço)..."
                                      className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400"
                                      defaultValue="Dr. João"
                                    />
                                  </div>
                                </div>

                                {/* Filtros */}
                                <div className="p-4 space-y-4 overflow-auto bg-white dark:bg-gray-900">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2 mb-2">
                                        <Users size={16} className="text-purple-600 dark:text-purple-400" />
                                        Profissionais
                                      </label>
                                      <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-3 bg-gray-50 dark:bg-gray-800 max-h-32 overflow-auto">
                                        <div className="space-y-2">
                                          <label className="flex items-center gap-2 text-sm">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" defaultChecked readOnly />
                                            <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                                              <Users size={12} className="text-blue-600 dark:text-blue-400" />
                                            </div>
                                            <span className="text-gray-900 dark:text-gray-100">Dr. João Santos</span>
                                          </label>
                                          <label className="flex items-center gap-2 text-sm">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" defaultChecked readOnly />
                                            <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                                              <Users size={12} className="text-green-600 dark:text-green-400" />
                                            </div>
                                            <span className="text-gray-900 dark:text-gray-100">Dra. Ana Costa</span>
                                          </label>
                                          <label className="flex items-center gap-2 text-sm">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" readOnly />
                                            <div className="w-6 h-6 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                                              <Users size={12} className="text-gray-600 dark:text-gray-400" />
                                            </div>
                                            <span className="text-gray-900 dark:text-gray-100">Dr. Pedro Lima</span>
                                          </label>
                                        </div>
                                      </div>
                                    </div>

                                    <div>
                                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2 mb-2">
                                        <Tag size={16} className="text-purple-600 dark:text-purple-400" />
                                        Tipos de Serviço
                                      </label>
                                      <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-3 bg-gray-50 dark:bg-gray-800 max-h-32 overflow-auto">
                                        <div className="space-y-2">
                                          <label className="flex items-center gap-2 text-sm">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" defaultChecked readOnly />
                                            <div className="w-3 h-3 bg-blue-400 rounded"></div>
                                            <span className="text-gray-900 dark:text-gray-100">Consulta Médica</span>
                                          </label>
                                          <label className="flex items-center gap-2 text-sm">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" defaultChecked readOnly />
                                            <div className="w-3 h-3 bg-green-400 rounded"></div>
                                            <span className="text-gray-900 dark:text-gray-100">Terapia</span>
                                          </label>
                                          <label className="flex items-center gap-2 text-sm">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" readOnly />
                                            <div className="w-3 h-3 bg-orange-400 rounded"></div>
                                            <span className="text-gray-900 dark:text-gray-100">Avaliação</span>
                                          </label>
                                        </div>
                                      </div>
                                    </div>
                                  </div>

                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2 mb-2">
                                        <MapPin size={16} className="text-purple-600 dark:text-purple-400" />
                                        Locais
                                      </label>
                                      <select className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400">
                                        <option>Todos os locais</option>
                                        <option selected>Sala 101 - Consultório</option>
                                        <option>Sala 102 - Terapia</option>
                                      </select>
                                    </div>

                                    <div>
                                      <label className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2 mb-2">
                                        <Activity size={16} className="text-purple-600 dark:text-purple-400" />
                                        Status
                                      </label>
                                      <select className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400">
                                        <option>Todos os status</option>
                                        <option selected>Agendado</option>
                                        <option>Confirmado</option>
                                        <option>Realizado</option>
                                        <option>Cancelado</option>
                                      </select>
                                    </div>
                                  </div>
                                </div>

                                {/* Footer com botões */}
                                <div className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800">
                                  <div className="flex gap-2">
                                    <button className="flex-1 px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-500 dark:from-purple-700 dark:to-purple-600 text-white rounded-lg text-sm font-medium hover:from-purple-700 hover:to-purple-600 dark:hover:from-purple-800 dark:hover:to-purple-700 transition-all">
                                      Aplicar Filtros
                                    </button>
                                    <button className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm hover:bg-gray-400 dark:hover:bg-gray-500 transition-colors">
                                      Limpar
                                    </button>
                                  </div>
                                </div>

                                {/* Indicador de ação */}
                                <div className="absolute top-20 right-4 bg-blue-400 text-white px-3 py-2 rounded-lg shadow-lg text-xs font-medium animate-pulse">
                                  🔍 Use filtros para encontrar agendamentos específicos
                                </div>
                              </div>
                            </div>

                            {/* Slide 4: Visualizações do Calendário */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white flex items-center">
                                    <Calendar size={20} className="mr-2 text-purple-600 dark:text-purple-400" />
                                    Visualizações
                                  </h1>
                                </div>
                                <div className="p-4 bg-white dark:bg-gray-900">
                                  <div className="flex gap-2 mb-4">
                                    <button className="px-3 py-2 bg-purple-600 dark:bg-purple-700 text-white rounded text-sm font-medium">Dia</button>
                                    <button className="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">Semana</button>
                                    <button className="px-3 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors">Mês</button>
                                  </div>
                                  <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800/30 rounded-lg p-4">
                                    <div className="text-center text-sm text-purple-800 dark:text-purple-300 font-medium mb-3">Segunda, 15 de Março</div>
                                    <div className="space-y-2">
                                      <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg text-sm border-l-4 border-blue-400">
                                        <div className="font-medium text-gray-900 dark:text-gray-100">09:00 - Consulta</div>
                                        <div className="text-gray-600 dark:text-gray-400">Maria Silva - Dr. João</div>
                                      </div>
                                      <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg text-sm border-l-4 border-green-400">
                                        <div className="font-medium text-gray-900 dark:text-gray-100">10:00 - Terapia</div>
                                        <div className="text-gray-600 dark:text-gray-400">Pedro Santos - Dra. Ana</div>
                                      </div>
                                      <div className="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-lg text-sm border-l-4 border-orange-400">
                                        <div className="font-medium text-gray-900 dark:text-gray-100">14:00 - Avaliação</div>
                                        <div className="text-gray-600 dark:text-gray-400">Ana Costa - Dr. João</div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 5: Detalhes do Agendamento */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Detalhes do Agendamento</h1>
                                </div>
                                <div className="p-4 space-y-4 bg-white dark:bg-gray-900">
                                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border-l-4 border-blue-400">
                                    <div className="text-sm font-medium text-blue-800 dark:text-blue-300">Consulta Médica</div>
                                    <div className="text-sm text-blue-600 dark:text-blue-400 mt-1">15/03/2024 - 09:00 às 10:00</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Paciente</div>
                                    <div className="text-base text-gray-900 dark:text-gray-100">Maria Silva</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Profissional</div>
                                    <div className="text-base text-gray-900 dark:text-gray-100">Dr. João Santos</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Local</div>
                                    <div className="text-base text-gray-900 dark:text-gray-100">Sala 101 - Consultório Principal</div>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300">Status</div>
                                    <div className="flex items-center gap-2">
                                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                                      <span className="text-base text-green-600 dark:text-green-400">Confirmado</span>
                                    </div>
                                  </div>
                                  <div className="flex gap-2 pt-2">
                                    <button className="flex-1 px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded text-sm hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors">
                                      Editar
                                    </button>
                                    <button className="flex-1 px-4 py-2 bg-red-600 dark:bg-red-700 text-white rounded text-sm hover:bg-red-700 dark:hover:bg-red-800 transition-colors">
                                      Cancelar
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 6: Atualização Automática */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white flex items-center">
                                    <RefreshCw size={20} className="mr-2 text-purple-600 dark:text-purple-400 animate-spin" />
                                    Atualizações em Tempo Real
                                  </h1>
                                </div>
                                <div className="p-4 bg-white dark:bg-gray-900">
                                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800/30 mb-4">
                                    <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
                                      <CheckCircle size={16} />
                                      <span className="text-sm font-medium">Novo agendamento criado!</span>
                                    </div>
                                    <div className="text-sm text-green-600 dark:text-green-400 mt-1">
                                      Pedro Lima - 16/03 às 11:00
                                    </div>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-sm border border-gray-200 dark:border-gray-600">
                                      <div>
                                        <div className="font-medium text-gray-900 dark:text-gray-100">Maria Silva</div>
                                        <div className="text-gray-500 dark:text-gray-400">15/03 09:00</div>
                                      </div>
                                      <div className="text-green-600 dark:text-green-400">Confirmado</div>
                                    </div>
                                    <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg text-sm border border-green-200 dark:border-green-800/30">
                                      <div>
                                        <div className="font-medium text-gray-900 dark:text-gray-100">Pedro Lima</div>
                                        <div className="text-gray-500 dark:text-gray-400">16/03 11:00</div>
                                      </div>
                                      <div className="text-blue-600 dark:text-blue-400">Novo</div>
                                    </div>
                                    <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-sm border border-gray-200 dark:border-gray-600">
                                      <div>
                                        <div className="font-medium text-gray-900 dark:text-gray-100">Ana Costa</div>
                                        <div className="text-gray-500 dark:text-gray-400">15/03 14:00</div>
                                      </div>
                                      <div className="text-orange-600 dark:text-orange-400">Agendado</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 7: Exportação de Dados */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <div className="flex justify-between items-center">
                                    <h1 className="text-lg font-bold text-slate-800 dark:text-white">Exportar Agendamentos</h1>
                                    <button className="flex items-center gap-2 px-3 py-2 bg-purple-600 dark:bg-purple-700 text-white rounded text-sm hover:bg-purple-700 dark:hover:bg-purple-800 transition-colors">
                                      <Download size={16} />
                                      Exportar
                                    </button>
                                  </div>
                                </div>
                                <div className="p-4 space-y-4 bg-white dark:bg-gray-900">
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Período</label>
                                    <select className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400">
                                      <option>Últimos 30 dias</option>
                                      <option>Último mês</option>
                                      <option>Personalizado</option>
                                    </select>
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Formato</label>
                                    <div className="grid grid-cols-2 gap-2">
                                      <button className="p-3 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-800 flex items-center gap-2 text-gray-900 dark:text-gray-100 transition-colors">
                                        <FileText size={16} />
                                        PDF
                                      </button>
                                      <button className="p-3 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-800 flex items-center gap-2 text-gray-900 dark:text-gray-100 transition-colors">
                                        <FileText size={16} />
                                        Excel
                                      </button>
                                      <button className="p-3 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-800 flex items-center gap-2 text-gray-900 dark:text-gray-100 transition-colors">
                                        <FileText size={16} />
                                        CSV
                                      </button>
                                      <button className="p-3 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-800 flex items-center gap-2 text-gray-900 dark:text-gray-100 transition-colors">
                                        <FileText size={16} />
                                        JSON
                                      </button>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Incluir</label>
                                    <div className="space-y-2">
                                      <label className="flex items-center gap-2 text-sm">
                                        <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" defaultChecked readOnly />
                                        <span className="text-gray-900 dark:text-gray-100">Dados do paciente</span>
                                      </label>
                                      <label className="flex items-center gap-2 text-sm">
                                        <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" defaultChecked readOnly />
                                        <span className="text-gray-900 dark:text-gray-100">Informações do profissional</span>
                                      </label>
                                      <label className="flex items-center gap-2 text-sm">
                                        <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" readOnly />
                                        <span className="text-gray-900 dark:text-gray-100">Valores financeiros</span>
                                      </label>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <CheckCircle size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Tutorial Concluído!</h3>
                                <p className="text-purple-200">Agora você está pronto para usar o calendário de agendamentos</p>
                              </div>
                            </div>
                          </>
                        )}

                        {/* Tutorial de Horários de Trabalho */}
                        {selectedTutorial?.id === 'working-hours' && (
                          <>
                            {/* Slide 1: Interface Real de Horários de Trabalho */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-4xl h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                {/* Header da página */}
                                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                                  <div className="flex justify-between items-center mb-4">
                                    <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
                                      <Clock size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
                                      Horários de Trabalho
                                    </h1>
                                  </div>
                                </div>

                                {/* Área de filtros */}
                                <div className="p-6 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                                  <div className="bg-white dark:bg-gray-800 border border-purple-200 dark:border-purple-800/30 rounded-lg p-4">
                                    <div className="flex items-center justify-between mb-4">
                                      <h3 className="text-lg font-semibold text-purple-700 dark:text-purple-300">Filtros</h3>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                                      <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                          Profissionais
                                        </label>
                                        <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400">
                                          <option>Selecione...</option>
                                          <option selected>Dr. João Santos</option>
                                          <option>Dra. Ana Costa</option>
                                          <option>Dr. Pedro Lima</option>
                                        </select>
                                      </div>
                                      <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                          Dia da Semana
                                        </label>
                                        <select className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400">
                                          <option selected>Segunda-feira</option>
                                          <option>Terça-feira</option>
                                          <option>Quarta-feira</option>
                                          <option>Quinta-feira</option>
                                          <option>Sexta-feira</option>
                                          <option>Sábado</option>
                                        </select>
                                      </div>
                                      <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                          &nbsp;
                                        </label>
                                        <button className="w-full px-4 py-3 bg-purple-600 dark:bg-purple-700 text-white rounded-lg text-sm font-medium hover:bg-purple-700 dark:hover:bg-purple-800 transition-colors">
                                          Carregar Horários
                                        </button>
                                      </div>
                                      <div>
                                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                          &nbsp;
                                        </label>
                                        <button className="w-full px-4 py-3 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-800/30 rounded-lg text-sm hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors">
                                          <Download size={16} className="inline mr-2" />
                                          Exportar
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Grade de horários */}
                                <div className="px-6 pb-6 flex-1 overflow-auto">
                                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
                                    <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
                                      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Dr. João Santos - Segunda-feira</h3>
                                    </div>

                                    <div className="p-4">
                                      <div className="overflow-x-auto">
                                        <table className="w-full text-sm border-collapse">
                                          <thead>
                                            <tr className="bg-gray-50 dark:bg-gray-700">
                                              <th className="text-left px-4 py-3 border border-gray-200 dark:border-gray-600 font-medium text-gray-700 dark:text-gray-300">Horário</th>
                                              <th className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600 font-medium text-gray-700 dark:text-gray-300">Status</th>
                                              <th className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600 font-medium text-gray-700 dark:text-gray-300">Ação</th>
                                            </tr>
                                          </thead>
                                          <tbody>
                                            <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                              <td className="px-4 py-3 border border-gray-200 dark:border-gray-600 text-gray-900 dark:text-gray-100">08:00</td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <div className="w-6 h-6 bg-green-500 rounded mx-auto cursor-pointer hover:bg-green-600 transition-colors" title="Disponível - Clique para alterar"></div>
                                              </td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <span className="text-green-600 dark:text-green-400 text-sm">Disponível</span>
                                              </td>
                                            </tr>
                                            <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                              <td className="px-4 py-3 border border-gray-200 dark:border-gray-600 text-gray-900 dark:text-gray-100">09:00</td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <div className="w-6 h-6 bg-green-500 rounded mx-auto cursor-pointer hover:bg-green-600 transition-colors" title="Disponível - Clique para alterar"></div>
                                              </td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <span className="text-green-600 dark:text-green-400 text-sm">Disponível</span>
                                              </td>
                                            </tr>
                                            <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                              <td className="px-4 py-3 border border-gray-200 dark:border-gray-600 text-gray-900 dark:text-gray-100">10:00</td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <div className="w-6 h-6 bg-green-500 rounded mx-auto cursor-pointer hover:bg-green-600 transition-colors" title="Disponível - Clique para alterar"></div>
                                              </td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <span className="text-green-600 dark:text-green-400 text-sm">Disponível</span>
                                              </td>
                                            </tr>
                                            <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                              <td className="px-4 py-3 border border-gray-200 dark:border-gray-600 text-gray-900 dark:text-gray-100">11:00</td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <div className="w-6 h-6 bg-green-500 rounded mx-auto cursor-pointer hover:bg-green-600 transition-colors" title="Disponível - Clique para alterar"></div>
                                              </td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <span className="text-green-600 dark:text-green-400 text-sm">Disponível</span>
                                              </td>
                                            </tr>
                                            <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                              <td className="px-4 py-3 border border-gray-200 dark:border-gray-600 text-gray-900 dark:text-gray-100">12:00</td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <div className="w-6 h-6 bg-gray-400 dark:bg-gray-500 rounded mx-auto cursor-pointer hover:bg-gray-500 dark:hover:bg-gray-400 transition-colors" title="Indisponível - Clique para alterar"></div>
                                              </td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <span className="text-gray-600 dark:text-gray-400 text-sm">Almoço</span>
                                              </td>
                                            </tr>
                                            <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                              <td className="px-4 py-3 border border-gray-200 dark:border-gray-600 text-gray-900 dark:text-gray-100">14:00</td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <div className="w-6 h-6 bg-green-500 rounded mx-auto cursor-pointer hover:bg-green-600 transition-colors" title="Disponível - Clique para alterar"></div>
                                              </td>
                                              <td className="text-center px-4 py-3 border border-gray-200 dark:border-gray-600">
                                                <span className="text-green-600 dark:text-green-400 text-sm">Disponível</span>
                                              </td>
                                            </tr>
                                          </tbody>
                                        </table>
                                      </div>
                                    </div>
                                  </div>

                                  {/* Legenda */}
                                  <div className="mt-6 flex flex-wrap gap-6 text-sm">
                                    <div className="flex items-center gap-2">
                                      <div className="w-4 h-4 bg-green-500 rounded"></div>
                                      <span className="text-gray-700 dark:text-gray-300">Disponível</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <div className="w-4 h-4 bg-gray-400 dark:bg-gray-500 rounded"></div>
                                      <span className="text-gray-700 dark:text-gray-300">Indisponível</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                      <div className="w-4 h-4 bg-red-500 rounded"></div>
                                      <span className="text-gray-700 dark:text-gray-300">Conflito</span>
                                    </div>
                                  </div>

                                  {/* Botão salvar */}
                                  <div className="mt-6">
                                    <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white rounded-lg text-sm font-medium hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700 transition-all shadow-lg">
                                      Salvar Alterações
                                    </button>
                                  </div>
                                </div>

                                {/* Indicador de ação */}
                                <div className="absolute top-32 right-4 bg-yellow-400 text-black px-3 py-2 rounded-lg shadow-lg text-xs font-medium animate-pulse">
                                  👆 Clique nos quadrados para alterar disponibilidade
                                </div>
                              </div>
                            </div>

                            {/* Slide 2: Configurando Horários */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Configuração por Arraste</h1>
                                </div>
                                <div className="p-4 bg-white dark:bg-gray-900">
                                  <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
                                    Clique e arraste para selecionar múltiplos horários
                                  </div>
                                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3 border border-purple-200 dark:border-purple-800/30">
                                    <table className="w-full text-sm">
                                      <thead>
                                        <tr>
                                          <th className="text-left p-2 text-gray-700 dark:text-gray-300">Horário</th>
                                          <th className="text-center p-2 text-gray-700 dark:text-gray-300">Seg</th>
                                          <th className="text-center p-2 text-gray-700 dark:text-gray-300">Ter</th>
                                          <th className="text-center p-2 text-gray-700 dark:text-gray-300">Qua</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        <tr className="bg-purple-100 dark:bg-purple-900/30">
                                          <td className="p-2 text-gray-900 dark:text-gray-100">09:00</td>
                                          <td className="text-center p-2"><div className="w-5 h-5 bg-purple-500 rounded mx-auto border-2 border-purple-700 dark:border-purple-300"></div></td>
                                          <td className="text-center p-2"><div className="w-5 h-5 bg-purple-500 rounded mx-auto border-2 border-purple-700 dark:border-purple-300"></div></td>
                                          <td className="text-center p-2"><div className="w-5 h-5 bg-purple-500 rounded mx-auto border-2 border-purple-700 dark:border-purple-300"></div></td>
                                        </tr>
                                        <tr className="bg-purple-100 dark:bg-purple-900/30">
                                          <td className="p-2 text-gray-900 dark:text-gray-100">10:00</td>
                                          <td className="text-center p-2"><div className="w-5 h-5 bg-purple-500 rounded mx-auto border-2 border-purple-700 dark:border-purple-300"></div></td>
                                          <td className="text-center p-2"><div className="w-5 h-5 bg-purple-500 rounded mx-auto border-2 border-purple-700 dark:border-purple-300"></div></td>
                                          <td className="text-center p-2"><div className="w-5 h-5 bg-purple-500 rounded mx-auto border-2 border-purple-700 dark:border-purple-300"></div></td>
                                        </tr>
                                        <tr>
                                          <td className="p-2 text-gray-900 dark:text-gray-100">11:00</td>
                                          <td className="text-center p-2"><div className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded mx-auto"></div></td>
                                          <td className="text-center p-2"><div className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded mx-auto"></div></td>
                                          <td className="text-center p-2"><div className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded mx-auto"></div></td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                  <div className="mt-4 text-sm text-purple-700 dark:text-purple-300 bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-200 dark:border-purple-800/30">
                                    <div className="font-medium">Seleção Ativa:</div>
                                    <div>09:00 - 10:00 (Segunda a Quarta)</div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 3: Copiando Horários */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Copiar Horários</h1>
                                </div>
                                <div className="p-4 bg-white dark:bg-gray-900">
                                  <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Copiar de:</div>
                                    <select className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400">
                                      <option>Segunda-feira</option>
                                    </select>
                                  </div>
                                  <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Para:</div>
                                    <div className="space-y-2">
                                      <label className="flex items-center gap-2 text-sm">
                                        <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" defaultChecked readOnly />
                                        <span className="text-gray-900 dark:text-gray-100">Terça-feira</span>
                                      </label>
                                      <label className="flex items-center gap-2 text-sm">
                                        <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" defaultChecked readOnly />
                                        <span className="text-gray-900 dark:text-gray-100">Quarta-feira</span>
                                      </label>
                                      <label className="flex items-center gap-2 text-sm">
                                        <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" defaultChecked readOnly />
                                        <span className="text-gray-900 dark:text-gray-100">Quinta-feira</span>
                                      </label>
                                      <label className="flex items-center gap-2 text-sm">
                                        <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" defaultChecked readOnly />
                                        <span className="text-gray-900 dark:text-gray-100">Sexta-feira</span>
                                      </label>
                                    </div>
                                  </div>
                                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-800/30">
                                    <div className="text-sm text-green-700 dark:text-green-300">
                                      <div className="font-medium">Horários a serem copiados:</div>
                                      <div>09:00 - 12:00</div>
                                      <div>14:00 - 18:00</div>
                                    </div>
                                  </div>
                                  <button className="w-full mt-4 px-4 py-2 bg-purple-600 dark:bg-purple-700 text-white rounded text-sm hover:bg-purple-700 dark:hover:bg-purple-800 transition-colors">
                                    Copiar Horários
                                  </button>
                                </div>
                              </div>
                            </div>

                            {/* Slide 4: Múltiplos Profissionais */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Múltiplos Profissionais</h1>
                                </div>
                                <div className="p-4 bg-white dark:bg-gray-900">
                                  <div className="mb-3">
                                    <div className="text-xs font-medium text-gray-700 mb-2">Selecionar Profissionais:</div>
                                    <div className="space-y-1">
                                      <label className="flex items-center gap-2 text-xs p-2 bg-blue-50 rounded">
                                        <input type="checkbox" className="rounded" defaultChecked readOnly />
                                        <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center">
                                          <Users size={12} className="text-blue-600" />
                                        </div>
                                        Dr. João Santos
                                      </label>
                                      <label className="flex items-center gap-2 text-xs p-2 bg-green-50 rounded">
                                        <input type="checkbox" className="rounded" defaultChecked readOnly />
                                        <div className="w-6 h-6 bg-green-200 rounded-full flex items-center justify-center">
                                          <Users size={12} className="text-green-600" />
                                        </div>
                                        Dra. Ana Costa
                                      </label>
                                      <label className="flex items-center gap-2 text-xs p-2 bg-gray-50 rounded">
                                        <input type="checkbox" className="rounded" readOnly />
                                        <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center">
                                          <Users size={12} className="text-gray-600" />
                                        </div>
                                        Dr. Pedro Lima
                                      </label>
                                    </div>
                                  </div>
                                  <div className="bg-yellow-50 p-2 rounded border border-yellow-200">
                                    <div className="text-xs text-yellow-700">
                                      <div className="font-medium">Configuração em lote:</div>
                                      <div>2 profissionais selecionados</div>
                                      <div>Alterações serão aplicadas simultaneamente</div>
                                    </div>
                                  </div>
                                  <button className="w-full mt-3 px-3 py-2 bg-blue-500 text-white rounded text-xs">
                                    Configurar Horários
                                  </button>
                                </div>
                              </div>
                            </div>

                            {/* Slide 5: Exceções */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Exceções e Feriados</h1>
                                </div>
                                <div className="p-4 space-y-4 bg-white dark:bg-gray-900">
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Data da Exceção</label>
                                    <input type="date" className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400" />
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tipo</label>
                                    <select className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400">
                                      <option>Feriado Nacional</option>
                                      <option>Férias</option>
                                      <option>Ausência</option>
                                      <option>Horário Especial</option>
                                    </select>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800/30">
                                      <div>
                                        <div className="text-sm font-medium text-red-800 dark:text-red-300">25/12/2024</div>
                                        <div className="text-sm text-red-600 dark:text-red-400">Natal - Feriado</div>
                                      </div>
                                      <XCircle size={16} className="text-red-500 dark:text-red-400" />
                                    </div>
                                    <div className="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800/30">
                                      <div>
                                        <div className="text-sm font-medium text-orange-800 dark:text-orange-300">01/01/2025</div>
                                        <div className="text-sm text-orange-600 dark:text-orange-400">Ano Novo - Feriado</div>
                                      </div>
                                      <XCircle size={16} className="text-orange-500 dark:text-orange-400" />
                                    </div>
                                  </div>
                                  <button className="w-full px-4 py-2 bg-red-600 dark:bg-red-700 text-white rounded text-sm hover:bg-red-700 dark:hover:bg-red-800 transition-colors">
                                    Adicionar Exceção
                                  </button>
                                </div>
                              </div>
                            </div>

                            {/* Slide 6: Visualização */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Visualização Clara</h1>
                                </div>
                                <div className="p-4 bg-white dark:bg-gray-900">
                                  <div className="mb-3">
                                    <div className="text-xs font-medium text-gray-700 mb-2">Legenda de Cores:</div>
                                    <div className="space-y-1">
                                      <div className="flex items-center gap-2 text-xs">
                                        <div className="w-4 h-4 bg-green-400 rounded"></div>
                                        <span>Disponível</span>
                                      </div>
                                      <div className="flex items-center gap-2 text-xs">
                                        <div className="w-4 h-4 bg-blue-400 rounded"></div>
                                        <span>Ocupado</span>
                                      </div>
                                      <div className="flex items-center gap-2 text-xs">
                                        <div className="w-4 h-4 bg-gray-300 rounded"></div>
                                        <span>Indisponível</span>
                                      </div>
                                      <div className="flex items-center gap-2 text-xs">
                                        <div className="w-4 h-4 bg-red-400 rounded"></div>
                                        <span>Exceção/Feriado</span>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="bg-gray-50 rounded p-2">
                                    <table className="w-full text-xs">
                                      <thead>
                                        <tr>
                                          <th className="text-left p-1">Horário</th>
                                          <th className="text-center p-1">Seg</th>
                                          <th className="text-center p-1">Ter</th>
                                          <th className="text-center p-1">Qua</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        <tr>
                                          <td className="p-1">09:00</td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-blue-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-red-400 rounded mx-auto"></div></td>
                                        </tr>
                                        <tr>
                                          <td className="p-1">10:00</td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-green-400 rounded mx-auto"></div></td>
                                          <td className="text-center p-1"><div className="w-4 h-4 bg-gray-300 rounded mx-auto"></div></td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 7: Integração */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Integração com Calendário</h1>
                                </div>
                                <div className="p-4 bg-white dark:bg-gray-900">
                                  <div className="bg-green-50 p-3 rounded border border-green-200 mb-3">
                                    <div className="flex items-center gap-2 text-green-700">
                                      <CheckCircle size={16} />
                                      <span className="text-sm font-medium">Horários sincronizados!</span>
                                    </div>
                                    <div className="text-xs text-green-600 mt-1">
                                      Configurações aplicadas ao calendário automaticamente
                                    </div>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="text-xs font-medium text-gray-700">Horários Disponíveis:</div>
                                    <div className="bg-blue-50 p-2 rounded">
                                      <div className="text-xs">
                                        <div className="font-medium text-blue-800">Dr. João Santos</div>
                                        <div className="text-blue-600">Seg-Sex: 09:00-12:00, 14:00-18:00</div>
                                      </div>
                                    </div>
                                    <div className="bg-green-50 p-2 rounded">
                                      <div className="text-xs">
                                        <div className="font-medium text-green-800">Dra. Ana Costa</div>
                                        <div className="text-green-600">Seg-Qui: 08:00-12:00, 13:00-17:00</div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="mt-3 text-xs text-gray-600 bg-gray-50 p-2 rounded">
                                    <div className="font-medium">Próximos passos:</div>
                                    <div>• Acesse o calendário para criar agendamentos</div>
                                    <div>• Os horários configurados limitarão as opções</div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <CheckCircle size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Tutorial Concluído!</h3>
                                <p className="text-purple-200">Agora você pode configurar horários de trabalho eficientemente</p>
                              </div>
                            </div>
                          </>
                        )}

                        {/* Tutorial de Locais */}
                        {selectedTutorial?.id === 'locations' && (
                          <>
                            {/* Slide 1: Interface Real de Locais */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-4xl h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                {/* Header da página */}
                                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                                  <div className="flex justify-between items-center mb-4">
                                    <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
                                      <MapPin size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
                                      Locais
                                    </h1>
                                    <div className="flex items-center gap-2">
                                      <button className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white rounded-lg hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700 transition-all">
                                        <Plus size={16} />
                                        Novo Local
                                      </button>
                                    </div>
                                  </div>
                                </div>

                                {/* Área de filtros */}
                                <div className="p-6 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                                  <div className="bg-white dark:bg-gray-800 border border-purple-200 dark:border-purple-800/30 rounded-lg p-4">
                                    <div className="flex items-center justify-between mb-4">
                                      <h3 className="text-lg font-semibold text-purple-700 dark:text-purple-300">Lista de Localizações</h3>
                                      <button className="p-2 text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors" title="Atualizar lista">
                                        <RefreshCw size={18} />
                                      </button>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                                      <div className="md:col-span-2">
                                        <div className="relative">
                                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={16} />
                                          <input
                                            type="text"
                                            placeholder="Buscar no calendário (título, profissional, paciente, serviço)..."
                                            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400"
                                          />
                                        </div>
                                      </div>
                                      <div>
                                        <button className="w-full px-3 py-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-800/30 rounded-lg text-sm hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors">
                                          <Filter size={16} className="inline mr-2" />
                                          Filtros
                                        </button>
                                      </div>
                                      <div>
                                        <button className="w-full px-3 py-2 bg-purple-600 dark:bg-purple-700 text-white rounded-lg text-sm hover:bg-purple-700 dark:hover:bg-purple-800 transition-colors">
                                          <Download size={16} className="inline mr-2" />
                                          Exportar
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Tabela */}
                                <div className="px-6 pb-6 flex-1 overflow-auto">
                                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
                                    <table className="w-full">
                                      <thead className="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                          <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" />
                                          </th>
                                          <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Nome</th>
                                          <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Endereço</th>
                                          <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Unidade</th>
                                          <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                          <th className="text-right px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Ações</th>
                                        </tr>
                                      </thead>
                                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                          <td className="px-6 py-4 text-center">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" />
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                              <div className="flex-shrink-0 h-10 w-10 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full flex items-center justify-center">
                                                <MapPin size={20} />
                                              </div>
                                              <div className="ml-4">
                                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                  Sala 101
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900 dark:text-gray-100">Rua das Flores, 123</div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900 dark:text-gray-100">Unidade Central</div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                                              Ativo
                                            </span>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end gap-2">
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Compartilhar">
                                                <Share2 size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Editar">
                                                <Edit size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors" title="Excluir">
                                                <Trash size={16} />
                                              </button>
                                            </div>
                                          </td>
                                        </tr>
                                        <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                          <td className="px-6 py-4 text-center">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" />
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                              <div className="flex-shrink-0 h-10 w-10 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full flex items-center justify-center">
                                                <MapPin size={20} />
                                              </div>
                                              <div className="ml-4">
                                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                  Sala 102
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900 dark:text-gray-100">Rua das Palmeiras, 456</div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900 dark:text-gray-100">Unidade Norte</div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                                              Ativo
                                            </span>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end gap-2">
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Compartilhar">
                                                <Share2 size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Editar">
                                                <Edit size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors" title="Excluir">
                                                <Trash size={16} />
                                              </button>
                                            </div>
                                          </td>
                                        </tr>
                                        <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                          <td className="px-6 py-4 text-center">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" />
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                              <div className="flex-shrink-0 h-10 w-10 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full flex items-center justify-center">
                                                <MapPin size={20} />
                                              </div>
                                              <div className="ml-4">
                                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                  Sala 103
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900 dark:text-gray-100">Av. Central, 789</div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900 dark:text-gray-100">Unidade Central</div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400">
                                              Inativo
                                            </span>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end gap-2">
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Compartilhar">
                                                <Share2 size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Editar">
                                                <Edit size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors" title="Excluir">
                                                <Trash size={16} />
                                              </button>
                                            </div>
                                          </td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>

                                  {/* Paginação */}
                                  <div className="mt-4 flex items-center justify-between text-sm text-gray-700 dark:text-gray-300">
                                    <div className="flex items-center gap-2">
                                      <span>Mostrando</span>
                                      <select className="border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-xs bg-white dark:bg-gray-700">
                                        <option>10</option>
                                        <option>25</option>
                                        <option>50</option>
                                      </select>
                                      <span>de 3 localizações</span>
                                    </div>
                                    <div className="flex gap-1">
                                      <button className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-gray-400 dark:text-gray-500" disabled>Anterior</button>
                                      <button className="px-3 py-1 bg-purple-600 text-white rounded">1</button>
                                      <button className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-gray-400 dark:text-gray-500" disabled>Próximo</button>
                                    </div>
                                  </div>
                                </div>

                                {/* Indicador de ação */}
                                <div className="absolute top-16 right-4 bg-blue-400 text-white px-3 py-2 rounded-lg shadow-lg text-xs font-medium animate-bounce">
                                  ➕ Clique em "Novo Local" para adicionar
                                </div>
                              </div>
                            </div>

                            {/* Slide 2: Cadastrando Local */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Novo Local</h1>
                                </div>
                                <div className="p-4 space-y-4 bg-white dark:bg-gray-900">
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Nome do Local</label>
                                    <input type="text" placeholder="Ex: Sala 104" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Descrição</label>
                                    <input type="text" placeholder="Ex: Consultório Médico" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Capacidade</label>
                                    <input type="number" placeholder="1" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Unidade</label>
                                    <select className="w-full p-2 border rounded text-xs">
                                      <option>Unidade Central</option>
                                    </select>
                                  </div>
                                  <div>
                                    <label className="flex items-center gap-2 text-xs">
                                      <input type="checkbox" className="rounded" defaultChecked readOnly />
                                      Local ativo
                                    </label>
                                  </div>
                                  <div className="flex gap-2 pt-2">
                                    <button className="flex-1 px-3 py-2 bg-green-500 text-white rounded text-xs">
                                      Salvar
                                    </button>
                                    <button className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-xs">
                                      Cancelar
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 3: Organização por Unidades */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Organização por Unidades</h1>
                                </div>
                                <div className="p-3">
                                  <div className="space-y-3">
                                    <div className="bg-blue-50 p-3 rounded border border-blue-200">
                                      <div className="flex items-center gap-2 mb-2">
                                        <Building size={16} className="text-blue-600" />
                                        <span className="text-sm font-medium text-blue-800">Unidade Central</span>
                                      </div>
                                      <div className="space-y-1 ml-6">
                                        <div className="text-xs text-blue-600">• Sala 101 - Consultório Principal</div>
                                        <div className="text-xs text-blue-600">• Sala 102 - Sala de Terapia</div>
                                        <div className="text-xs text-blue-600">• Sala 103 - Consultório Médico</div>
                                      </div>
                                    </div>
                                    <div className="bg-green-50 p-3 rounded border border-green-200">
                                      <div className="flex items-center gap-2 mb-2">
                                        <Building size={16} className="text-green-600" />
                                        <span className="text-sm font-medium text-green-800">Unidade Norte</span>
                                      </div>
                                      <div className="space-y-1 ml-6">
                                        <div className="text-xs text-green-600">• Sala 201 - Sala de Reuniões</div>
                                        <div className="text-xs text-green-600">• Sala 202 - Consultório</div>
                                      </div>
                                    </div>
                                    <div className="bg-purple-50 p-3 rounded border border-purple-200">
                                      <div className="flex items-center gap-2 mb-2">
                                        <Building size={16} className="text-purple-600" />
                                        <span className="text-sm font-medium text-purple-800">Unidade Sul</span>
                                      </div>
                                      <div className="space-y-1 ml-6">
                                        <div className="text-xs text-purple-600">• Sala 301 - Auditório</div>
                                        <div className="text-xs text-purple-600">• Sala 302 - Laboratório</div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 4: Configuração de Capacidade */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Configuração de Capacidade</h1>
                                </div>
                                <div className="p-3 space-y-3">
                                  <div className="bg-blue-50 p-3 rounded border border-blue-200">
                                    <div className="flex items-center justify-between mb-2">
                                      <span className="text-sm font-medium text-blue-800">Sala 101</span>
                                      <span className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded">Capacidade: 1</span>
                                    </div>
                                    <div className="text-xs text-blue-600">Consultório individual - 1 paciente por vez</div>
                                  </div>
                                  <div className="bg-green-50 p-3 rounded border border-green-200">
                                    <div className="flex items-center justify-between mb-2">
                                      <span className="text-sm font-medium text-green-800">Sala 201</span>
                                      <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">Capacidade: 8</span>
                                    </div>
                                    <div className="text-xs text-green-600">Sala de reuniões - até 8 pessoas</div>
                                  </div>
                                  <div className="bg-purple-50 p-3 rounded border border-purple-200">
                                    <div className="flex items-center justify-between mb-2">
                                      <span className="text-sm font-medium text-purple-800">Auditório</span>
                                      <span className="text-xs bg-purple-200 text-purple-800 px-2 py-1 rounded">Capacidade: 50</span>
                                    </div>
                                    <div className="text-xs text-purple-600">Eventos e palestras - até 50 pessoas</div>
                                  </div>
                                  <div className="bg-yellow-50 p-2 rounded border border-yellow-200">
                                    <div className="text-xs text-yellow-700">
                                      <div className="font-medium">Dica:</div>
                                      <div>A capacidade limita quantos agendamentos simultâneos são permitidos</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 5: Status de Disponibilidade */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Status de Disponibilidade</h1>
                                </div>
                                <div className="p-4 bg-white dark:bg-gray-900">
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between p-2 bg-green-50 rounded border border-green-200">
                                      <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                                        <div>
                                          <div className="text-sm font-medium">Sala 101</div>
                                          <div className="text-xs text-gray-500">Consultório Principal</div>
                                        </div>
                                      </div>
                                      <div className="text-xs text-green-600 font-medium">Ativo</div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-green-50 rounded border border-green-200">
                                      <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                                        <div>
                                          <div className="text-sm font-medium">Sala 102</div>
                                          <div className="text-xs text-gray-500">Sala de Terapia</div>
                                        </div>
                                      </div>
                                      <div className="text-xs text-green-600 font-medium">Ativo</div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-red-50 rounded border border-red-200">
                                      <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                                        <div>
                                          <div className="text-sm font-medium">Sala 103</div>
                                          <div className="text-xs text-gray-500">Em Manutenção</div>
                                        </div>
                                      </div>
                                      <div className="text-xs text-red-600 font-medium">Inativo</div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-yellow-50 rounded border border-yellow-200">
                                      <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                        <div>
                                          <div className="text-sm font-medium">Sala 104</div>
                                          <div className="text-xs text-gray-500">Em Reforma</div>
                                        </div>
                                      </div>
                                      <div className="text-xs text-yellow-600 font-medium">Temporário</div>
                                    </div>
                                  </div>
                                  <div className="mt-3 bg-gray-50 p-2 rounded">
                                    <div className="text-xs text-gray-600">
                                      <div className="font-medium">Legenda:</div>
                                      <div>🟢 Ativo - Disponível para agendamentos</div>
                                      <div>🔴 Inativo - Não aparece nas opções</div>
                                      <div>🟡 Temporário - Status especial</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 6: Filtros e Busca */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                    <Filter size={20} className="mr-2 text-slate-600" />
                                    Filtros e Busca
                                  </h1>
                                </div>
                                <div className="p-3 space-y-3">
                                  <div>
                                    <input
                                      type="text"
                                      placeholder="Buscar por nome ou descrição..."
                                      className="w-full p-2 border rounded text-xs"
                                      defaultValue="Sala 10"
                                    />
                                  </div>
                                  <div className="grid grid-cols-2 gap-2">
                                    <div>
                                      <label className="text-xs font-medium text-gray-700">Unidade</label>
                                      <select className="w-full p-2 border rounded text-xs">
                                        <option>Todas</option>
                                        <option>Unidade Central</option>
                                        <option>Unidade Norte</option>
                                      </select>
                                    </div>
                                    <div>
                                      <label className="text-xs font-medium text-gray-700">Status</label>
                                      <select className="w-full p-2 border rounded text-xs">
                                        <option>Todos</option>
                                        <option>Ativo</option>
                                        <option>Inativo</option>
                                      </select>
                                    </div>
                                  </div>
                                  <div className="bg-blue-50 p-2 rounded">
                                    <div className="text-xs text-blue-700 font-medium mb-1">Resultados da busca:</div>
                                    <div className="space-y-1">
                                      <div className="bg-white p-2 rounded text-xs">
                                        <div className="font-medium">Sala 101</div>
                                        <div className="text-gray-500">Consultório Principal</div>
                                      </div>
                                      <div className="bg-white p-2 rounded text-xs">
                                        <div className="font-medium">Sala 102</div>
                                        <div className="text-gray-500">Sala de Terapia</div>
                                      </div>
                                    </div>
                                  </div>
                                  <button className="w-full px-3 py-2 bg-blue-500 text-white rounded text-xs">
                                    Aplicar Filtros
                                  </button>
                                </div>
                              </div>
                            </div>

                            {/* Slide 7: Integração com Agendamentos */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Integração com Agendamentos</h1>
                                </div>
                                <div className="p-3">
                                  <div className="bg-green-50 p-3 rounded border border-green-200 mb-3">
                                    <div className="flex items-center gap-2 text-green-700">
                                      <CheckCircle size={16} />
                                      <span className="text-sm font-medium">Locais sincronizados!</span>
                                    </div>
                                    <div className="text-xs text-green-600 mt-1">
                                      Locais ativos aparecem automaticamente no agendamento
                                    </div>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="text-xs font-medium text-gray-700">Ao criar agendamento:</div>
                                    <div className="bg-white border rounded p-2">
                                      <label className="text-xs font-medium text-gray-700">Local</label>
                                      <select className="w-full p-1 border rounded text-xs mt-1">
                                        <option>Selecione um local...</option>
                                        <option>Sala 101 - Consultório Principal</option>
                                        <option>Sala 102 - Sala de Terapia</option>
                                        <option>Sala 201 - Sala de Reuniões</option>
                                      </select>
                                    </div>
                                  </div>
                                  <div className="mt-3 text-xs text-gray-600 bg-gray-50 p-2 rounded">
                                    <div className="font-medium">Benefícios:</div>
                                    <div>• Controle de capacidade automático</div>
                                    <div>• Prevenção de conflitos de local</div>
                                    <div>• Organização por unidades</div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <CheckCircle size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Tutorial Concluído!</h3>
                                <p className="text-purple-200">Agora você pode gerenciar locais e salas eficientemente</p>
                              </div>
                            </div>
                          </>
                        )}

                        {/* Tutorial de Tipos de Serviço */}
                        {selectedTutorial?.id === 'service-types' && (
                          <>
                            {/* Slide 1: Interface Real de Tipos de Serviço */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-4xl h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                {/* Header da página */}
                                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                                  <div className="flex justify-between items-center mb-4">
                                    <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
                                      <Tag size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
                                      Tipos de Serviço
                                    </h1>
                                    <div className="flex items-center gap-2">
                                      <button className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white rounded-lg hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700 transition-all">
                                        <Plus size={16} />
                                        Novo Serviço
                                      </button>
                                    </div>
                                  </div>
                                </div>

                                {/* Área de filtros */}
                                <div className="p-6 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
                                  <div className="bg-white dark:bg-gray-800 border border-purple-200 dark:border-purple-800/30 rounded-lg p-4">
                                    <div className="flex items-center justify-between mb-4">
                                      <h3 className="text-lg font-semibold text-purple-700 dark:text-purple-300">Lista de Tipos de Serviço</h3>
                                      <button className="p-2 text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-purple-400 transition-colors" title="Atualizar lista">
                                        <RefreshCw size={18} />
                                      </button>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                                      <div className="md:col-span-2">
                                        <div className="relative">
                                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={16} />
                                          <input
                                            type="text"
                                            placeholder="Buscar no calendário (título, profissional, paciente, serviço)..."
                                            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 dark:focus:ring-purple-400"
                                          />
                                        </div>
                                      </div>
                                      <div>
                                        <button className="w-full px-3 py-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 border border-purple-200 dark:border-purple-800/30 rounded-lg text-sm hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors">
                                          <Filter size={16} className="inline mr-2" />
                                          Filtros
                                        </button>
                                      </div>
                                      <div>
                                        <button className="w-full px-3 py-2 bg-purple-600 dark:bg-purple-700 text-white rounded-lg text-sm hover:bg-purple-700 dark:hover:bg-purple-800 transition-colors">
                                          <Download size={16} className="inline mr-2" />
                                          Exportar
                                        </button>
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Tabela */}
                                <div className="px-6 pb-6 flex-1 overflow-auto">
                                  <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm">
                                    <table className="w-full">
                                      <thead className="bg-gray-50 dark:bg-gray-700">
                                        <tr>
                                          <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" />
                                          </th>
                                          <th className="text-left px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Nome do Serviço</th>
                                          <th className="text-center px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Valor</th>
                                          <th className="text-right px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Ações</th>
                                        </tr>
                                      </thead>
                                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                          <td className="px-6 py-4 text-center">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" />
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                              <div className="flex-shrink-0 h-10 w-10 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full flex items-center justify-center">
                                                <Tag size={20} />
                                              </div>
                                              <div className="ml-4">
                                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                  Consulta Médica
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap text-center">
                                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                              R$ 150,00
                                            </div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end gap-2">
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Compartilhar">
                                                <Share2 size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Editar">
                                                <Edit size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors" title="Excluir">
                                                <Trash size={16} />
                                              </button>
                                            </div>
                                          </td>
                                        </tr>
                                        <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                          <td className="px-6 py-4 text-center">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" />
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                              <div className="flex-shrink-0 h-10 w-10 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full flex items-center justify-center">
                                                <Tag size={20} />
                                              </div>
                                              <div className="ml-4">
                                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                  Terapia
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap text-center">
                                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                              R$ 120,00
                                            </div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end gap-2">
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Compartilhar">
                                                <Share2 size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Editar">
                                                <Edit size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors" title="Excluir">
                                                <Trash size={16} />
                                              </button>
                                            </div>
                                          </td>
                                        </tr>
                                        <tr className="hover:bg-purple-50 dark:hover:bg-purple-900/20">
                                          <td className="px-6 py-4 text-center">
                                            <input type="checkbox" className="rounded border-gray-300 dark:border-gray-600 text-purple-600 focus:ring-purple-500" />
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center">
                                              <div className="flex-shrink-0 h-10 w-10 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 rounded-full flex items-center justify-center">
                                                <Tag size={20} />
                                              </div>
                                              <div className="ml-4">
                                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                  Avaliação
                                                </div>
                                              </div>
                                            </div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap text-center">
                                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                              R$ 200,00
                                            </div>
                                          </td>
                                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end gap-2">
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Compartilhar">
                                                <Share2 size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors" title="Editar">
                                                <Edit size={16} />
                                              </button>
                                              <button className="p-1 text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors" title="Excluir">
                                                <Trash size={16} />
                                              </button>
                                            </div>
                                          </td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </div>

                                  {/* Paginação */}
                                  <div className="mt-4 flex items-center justify-between text-sm text-gray-700 dark:text-gray-300">
                                    <div className="flex items-center gap-2">
                                      <span>Mostrando</span>
                                      <select className="border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-xs bg-white dark:bg-gray-700">
                                        <option>10</option>
                                        <option>25</option>
                                        <option>50</option>
                                      </select>
                                      <span>de 3 tipos de serviço</span>
                                    </div>
                                    <div className="flex gap-1">
                                      <button className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-gray-400 dark:text-gray-500" disabled>Anterior</button>
                                      <button className="px-3 py-1 bg-purple-600 text-white rounded">1</button>
                                      <button className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-gray-400 dark:text-gray-500" disabled>Próximo</button>
                                    </div>
                                  </div>
                                </div>

                                {/* Indicador de ação */}
                                <div className="absolute top-16 right-4 bg-green-400 text-white px-3 py-2 rounded-lg shadow-lg text-xs font-medium animate-bounce">
                                  ➕ Clique em "Novo Serviço" para adicionar
                                </div>
                              </div>
                            </div>

                            {/* Slide 2: Cadastrando Tipo de Serviço */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Novo Tipo de Serviço</h1>
                                </div>
                                <div className="p-4 space-y-4 bg-white dark:bg-gray-900">
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Nome do Serviço</label>
                                    <input type="text" placeholder="Ex: Consulta Psicológica" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Duração (minutos)</label>
                                    <input type="number" placeholder="60" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Preço</label>
                                    <input type="number" placeholder="150.00" className="w-full p-2 border rounded text-xs" />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Cor</label>
                                    <div className="flex gap-2">
                                      <div className="w-8 h-8 bg-blue-400 rounded cursor-pointer border-2 border-blue-600"></div>
                                      <div className="w-8 h-8 bg-green-400 rounded cursor-pointer"></div>
                                      <div className="w-8 h-8 bg-purple-400 rounded cursor-pointer"></div>
                                      <div className="w-8 h-8 bg-orange-400 rounded cursor-pointer"></div>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Profissionais Habilitados</label>
                                    <div className="space-y-1">
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" defaultChecked readOnly />
                                        Dr. João Santos
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" readOnly />
                                        Dra. Ana Costa
                                      </label>
                                    </div>
                                  </div>
                                  <div className="flex gap-2 pt-2">
                                    <button className="flex-1 px-3 py-2 bg-orange-500 text-white rounded text-xs">
                                      Salvar
                                    </button>
                                    <button className="flex-1 px-3 py-2 bg-gray-300 text-gray-700 rounded text-xs">
                                      Cancelar
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 3: Duração Personalizada */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Duração Personalizada</h1>
                                </div>
                                <div className="p-3">
                                  <div className="space-y-3">
                                    <div className="bg-blue-50 p-3 rounded border border-blue-200">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-blue-800">Consulta Médica</span>
                                        <span className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded">60 min</span>
                                      </div>
                                      <div className="text-xs text-blue-600">Tempo padrão para consultas médicas</div>
                                    </div>
                                    <div className="bg-green-50 p-3 rounded border border-green-200">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-green-800">Terapia</span>
                                        <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">45 min</span>
                                      </div>
                                      <div className="text-xs text-green-600">Sessões de terapia mais curtas</div>
                                    </div>
                                    <div className="bg-purple-50 p-3 rounded border border-purple-200">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-purple-800">Avaliação</span>
                                        <span className="text-xs bg-purple-200 text-purple-800 px-2 py-1 rounded">90 min</span>
                                      </div>
                                      <div className="text-xs text-purple-600">Avaliações detalhadas mais longas</div>
                                    </div>
                                    <div className="bg-orange-50 p-3 rounded border border-orange-200">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-orange-800">Retorno</span>
                                        <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded">30 min</span>
                                      </div>
                                      <div className="text-xs text-orange-600">Consultas de retorno rápidas</div>
                                    </div>
                                  </div>
                                  <div className="mt-3 bg-yellow-50 p-2 rounded border border-yellow-200">
                                    <div className="text-xs text-yellow-700">
                                      <div className="font-medium">Dica:</div>
                                      <div>A duração afeta automaticamente os horários disponíveis no calendário</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 4: Cores Identificadoras */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Cores Identificadoras</h1>
                                </div>
                                <div className="p-3">
                                  <div className="mb-3 text-xs text-gray-600">
                                    Visualização no calendário:
                                  </div>
                                  <div className="bg-gray-50 p-3 rounded">
                                    <div className="space-y-2">
                                      <div className="flex items-center gap-2 p-2 bg-blue-100 rounded border-l-4 border-blue-400">
                                        <div className="w-3 h-3 bg-blue-400 rounded"></div>
                                        <div className="text-xs">
                                          <div className="font-medium">09:00 - Consulta Médica</div>
                                          <div className="text-gray-500">Maria Silva - Dr. João</div>
                                        </div>
                                      </div>
                                      <div className="flex items-center gap-2 p-2 bg-green-100 rounded border-l-4 border-green-400">
                                        <div className="w-3 h-3 bg-green-400 rounded"></div>
                                        <div className="text-xs">
                                          <div className="font-medium">10:00 - Terapia</div>
                                          <div className="text-gray-500">Pedro Santos - Dra. Ana</div>
                                        </div>
                                      </div>
                                      <div className="flex items-center gap-2 p-2 bg-purple-100 rounded border-l-4 border-purple-400">
                                        <div className="w-3 h-3 bg-purple-400 rounded"></div>
                                        <div className="text-xs">
                                          <div className="font-medium">14:00 - Avaliação</div>
                                          <div className="text-gray-500">Ana Costa - Dr. João</div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="mt-3">
                                    <div className="text-xs font-medium text-gray-700 mb-2">Paleta de cores:</div>
                                    <div className="flex gap-2">
                                      <div className="w-8 h-8 bg-blue-400 rounded cursor-pointer border-2 border-blue-600"></div>
                                      <div className="w-8 h-8 bg-green-400 rounded cursor-pointer"></div>
                                      <div className="w-8 h-8 bg-purple-400 rounded cursor-pointer"></div>
                                      <div className="w-8 h-8 bg-orange-400 rounded cursor-pointer"></div>
                                      <div className="w-8 h-8 bg-red-400 rounded cursor-pointer"></div>
                                      <div className="w-8 h-8 bg-yellow-400 rounded cursor-pointer"></div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 5: Profissionais Habilitados */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Profissionais Habilitados</h1>
                                </div>
                                <div className="p-3">
                                  <div className="space-y-3">
                                    <div className="bg-blue-50 p-3 rounded border border-blue-200">
                                      <div className="text-sm font-medium text-blue-800 mb-2">Consulta Médica</div>
                                      <div className="space-y-1">
                                        <div className="flex items-center gap-2 text-xs">
                                          <CheckCircle size={12} className="text-green-500" />
                                          <span>Dr. João Santos - Clínico Geral</span>
                                        </div>
                                        <div className="flex items-center gap-2 text-xs">
                                          <CheckCircle size={12} className="text-green-500" />
                                          <span>Dr. Pedro Lima - Cardiologista</span>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="bg-green-50 p-3 rounded border border-green-200">
                                      <div className="text-sm font-medium text-green-800 mb-2">Terapia</div>
                                      <div className="space-y-1">
                                        <div className="flex items-center gap-2 text-xs">
                                          <CheckCircle size={12} className="text-green-500" />
                                          <span>Dra. Ana Costa - Psicóloga</span>
                                        </div>
                                        <div className="flex items-center gap-2 text-xs">
                                          <XCircle size={12} className="text-gray-400" />
                                          <span className="text-gray-500">Dr. João Santos - Não habilitado</span>
                                        </div>
                                      </div>
                                    </div>
                                    <div className="bg-purple-50 p-3 rounded border border-purple-200">
                                      <div className="text-sm font-medium text-purple-800 mb-2">Avaliação</div>
                                      <div className="space-y-1">
                                        <div className="flex items-center gap-2 text-xs">
                                          <CheckCircle size={12} className="text-green-500" />
                                          <span>Dr. João Santos - Especialista</span>
                                        </div>
                                        <div className="flex items-center gap-2 text-xs">
                                          <CheckCircle size={12} className="text-green-500" />
                                          <span>Dra. Ana Costa - Especialista</span>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 6: Controle de Preços */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Controle de Preços</h1>
                                </div>
                                <div className="p-3">
                                  <div className="space-y-3">
                                    <div className="bg-blue-50 p-3 rounded border border-blue-200">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-blue-800">Consulta Médica</span>
                                        <span className="text-sm font-bold text-blue-800">R$ 150,00</span>
                                      </div>
                                      <div className="text-xs text-blue-600">Valor padrão para consultas</div>
                                    </div>
                                    <div className="bg-green-50 p-3 rounded border border-green-200">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-green-800">Terapia</span>
                                        <span className="text-sm font-bold text-green-800">R$ 120,00</span>
                                      </div>
                                      <div className="text-xs text-green-600">Sessões de terapia</div>
                                    </div>
                                    <div className="bg-purple-50 p-3 rounded border border-purple-200">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-purple-800">Avaliação</span>
                                        <span className="text-sm font-bold text-purple-800">R$ 200,00</span>
                                      </div>
                                      <div className="text-xs text-purple-600">Avaliações completas</div>
                                    </div>
                                  </div>
                                  <div className="mt-3 bg-gray-50 p-3 rounded">
                                    <div className="text-xs font-medium text-gray-700 mb-2">Resumo Financeiro:</div>
                                    <div className="space-y-1 text-xs text-gray-600">
                                      <div className="flex justify-between">
                                        <span>Total de serviços:</span>
                                        <span>3 tipos</span>
                                      </div>
                                      <div className="flex justify-between">
                                        <span>Valor médio:</span>
                                        <span>R$ 156,67</span>
                                      </div>
                                      <div className="flex justify-between font-medium">
                                        <span>Receita potencial/dia:</span>
                                        <span>R$ 1.200,00</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 7: Integração Automática */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Integração Automática</h1>
                                </div>
                                <div className="p-3">
                                  <div className="bg-green-50 p-3 rounded border border-green-200 mb-3">
                                    <div className="flex items-center gap-2 text-green-700">
                                      <CheckCircle size={16} />
                                      <span className="text-sm font-medium">Tipos de serviço sincronizados!</span>
                                    </div>
                                    <div className="text-xs text-green-600 mt-1">
                                      Aparecem automaticamente no formulário de agendamento
                                    </div>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="text-xs font-medium text-gray-700">Ao criar agendamento:</div>
                                    <div className="bg-white border rounded p-2">
                                      <label className="text-xs font-medium text-gray-700">Tipo de Serviço</label>
                                      <select className="w-full p-1 border rounded text-xs mt-1">
                                        <option>Selecione um serviço...</option>
                                        <option>Consulta Médica - R$ 150,00 (60 min)</option>
                                        <option>Terapia - R$ 120,00 (45 min)</option>
                                        <option>Avaliação - R$ 200,00 (90 min)</option>
                                      </select>
                                    </div>
                                    <div className="bg-blue-50 p-2 rounded text-xs">
                                      <div className="font-medium text-blue-800">Selecionado: Consulta Médica</div>
                                      <div className="text-blue-600">Duração: 60 min | Valor: R$ 150,00</div>
                                      <div className="text-blue-600">Profissionais: Dr. João, Dr. Pedro</div>
                                    </div>
                                  </div>
                                  <div className="mt-3 text-xs text-gray-600 bg-gray-50 p-2 rounded">
                                    <div className="font-medium">Benefícios:</div>
                                    <div>• Cálculo automático de duração</div>
                                    <div>• Filtro de profissionais habilitados</div>
                                    <div>• Controle de preços integrado</div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <CheckCircle size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Tutorial Concluído!</h3>
                                <p className="text-purple-200">Agora você pode gerenciar tipos de serviço eficientemente</p>
                              </div>
                            </div>
                          </>
                        )}

                        {/* Tutorial de Relatórios */}
                        {selectedTutorial?.id === 'reports' && (
                          <>
                            {/* Slide 1: Dashboard de Relatórios */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white flex items-center">
                                    <LayoutDashboard size={20} className="mr-2 text-purple-600 dark:text-purple-400" />
                                    Dashboard
                                  </h1>
                                </div>
                                <div className="p-4 bg-white dark:bg-gray-900">
                                  <div className="grid grid-cols-2 gap-3 mb-4">
                                    <div className="bg-blue-50 p-3 rounded border">
                                      <div className="text-xs text-gray-600">Agendamentos Hoje</div>
                                      <div className="text-lg font-bold text-blue-600">24</div>
                                    </div>
                                    <div className="bg-green-50 p-3 rounded border">
                                      <div className="text-xs text-gray-600">Taxa de Ocupação</div>
                                      <div className="text-lg font-bold text-green-600">85%</div>
                                    </div>
                                    <div className="bg-orange-50 p-3 rounded border">
                                      <div className="text-xs text-gray-600">Receita Mensal</div>
                                      <div className="text-lg font-bold text-orange-600">R$ 45k</div>
                                    </div>
                                    <div className="bg-purple-50 p-3 rounded border">
                                      <div className="text-xs text-gray-600">Profissionais Ativos</div>
                                      <div className="text-lg font-bold text-purple-600">8</div>
                                    </div>
                                  </div>
                                  <div className="bg-gray-50 p-3 rounded">
                                    <div className="text-xs font-medium text-gray-700 mb-2">Agendamentos por Dia</div>
                                    <div className="flex items-end gap-1 h-16">
                                      <div className="bg-blue-400 w-4" style={{height: '60%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '80%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '40%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '90%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '70%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '50%'}}></div>
                                      <div className="bg-blue-400 w-4" style={{height: '30%'}}></div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 2: Relatório de Agendamentos */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <div className="flex justify-between items-center">
                                    <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                      <FileText size={20} className="mr-2 text-slate-600" />
                                      Relatório de Agendamentos
                                    </h1>
                                    <button className="flex items-center gap-1 px-2 py-1 bg-red-500 text-white rounded text-xs">
                                      <Download size={12} />
                                      Exportar
                                    </button>
                                  </div>
                                </div>
                                <div className="p-3">
                                  <div className="mb-3 flex gap-2">
                                    <select className="flex-1 p-1 border rounded text-xs">
                                      <option>Últimos 30 dias</option>
                                    </select>
                                    <select className="flex-1 p-1 border rounded text-xs">
                                      <option>Todos os profissionais</option>
                                    </select>
                                  </div>
                                  <div className="space-y-2">
                                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                                      <div>
                                        <div className="font-medium">Maria Silva</div>
                                        <div className="text-gray-500">Dr. João - 15/03 09:00</div>
                                      </div>
                                      <div className="text-green-600 font-medium">Realizado</div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                                      <div>
                                        <div className="font-medium">Pedro Santos</div>
                                        <div className="text-gray-500">Dra. Ana - 15/03 10:00</div>
                                      </div>
                                      <div className="text-blue-600 font-medium">Agendado</div>
                                    </div>
                                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded text-xs">
                                      <div>
                                        <div className="font-medium">Ana Costa</div>
                                        <div className="text-gray-500">Dr. João - 15/03 11:00</div>
                                      </div>
                                      <div className="text-red-600 font-medium">Cancelado</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 3: Gráficos Interativos */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white dark:bg-gray-900 w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative border border-gray-200 dark:border-gray-700">
                                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                                  <h1 className="text-lg font-bold text-slate-800 dark:text-white">Gráficos Interativos</h1>
                                </div>
                                <div className="p-4 bg-white dark:bg-gray-900">
                                  <div className="flex gap-2 mb-3">
                                    <button className="px-3 py-1 bg-red-500 text-white rounded text-xs">Pizza</button>
                                    <button className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-xs">Barras</button>
                                    <button className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-xs">Linhas</button>
                                  </div>
                                  <div className="bg-gray-50 rounded p-3 mb-3">
                                    <div className="flex items-center justify-center h-32">
                                      <div className="relative">
                                        <div className="w-24 h-24 rounded-full border-8 border-blue-400" style={{borderRightColor: '#f87171', borderBottomColor: '#34d399', borderLeftColor: '#a78bfa'}}></div>
                                        <div className="absolute inset-0 flex items-center justify-center">
                                          <PieChart size={32} className="text-gray-400" />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="space-y-1 text-xs">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-blue-400 rounded"></div>
                                        <span>Consultas</span>
                                      </div>
                                      <span>45%</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-red-400 rounded"></div>
                                        <span>Terapias</span>
                                      </div>
                                      <span>30%</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-green-400 rounded"></div>
                                        <span>Avaliações</span>
                                      </div>
                                      <span>15%</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        <div className="w-3 h-3 bg-purple-400 rounded"></div>
                                        <span>Outros</span>
                                      </div>
                                      <span>10%</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 4: Filtros Avançados */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                    <Filter size={20} className="mr-2 text-slate-600" />
                                    Filtros Avançados
                                  </h1>
                                </div>
                                <div className="p-3 space-y-3">
                                  <div className="grid grid-cols-2 gap-2">
                                    <div>
                                      <label className="text-xs font-medium text-gray-700">Data Início</label>
                                      <input type="date" className="w-full p-1 border rounded text-xs" />
                                    </div>
                                    <div>
                                      <label className="text-xs font-medium text-gray-700">Data Fim</label>
                                      <input type="date" className="w-full p-1 border rounded text-xs" />
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Profissionais</label>
                                    <div className="space-y-1 mt-1">
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" defaultChecked readOnly />
                                        Dr. João Santos
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" defaultChecked readOnly />
                                        Dra. Ana Costa
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" readOnly />
                                        Dr. Pedro Lima
                                      </label>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Status</label>
                                    <div className="space-y-1 mt-1">
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" defaultChecked readOnly />
                                        Realizado
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" defaultChecked readOnly />
                                        Agendado
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" readOnly />
                                        Cancelado
                                      </label>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Tipo de Serviço</label>
                                    <select className="w-full p-1 border rounded text-xs mt-1">
                                      <option>Todos os tipos</option>
                                      <option>Consulta Médica</option>
                                      <option>Terapia</option>
                                      <option>Avaliação</option>
                                    </select>
                                  </div>
                                  <button className="w-full px-3 py-2 bg-red-500 text-white rounded text-xs">
                                    Aplicar Filtros
                                  </button>
                                </div>
                              </div>
                            </div>

                            {/* Slide 5: Exportação Flexível */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <div className="flex justify-between items-center">
                                    <h1 className="text-lg font-bold text-slate-800">Exportação Flexível</h1>
                                    <button className="flex items-center gap-1 px-2 py-1 bg-red-500 text-white rounded text-xs">
                                      <Download size={12} />
                                      Exportar
                                    </button>
                                  </div>
                                </div>
                                <div className="p-3 space-y-3">
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Formato de Exportação</label>
                                    <div className="grid grid-cols-2 gap-2 mt-1">
                                      <button className="p-2 border rounded text-xs hover:bg-gray-50 flex items-center gap-2 bg-red-50 border-red-200">
                                        <FileText size={14} />
                                        PDF
                                      </button>
                                      <button className="p-2 border rounded text-xs hover:bg-gray-50 flex items-center gap-2">
                                        <FileText size={14} />
                                        Excel
                                      </button>
                                      <button className="p-2 border rounded text-xs hover:bg-gray-50 flex items-center gap-2">
                                        <FileText size={14} />
                                        CSV
                                      </button>
                                      <button className="p-2 border rounded text-xs hover:bg-gray-50 flex items-center gap-2">
                                        <FileText size={14} />
                                        JSON
                                      </button>
                                    </div>
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium text-gray-700">Dados a Incluir</label>
                                    <div className="space-y-1 mt-1">
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" defaultChecked readOnly />
                                        Informações do paciente
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" defaultChecked readOnly />
                                        Dados do profissional
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" defaultChecked readOnly />
                                        Tipo de serviço
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" readOnly />
                                        Valores financeiros
                                      </label>
                                      <label className="flex items-center gap-2 text-xs">
                                        <input type="checkbox" className="rounded" readOnly />
                                        Observações
                                      </label>
                                    </div>
                                  </div>
                                  <div className="bg-green-50 p-2 rounded border border-green-200">
                                    <div className="text-xs text-green-700">
                                      <div className="font-medium">Relatório pronto!</div>
                                      <div>156 agendamentos encontrados</div>
                                      <div>Período: 01/03 a 31/03/2024</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 6: Análise de Ocupação */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Análise de Ocupação</h1>
                                </div>
                                <div className="p-3">
                                  <div className="space-y-3">
                                    <div className="bg-blue-50 p-3 rounded border border-blue-200">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-blue-800">Dr. João Santos</span>
                                        <span className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded">85%</span>
                                      </div>
                                      <div className="w-full bg-blue-200 rounded-full h-2">
                                        <div className="bg-blue-500 h-2 rounded-full" style={{width: '85%'}}></div>
                                      </div>
                                      <div className="text-xs text-blue-600 mt-1">34/40 horas ocupadas</div>
                                    </div>
                                    <div className="bg-green-50 p-3 rounded border border-green-200">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-green-800">Dra. Ana Costa</span>
                                        <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">72%</span>
                                      </div>
                                      <div className="w-full bg-green-200 rounded-full h-2">
                                        <div className="bg-green-500 h-2 rounded-full" style={{width: '72%'}}></div>
                                      </div>
                                      <div className="text-xs text-green-600 mt-1">29/40 horas ocupadas</div>
                                    </div>
                                    <div className="bg-orange-50 p-3 rounded border border-orange-200">
                                      <div className="flex items-center justify-between mb-2">
                                        <span className="text-sm font-medium text-orange-800">Dr. Pedro Lima</span>
                                        <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded">45%</span>
                                      </div>
                                      <div className="w-full bg-orange-200 rounded-full h-2">
                                        <div className="bg-orange-500 h-2 rounded-full" style={{width: '45%'}}></div>
                                      </div>
                                      <div className="text-xs text-orange-600 mt-1">18/40 horas ocupadas</div>
                                    </div>
                                  </div>
                                  <div className="mt-3 bg-gray-50 p-2 rounded">
                                    <div className="text-xs text-gray-600">
                                      <div className="font-medium">Média geral: 67%</div>
                                      <div>Total de horas: 120h</div>
                                      <div>Horas ocupadas: 81h</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Slide 7: Indicadores de Performance */}
                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                                <div className="p-2 border-b">
                                  <h1 className="text-lg font-bold text-slate-800">Indicadores de Performance</h1>
                                </div>
                                <div className="p-3">
                                  <div className="grid grid-cols-2 gap-3 mb-4">
                                    <div className="bg-blue-50 p-3 rounded border border-blue-200 text-center">
                                      <div className="text-lg font-bold text-blue-600">156</div>
                                      <div className="text-xs text-blue-800">Agendamentos</div>
                                      <div className="text-xs text-green-600">↑ 12%</div>
                                    </div>
                                    <div className="bg-green-50 p-3 rounded border border-green-200 text-center">
                                      <div className="text-lg font-bold text-green-600">89%</div>
                                      <div className="text-xs text-green-800">Taxa Comparecimento</div>
                                      <div className="text-xs text-green-600">↑ 5%</div>
                                    </div>
                                    <div className="bg-purple-50 p-3 rounded border border-purple-200 text-center">
                                      <div className="text-lg font-bold text-purple-600">R$ 18.7k</div>
                                      <div className="text-xs text-purple-800">Receita</div>
                                      <div className="text-xs text-green-600">↑ 8%</div>
                                    </div>
                                    <div className="bg-orange-50 p-3 rounded border border-orange-200 text-center">
                                      <div className="text-lg font-bold text-orange-600">4.2</div>
                                      <div className="text-xs text-orange-800">Avaliação Média</div>
                                      <div className="text-xs text-green-600">↑ 0.3</div>
                                    </div>
                                  </div>
                                  <div className="bg-gray-50 p-3 rounded">
                                    <div className="text-xs font-medium text-gray-700 mb-2">Tendência Mensal:</div>
                                    <div className="flex items-end gap-1 h-12">
                                      <div className="bg-blue-400 w-3" style={{height: '40%'}}></div>
                                      <div className="bg-blue-400 w-3" style={{height: '60%'}}></div>
                                      <div className="bg-blue-400 w-3" style={{height: '80%'}}></div>
                                      <div className="bg-blue-400 w-3" style={{height: '70%'}}></div>
                                      <div className="bg-blue-400 w-3" style={{height: '90%'}}></div>
                                      <div className="bg-blue-400 w-3" style={{height: '100%'}}></div>
                                    </div>
                                    <div className="text-xs text-gray-600 mt-1">Jan - Jun 2024</div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                              <div className="text-center text-white p-8">
                                <CheckCircle size={64} className="mx-auto mb-4 text-green-300" />
                                <h3 className="text-xl font-bold mb-2">Tutorial Concluído!</h3>
                                <p className="text-purple-200">Agora você pode gerar relatórios e analisar dados eficientemente</p>
                              </div>
                            </div>
                          </>
                        )}

                      </div>
                    </div>
                  </div>
                ) : (
                  // Tela de seleção quando tutorial está selecionado mas não está tocando
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center text-white p-8">
                      <div className={`w-20 h-20 rounded-full bg-gradient-to-r ${selectedTutorial?.color || 'from-purple-600 to-purple-500'} dark:${selectedTutorial?.darkColor || 'from-purple-700 to-purple-600'} flex items-center justify-center mx-auto mb-4`}>
                        {selectedTutorial?.icon}
                      </div>
                      <h3 className="text-xl font-bold mb-2">{selectedTutorial?.title}</h3>
                      <p className="text-purple-200 text-sm mb-4">{selectedTutorial?.description}</p>
                      <button
                        onClick={() => setIsVideoPlaying(true)}
                        className="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors flex items-center gap-2 mx-auto"
                      >
                        <Play size={16} />
                        Iniciar Tutorial
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>



          {/* Section cards */}
          <h3 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center">
            <Info className="mr-2 text-purple-500" size={20} />
            Seções do Módulo
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            {/* Calendar section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Calendar className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Calendário</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Visualize e gerencie todos os agendamentos em uma interface intuitiva de calendário.
                      Alterne entre visualizações diárias, semanais e mensais para melhor organização.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Visualização de calendário;
                        Criação rápida de agendamentos; Filtros avançados; Exportação de dados.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Calendar size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Working Hours section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Clock className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Horários de Trabalho</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Configure os horários de disponibilidade dos profissionais para agendamentos.
                      Defina horários personalizados para cada dia da semana.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Configuração de horários;
                        Seleção por arraste; Cópia de horários; Visualização por profissional.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Clock size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Locations section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <MapPin className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Locais e Salas</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie os locais e salas disponíveis para agendamentos.
                      Organize os atendimentos por unidade, andar ou tipo de sala.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro de locais;
                        Vinculação com unidades; Configuração de capacidade; Status de disponibilidade.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <MapPin size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Service Types section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Tag className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Tipos de Serviço</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie os diferentes tipos de serviços oferecidos pela sua clínica.
                      Configure preços, duração e profissionais habilitados para cada serviço.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro de serviços;
                        Definição de preços; Configuração de duração; Vinculação com profissionais.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Tag size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Occupancy section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Briefcase className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Ocupação</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Analise detalhadamente a ocupação dos profissionais, salas e horários.
                      Identifique períodos de maior demanda e otimize a agenda.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Análise de ocupação;
                        Gráficos por período; Filtros por profissional; Exportação de dados.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Briefcase size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Reports section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <FileText className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Relatórios</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Acesse relatórios detalhados sobre agendamentos, ocupação de salas,
                      produtividade de profissionais e muito mais.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Relatórios personalizáveis;
                        Exportação em diversos formatos; Listagem de agendamentos; Filtros avançados.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <FileText size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Dashboard section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <LayoutDashboard className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Dashboard</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Visualize estatísticas e indicadores de desempenho dos agendamentos.
                      Acompanhe métricas importantes para a gestão da sua clínica.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Gráficos interativos;
                        Indicadores de desempenho; Análise de tendências; Filtros por período.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <BarChart4 size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Getting started section */}
          <div className="mt-8 bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 p-6">
            <h3 className="text-lg font-semibold text-purple-800 dark:text-white mb-4 flex items-center">
              <LayoutDashboard className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
              Começando
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Para começar a utilizar o módulo de agendamento, recomendamos seguir estes passos:
            </p>
            <ol className="space-y-3 text-gray-600 dark:text-gray-300">
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">1</span>
                <span>Verifique os <strong>horários de trabalho</strong> dos profissionais para garantir que estejam corretamente configurados.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">2</span>
                <span>Confira os <strong>locais e salas</strong> disponíveis para agendamentos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">3</span>
                <span>Acesse o <strong>calendário</strong> para visualizar e criar novos agendamentos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">4</span>
                <span>Utilize os <strong>relatórios</strong> para acompanhar e analisar os agendamentos realizados.</span>
              </li>
            </ol>
            <div className="mt-6 flex justify-center gap-4">
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="px-5 py-2.5 bg-white/20 hover:bg-white/30 text-purple-600 border border-purple-300 rounded-lg shadow transition-colors flex items-center gap-2"
              >
                <img
                  src="/logo_horizontal_sem_fundo.png"
                  alt="High Tide"
                  className="h-4 w-auto"
                />
                Dashboard
              </button>
              <button
                onClick={() => window.location.href = '/dashboard/scheduler/calendar'}
                className="px-5 py-2.5 bg-purple-600 hover:bg-purple-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
              >
                <Calendar size={18} />
                Ir para o Calendário
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntroductionPage;
