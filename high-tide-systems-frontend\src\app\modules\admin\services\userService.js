import { api } from '@/utils/api';
import { User } from '../models/user';
import { exportService } from "@/app/services/exportService";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";
import { applyUserPrivacyMasksForExport, getPrivacyWarningForExport } from '@/utils/exportPrivacy';

export const userService = {
  // Listar usuários com paginação e filtros
  list: async (page = 1, limit = 10, filters = {}) => {
    try {
      // Extrair userIds e parâmetros de ordenação do objeto filters para tratamento especial
      const { userIds, sortField, sortDirection, ...otherFilters } = filters;

      // Criar objeto de parâmetros base
      const params = {
        page,
        limit,
        ...otherFilters
      };

      // Adicionar parâmetros de ordenação se fornecidos
      if (sortField) {
        params.sortField = sortField;
      }
      if (sortDirection) {
        params.sortDirection = sortDirection;
      }

      // Construir query params
      const queryParams = new URLSearchParams(params);

      // Adicionar userIds como parâmetros separados com notação de array
      if (userIds && userIds.length > 0) {
        // Garantir que userIds seja um array
        const userIdsArray = Array.isArray(userIds) ? userIds : [userIds];

        // Adicionar cada ID como um parâmetro separado
        userIdsArray.forEach((id, index) => {
          // Usar a notação de array para compatibilidade com a API
          queryParams.append(`userIds[${index}]`, id);
        });

        console.log("Filtrando por múltiplos IDs de usuários:", userIdsArray);
      }

      const response = await api.get(`/users?${queryParams}`);

      // Transform users to User objects
      if (response.data.users) {
        response.data.users = response.data.users.map(user => new User(user));
      }

      return response.data;
    } catch (error) {
      console.error('Erro ao listar usuários:', error);
      throw error;
    }
  },

  // Obter detalhes de um usuário específico
  get: async (id) => {
    try {
      const response = await api.get(`/users/${id}`);
      return new User(response.data);
    } catch (error) {
      console.error(`Erro ao buscar usuário ${id}:`, error);
      throw error;
    }
  },

  // Criar um novo usuário
  create: async (userData) => {
    try {
      const response = await api.post('/users', userData);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar usuário:', error);
      throw error;
    }
  },

  // Atualizar um usuário existente
  update: async (id, userData) => {
    try {
      const response = await api.put(`/users/${id}`, userData);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar usuário ${id}:`, error);
      throw error;
    }
  },

  // Atualizar módulos de um usuário
  updateModules: async (id, modules) => {
    try {
      const response = await api.patch(`/users/${id}/modules`, { modules });
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar módulos do usuário ${id}:`, error);
      throw error;
    }
  },

  // Atualizar permissões de um usuário
  updatePermissions: async (id, permissions) => {
    try {
      console.log(`[userService] Atualizando permissões do usuário ${id}`);
      console.log(`[userService] Permissões enviadas:`, permissions);
      const response = await api.patch(`/users/${id}/permissions`, { permissions });
      console.log(`[userService] Resposta da API:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar permissões do usuário ${id}:`, error);
      throw error;
    }
  },

  // Atualizar papel (role) de um usuário
  updateRole: async (id, role) => {
    try {
      const response = await api.patch(`/users/${id}/role`, { role });
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar papel do usuário ${id}:`, error);
      throw error;
    }
  },

  // Ativar/desativar um usuário
  toggleStatus: async (id, active) => {
    try {
      const response = await api.patch(`/users/${id}/status`, { active });
      return response.data;
    } catch (error) {
      console.error(`Erro ao alterar status do usuário ${id}:`, error);
      throw error;
    }
  },

  // Excluir um usuário
  delete: async (id) => {
    try {
      const response = await api.delete(`/users/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao excluir usuário ${id}:`, error);
      throw error;
    }
  },

  // Upload de imagem de perfil
  uploadProfileImage: async (id, imageFile) => {
    try {
      console.log(`Iniciando upload de imagem para usuário ${id}`);
      console.log('Arquivo:', imageFile.name, imageFile.type, imageFile.size);

      const formData = new FormData();
      formData.append('profileImage', imageFile);
      console.log('FormData criado com sucesso');

      // Verificar o token de autenticação
      const token = localStorage.getItem('token');
      console.log('Token de autenticação:', token ? 'Presente' : 'Ausente');

      console.log(`Enviando requisição POST para /users/${id}/profile-image`);
      const response = await api.post(`/users/${id}/profile-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        },
      });

      console.log('Upload concluído com sucesso');
      console.log('Resposta:', response.data);
      return response.data;
    } catch (error) {
      console.error(`Erro ao fazer upload de imagem para usuário ${id}:`, error);
      throw error;
    }
  },

  // Obter URL da imagem de perfil
  getProfileImageUrl: (userId) => {
    if (!userId) return null;

    // Obter a URL base da API
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';

    // Construir a URL da imagem de perfil
    return `${baseUrl}/users/${userId}/profile-image`;
  },

  /**
   * Exporta a lista de usuários com os filtros aplicados
   * @param {Object} filters - Filtros atuais (busca, status, etc)
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @param {Object} preferences - Preferências de privacidade (opcional)
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  exportUsers: async (filters, exportFormat = "xlsx", preferences = null) => {
    try {
      // Obter os dados filtrados da API
      const response = await userService.list(1, 1000, {
        ...filters,
      });

      // Extrair os dados dos usuários
      let data = response.users || [];

      // Aplicar máscaras de privacidade se as preferências foram fornecidas
      if (preferences) {
        data = applyUserPrivacyMasksForExport(data, preferences);
      }

      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Definição das colunas com formatação
      const columns = [
        { key: "fullName", header: "Nome Completo" },
        { key: "email", header: "Email" },
        { key: "role", header: "Papel",
          format: (value) => {
            const roles = {
              SYSTEM_ADMIN: "Administrador do Sistema",
              COMPANY_ADMIN: "Administrador da Empresa",
              EMPLOYEE: "Funcionário"
            };
            return roles[value] || value;
          }
        },
        { key: "companyName", header: "Empresa" },
        {
          key: "active",
          header: "Status",
          format: (value) => value ? "Ativo" : "Inativo",
          align: "center",
          width: 20
        },
        { key: "createdAt", header: "Data de Cadastro", type: "date" },
      ];

      // Preparar os dados para exportação
      const preparedData = data.map(user => {
        return {
          fullName: user.fullName || "",
          email: user.email || "",
          role: user.role || "",
          companyName: user.company?.name || "",
          active: user.active,
          createdAt: user.createdAt || "",
        };
      });

      // Filtros aplicados para subtítulo
      let subtitleParts = [];
      if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
      if (filters.userIds && filters.userIds.length > 0) {
        subtitleParts.push(`Usuários específicos: ${filters.userIds.length} selecionados`);
      }
      if (filters.active !== undefined) {
        subtitleParts.push(`Status: ${filters.active ? "Ativos" : "Inativos"}`);
      }
      if (filters.module) {
        subtitleParts.push(`Módulo: ${filters.module}`);
      }
      if (filters.companyId) {
        subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);
      }

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (subtitleParts.length > 0) {
        subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
      }

      // Adicionar aviso de privacidade se aplicável
      let privacyWarning = '';
      if (preferences) {
        privacyWarning = getPrivacyWarningForExport('user', preferences);
        if (privacyWarning) {
          subtitle += `\n\n${privacyWarning}`;
        }
      }

      // Exportar os dados
      return await exportService.exportData(preparedData, {
        format: exportFormat,
        filename: "usuarios",
        columns,
        title: "Lista de Usuários",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar usuários:", error);
      return false;
    }
  }
};

export default userService;
