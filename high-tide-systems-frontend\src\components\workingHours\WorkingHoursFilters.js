'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Search, RefreshCw, Filter, Building, Users, Briefcase, UserCheck } from 'lucide-react';
import { FilterButton } from '@/components/ui/ModuleHeader';
import MultiSelect from '@/components/ui/multi-select';
import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';
import { companyService } from '@/app/modules/admin/services/companyService';
import { professionsService } from '@/app/modules/admin/services/professionsService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';

export const WorkingHoursFilters = ({ 
  filters = {}, 
  onFiltersChange, 
  onSearch,
  selectedProviders,
  onProviderChange 
}) => {
  const [allProviders, setAllProviders] = useState([]);
  const [providers, setProviders] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [groups, setGroups] = useState([]);
  const [professions, setProfessions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [error, setError] = useState(null);
  
  // Estados para filtros temporários (antes de aplicar)
  const [tempFilters, setTempFilters] = useState(filters);
  const [appliedFilters, setAppliedFilters] = useState(filters);

  const { user } = useAuth();
  const { toast_error } = useToast();
  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  // Sincronizar filtros temporários quando filtros externos mudarem
  useEffect(() => {
    setTempFilters(filters);
    setAppliedFilters(filters);
  }, [filters]);

  useEffect(() => {
    const loadFilterData = async () => {
      try {
        setError(null);
        setIsLoading(true);

        // Carregar todas as opções necessárias
        const [
          providersData,
          companiesData,
          groupsData,
          professionsData
        ] = await Promise.all([
          appointmentService.getProviders({}),
          isSystemAdmin ? companyService.getCompanies() : Promise.resolve([]), // Usar getCompanies em vez de getCompaniesForSelect
          professionsService.getProfessionGroups(),
          professionsService.getProfessions()
        ]);

        // Formatar profissionais
        const formattedProviders = (providersData || [])
          .filter(p => p && p.id && p.fullName)
          .map(provider => ({
            value: provider.id,
            label: provider.fullName,
            companyId: provider.companyId || provider.company?.id,
            professionId: provider.professionId || provider.profession?.id,
            professionObj: provider.professionObj || provider.profession
          }));
        setAllProviders(formattedProviders);
        setProviders(formattedProviders);

        // Formatar empresas
        if (isSystemAdmin) {
          // Acessar companies do response se existir, senão usar directamente
          const companiesArray = companiesData?.companies || companiesData || [];
          const formattedCompanies = companiesArray
            .filter(c => c && c.id && c.name)
            .map(company => ({
              value: company.id,
              label: company.name
            }));
          setCompanies(formattedCompanies);
        }

        // Formatar grupos
        const formattedGroups = (groupsData || [])
          .filter(g => g && g.id && g.name)
          .map(group => ({
            value: group.id,
            label: group.name
          }));
        setGroups(formattedGroups);

        // Formatar profissões
        const formattedProfessions = (professionsData || [])
          .filter(p => p && p.id && p.name)
          .map(profession => ({
            value: profession.id,
            label: profession.name
          }));
        setProfessions(formattedProfessions);

      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        setError('Falha ao carregar opções. Por favor, tente novamente.');
        toast_error({
          title: "Erro ao carregar filtros",
          message: error.message || "Ocorreu um erro ao carregar os filtros"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadFilterData();
  }, [isSystemAdmin]);

  // Aplicar filtros apenas quando o usuário clicar no botão buscar
  useEffect(() => {
    let filtered = allProviders;
    
    // Aplicar filtro de busca por texto
    if (appliedFilters.search && appliedFilters.search.trim()) {
      const searchTerm = appliedFilters.search.toLowerCase().trim();
      filtered = filtered.filter(p => 
        p.label.toLowerCase().includes(searchTerm)
      );
    }
    
    // Filtro por empresa (apenas System Admin)
    if (isSystemAdmin && appliedFilters.companies?.length > 0) {
      filtered = filtered.filter(p => 
        p.companyId && appliedFilters.companies.includes(p.companyId)
      );
    }
    
    // Filtro por grupo de profissão
    if (appliedFilters.groups?.length > 0) {
      filtered = filtered.filter(p => {
        const groupId = p.professionObj?.groupId || p.professionObj?.group?.id;
        return groupId && appliedFilters.groups.includes(groupId);
      });
    }
    
    // Filtro por profissão
    if (appliedFilters.professions?.length > 0) {
      filtered = filtered.filter(p => 
        p.professionId && appliedFilters.professions.includes(p.professionId)
      );
    }

    setProviders(filtered);
    
    // Se algum profissional selecionado não estiver mais na lista filtrada, removê-lo
    if (selectedProviders.length > 0) {
      const validSelectedProviders = selectedProviders.filter(sel => 
        filtered.some(p => p.value === sel.value)
      );
      if (validSelectedProviders.length !== selectedProviders.length) {
        onProviderChange(validSelectedProviders);
      }
    }
  }, [allProviders, appliedFilters, isSystemAdmin]);

  const handleFilterChange = useCallback((newFilters) => {
    setTempFilters(newFilters);
    // Não aplicar filtros automaticamente, apenas salvar temporariamente
  }, []);

  const handleSearch = useCallback(() => {
    // Aplicar os filtros temporários
    setAppliedFilters(tempFilters);
    onFiltersChange(tempFilters);
    onSearch(tempFilters);
  }, [onSearch, tempFilters, onFiltersChange]);

  const handleClearFilters = useCallback(() => {
    const clearedFilters = {
      search: "",
      companies: [],
      groups: [],
      professions: []
    };
    setTempFilters(clearedFilters);
    setAppliedFilters(clearedFilters);
    onFiltersChange(clearedFilters);
    onProviderChange([]);
    onSearch(clearedFilters);
  }, [onFiltersChange, onProviderChange, onSearch]);

  const getActiveFiltersCount = useCallback(() => {
    return Object.entries(tempFilters).filter(([key, value]) => {
      if (key === 'search') return false;
      if (Array.isArray(value)) return value.length > 0;
      return value !== null && value !== '';
    }).length;
  }, [tempFilters]);

  const hasUnappliedChanges = useCallback(() => {
    return JSON.stringify(tempFilters) !== JSON.stringify(appliedFilters);
  }, [tempFilters, appliedFilters]);

  return (
    <div className="space-y-4">
      {error && (
        <div className="p-3 mb-4 bg-error-50 dark:bg-error-900/20 text-error-700 dark:text-error-300 rounded-lg border border-error-200 dark:border-error-700">
          {error}
        </div>
      )}

      {/* Barra de pesquisa e botões */}
      <div className="flex flex-col md:flex-row gap-3 md:items-center">
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Buscar profissionais..."
              value={tempFilters.search || ""}
              onChange={(e) => handleFilterChange({ ...tempFilters, search: e.target.value })}
              className="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <FilterButton
            type="button"
            onClick={() => setIsFilterExpanded(!isFilterExpanded)}
            moduleColor="scheduler"
            variant="secondary"
          >
            <div className="flex items-center gap-2">
              <Filter size={16} className="text-gray-600 dark:text-gray-400" />
              <span>Filtros</span>
              <span className="bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full text-xs">
                {getActiveFiltersCount()}
              </span>
            </div>
          </FilterButton>

          <FilterButton
            type="button"
            onClick={handleSearch}
            moduleColor="scheduler"
            variant="primary"
            disabled={isLoading}
            className={hasUnappliedChanges() ? "relative" : ""}
          >
            <div className="flex items-center gap-2">
              <Search size={16} />
              <span>{isLoading ? "Buscando..." : "Buscar"}</span>
              {hasUnappliedChanges() && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              )}
            </div>
          </FilterButton>
        </div>
      </div>

      {/* Aviso sobre filtros não aplicados */}
      {hasUnappliedChanges() && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3">
          <div className="flex items-center gap-2 text-yellow-800 dark:text-yellow-200 text-sm">
            <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
            Você fez alterações nos filtros. Clique em "Buscar" para aplicá-las.
          </div>
        </div>
      )}

      {/* Filtros avançados (expansíveis) */}
      {isFilterExpanded && (
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
          <div className={`grid grid-cols-1 gap-4 ${isSystemAdmin ? 'md:grid-cols-2 lg:grid-cols-4' : 'md:grid-cols-3'}`}>
            {isSystemAdmin && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                  <Building size={16} className="text-purple-600 dark:text-purple-400" />
                  Empresas
                </label>
                <MultiSelect
                  options={companies}
                  value={tempFilters.companies || []}
                  onChange={(selected) => handleFilterChange({ ...tempFilters, companies: selected })}
                  placeholder="Selecione as empresas"
                  className="w-full"
                  moduleOverride="scheduler"
                  loading={isLoading}
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                <Users size={16} className="text-purple-600 dark:text-purple-400" />
                Grupos
              </label>
              <MultiSelect
                options={groups}
                value={tempFilters.groups || []}
                onChange={(selected) => handleFilterChange({ ...tempFilters, groups: selected })}
                placeholder="Selecione..."
                className="w-full"
                moduleOverride="scheduler"
                loading={isLoading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                <Briefcase size={16} className="text-purple-600 dark:text-purple-400" />
                Profissões
              </label>
              <MultiSelect
                options={professions}
                value={tempFilters.professions || []}
                onChange={(selected) => handleFilterChange({ ...tempFilters, professions: selected })}
                placeholder="Selecione..."
                className="w-full"
                moduleOverride="scheduler"
                loading={isLoading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                <UserCheck size={16} className="text-purple-600 dark:text-purple-400" />
                Profissionais
              </label>
              <MultiSelect
                value={selectedProviders}
                onChange={onProviderChange}
                options={providers}
                placeholder="Selecione..."
                loading={isLoading}
                moduleOverride="scheduler"
                className="w-full"
              />
            </div>
          </div>

          <div>
            <FilterButton
              type="button"
              onClick={handleClearFilters}
              moduleColor="scheduler"
              variant="secondary"
            >
              <div className="flex items-center gap-2">
                <RefreshCw size={16} />
                <span>Limpar Filtros</span>
              </div>
            </FilterButton>
          </div>
        </div>
      )}


    </div>
  );
};
