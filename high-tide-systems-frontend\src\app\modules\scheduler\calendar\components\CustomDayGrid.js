import React, { useState } from 'react';
import EventTooltip from './EventTooltip';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Grid, Clock, Eye, Download } from 'lucide-react';
import { getEventBackgroundColor, getEventTextColor, getEventStatusLabel } from '../utils/eventColors';
import '../styles/vibrant-calendar.css';
import MultipleEventsModal from './MultipleEventsModal';

const diasSemana = ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'];
const meses = [
  'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Mai<PERSON>', 'Jun<PERSON>',
  'Jul<PERSON>', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
];

function addDays(date, days) {
  const d = new Date(date);
  d.setDate(d.getDate() + days);
  return d;
}

function getHoursArray(start, end) {
  const arr = [];
  for (let h = start; h <= end; h++) arr.push(h);
  return arr;
}

function formatHour(hour) {
  return `${hour.toString().padStart(2, '0')}:00`;
}

export default function CustomDayGrid({ events, startHour, endHour, selectedDate, onNavigate, onViewChange, onEventClick, isDarkMode = false, onCreateAppointment, preferences, allowedHours, onExport, isExporting, isClient = false }) {
  selectedDate = new Date(selectedDate);
  if (isNaN(selectedDate)) selectedDate = new Date();
  const [modalEvents, setModalEvents] = useState(null);
  
  // Filtrar horários baseado nas preferências
  const filteredHours = getHoursArray(startHour, endHour).filter(hour => {
    return allowedHours && allowedHours[hour] !== false;
  });
  
  // Agrupar eventos por hora
  const eventsByHour = {};
  events.forEach(ev => {
    const start = new Date(ev.startDate || ev.start);
    const dayKey = start.toISOString().slice(0, 10); // yyyy-mm-dd
    const hour = start.getHours();
    const key = `${dayKey}-${hour}`;
    if (!eventsByHour[key]) eventsByHour[key] = [];
    eventsByHour[key].push(ev);
  });

  const dayKey = selectedDate.toISOString().slice(0, 10);
  const dayName = diasSemana[selectedDate.getDay()];
  const monthName = meses[selectedDate.getMonth()];
  const year = selectedDate.getFullYear();
  const dayNumber = selectedDate.getDate();

  const hours = getHoursArray(startHour, endHour);

  return (
    <div className="modern-calendar-container p-4">
      {/* Toolbar customizada */}
      <div className="modern-calendar-toolbar mb-4">
        <div className="toolbar-left">
          <button onClick={() => onNavigate && onNavigate('TODAY')} className="today-btn">
            <CalendarIcon size={16} />
            <span>Hoje</span>
          </button>
          <div className="navigation-buttons">
            <button onClick={() => onNavigate && onNavigate('PREV')} className="nav-btn">
              <ChevronLeft size={18} />
            </button>
            <button onClick={() => onNavigate && onNavigate('NEXT')} className="nav-btn">
              <ChevronRight size={18} />
            </button>
          </div>
          <div className="current-period">
            <h2 className="period-title">Dia</h2>
            <p className="period-date">{dayName}, {dayNumber} de {monthName} de {year}</p>
          </div>
        </div>

        <div className="toolbar-right">
          <div className="view-buttons">
            <button
              className="view-btn"
              onClick={() => onViewChange && onViewChange('month')}
            >
              <Grid size={16} />
              <span>Mês</span>
            </button>
            <button
              className="view-btn"
              onClick={() => onViewChange && onViewChange('week')}
            >
              <Clock size={16} />
              <span>Semana</span>
            </button>
            <button
              className="view-btn active"
              onClick={() => onViewChange && onViewChange('day')}
            >
              <Eye size={16} />
              <span>Dia</span>
            </button>
          </div>
          
          {/* Botão de exportar */}
          {onExport && (
            <div className="export-section ml-4">
              <button
                onClick={() => onExport('image')}
                disabled={isExporting || events.length === 0}
                className="export-btn"
                title="Exportar calendário como imagem"
              >
                <Download size={16} />
                <span>Exportar</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Grid do dia */}
      <div className="grid border border-purple-200 dark:border-[#374151] rounded-lg overflow-hidden" style={{ gridTemplateColumns: '80px 1fr' }}>
        {/* Header */}
        <div className="bg-purple-100 dark:bg-[#1a1d2a] text-purple-800 dark:text-[#a78bfa] font-bold p-3 border-b border-r border-purple-200 dark:border-[#374151]">Horário</div>
        <div className="bg-purple-100 dark:bg-[#1a1d2a] text-purple-800 dark:text-[#a78bfa] font-bold p-3 border-b border-r border-purple-200 dark:border-[#374151]">Agendamentos</div>
        
        {/* Horários */}
        {hours.map(hour => {
          const key = `${dayKey}-${hour}`;
          const slotEvents = eventsByHour[key] || [];
          const showEvents = slotEvents.slice(0, 3);
          const extraCount = slotEvents.length - 3;
          
          return (
            <React.Fragment key={hour}>
              {/* Coluna de horários */}
              <div className={`font-bold p-2 border-b border-r border-purple-200 dark:border-[#374151] text-center ${
                allowedHours && allowedHours[hour] === false 
                  ? 'bg-gray-100 dark:bg-[#1a1d2a] text-gray-400 dark:text-[#6b7280] opacity-50' 
                  : 'bg-white dark:bg-[#23273a] text-purple-700 dark:text-[#a78bfa]'
              }`}>
                {formatHour(hour)}
              </div>
              
              {/* Coluna de eventos */}
              <div
                className={`border-b border-r border-purple-200 dark:border-[#374151] p-3 ${
                  allowedHours && allowedHours[hour] === false 
                    ? 'bg-gray-100 dark:bg-[#1a1d2a] opacity-50 cursor-not-allowed' 
                    : 'bg-white dark:bg-[#23273a] cursor-pointer hover:bg-purple-50 dark:hover:bg-gray-800'
                }`}
                style={{ cursor: allowedHours && allowedHours[hour] === false ? 'not-allowed' : 'pointer' }}
                onClick={() => {
                  if (allowedHours && allowedHours[hour] === false) return;
                  if (isClient) return;
                  
                  const start = new Date(selectedDate);
                  start.setHours(hour, 0, 0, 0);
                  const end = new Date(start);
                  end.setHours(hour + 1, 0, 0, 0);
                  console.log('🟢 [CustomDayGrid] Slot (célula) clicado:', { start, end, dia: dayKey, hora: hour });
                  if (typeof onCreateAppointment === 'function') {
                    onCreateAppointment({ start, end, dia: dayKey, hora: hour });
                  }
                }}
              >
                <div>
                  <div className="flex flex-row gap-2">
                    {showEvents.map((ev, idx) => {
                      const eventStatus = ev.extendedProps?.status || ev.status || 'PENDING';
                      return (
                        <EventTooltip key={ev.id} content={ev.title || ev.personfullName}>
                          <div 
                            className="rounded-lg px-3 py-2 font-bold text-sm shadow-md cursor-pointer hover:opacity-80 transition"
                            style={{ 
                              background: getEventBackgroundColor(eventStatus, isDarkMode),
                              color: getEventTextColor(eventStatus, isDarkMode)
                            }}
                            onClick={e => {
                              e.stopPropagation();
                              if (isClient) return;
                              console.log('[CustomDayGrid] Evento clicado:', ev);
                              onEventClick && onEventClick({ event: ev });
                            }}
                          >
                            <div className="font-bold">{ev.title || ev.personfullName}</div>
                            <div className="text-xs opacity-90">{ev.providerfullName}</div>
                            <div className="text-xs opacity-70">
                              {new Date(ev.start).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })} - 
                              {new Date(ev.end).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
                            </div>
                          </div>
                        </EventTooltip>
                      );
                    })}
                  </div>
                  {extraCount > 0 && (
                    <div className="mt-3">
                      <button 
                        className="w-full rounded-lg bg-[#7c3aed] text-white font-bold text-sm py-2 px-3 hover:bg-[#a78bfa] transition"
                        onClick={e => { e.stopPropagation(); setModalEvents(slotEvents); }}
                      >
                        +{extraCount} mais agendamentos
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </React.Fragment>
          );
        })}
      </div>

      {/* Modal de eventos extras */}
      <MultipleEventsModal
        isOpen={!!modalEvents}
        onClose={() => setModalEvents(null)}
        events={modalEvents || []}
        title="Agendamentos do horário"
        onEventClick={onEventClick}
        isDarkMode={isDarkMode}
      />
    </div>
  );
} 