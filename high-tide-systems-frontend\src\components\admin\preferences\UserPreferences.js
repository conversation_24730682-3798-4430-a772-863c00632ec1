import { useState, useEffect } from "react";
import { Card } from "../../ui/Card";
import Button from "../../ui/Button";
import ModuleCheckbox from "../../ui/ModuleCheckbox";
import PreferencesSection from "./PreferencesSection";
import { preferencesService } from "@/services/preferencesService";
import { useToast } from "@/contexts/ToastContext";

const USER_FIELDS = [
  { key: "userBirthDate", label: "Data de Nascimento" },
  { key: "userCep", label: "CEP" },
  { key: "userCpfCnpj", label: "CPF/CNPJ" },
  { key: "userPhone", label: "Telefone" },
  { key: "userProfilePhoto", label: "Foto de perfil" },
  { key: "userRole", label: "Perfil de acesso" },
  { key: "userUnit", label: "Unidade" },
];

const CLIENT_FIELDS = [
  { key: "clientCep", label: "CEP" },
  { key: "clientCpfCnpj", label: "CPF/CNPJ" },
  { key: "clientPhone", label: "Telefone" },
];

const PATIENT_FIELDS = [
  { key: "patientAssociateClient", label: "Associar a Cliente" },
  { key: "patientBirthDate", label: "Data de Nascimento" },
  { key: "patientCep", label: "CEP" },
  { key: "patientCpfCnpj", label: "CPF" },
  { key: "patientEmail", label: "E-mail" },
  { key: "patientGender", label: "Gênero" },
  { key: "patientObservation", label: "Observação" },
  { key: "patientPhone", label: "Telefone" },
];

// Dados sensíveis específicos por entidade baseados no schema.prisma
const USER_PRIVACY_FIELDS = [
  { key: "hideUserCpf", label: "CPF", description: "Oculta ou mascara CPF dos usuários" },
  { key: "hideUserCnpj", label: "CNPJ", description: "Oculta ou mascara CNPJ dos usuários" },
  { key: "hideUserEmail", label: "E-mail", description: "Oculta ou mascara e-mails dos usuários" },
  { key: "hideUserPhone", label: "Telefone", description: "Oculta ou mascara telefones dos usuários" },
  { key: "hideUserAddress", label: "Endereço Completo", description: "Oculta endereços completos dos usuários" },
  { key: "hideUserBirthDate", label: "Data de Nascimento", description: "Oculta datas de nascimento dos usuários" },
  { key: "hideUserLastLoginIp", label: "IP do Último Login", description: "Oculta IPs de login dos usuários" },
];

const CLIENT_PRIVACY_FIELDS = [
  { key: "hideClientEmail", label: "E-mail", description: "Oculta ou mascara e-mails dos clientes" },
  { key: "hideClientFullName", label: "Nome Completo", description: "Oculta ou mascara nomes completos dos clientes" },
];

const PATIENT_PRIVACY_FIELDS = [
  { key: "hidePatientCpf", label: "CPF", description: "Oculta ou mascara CPF dos pacientes" },
  { key: "hidePatientEmail", label: "E-mail", description: "Oculta ou mascara e-mails dos pacientes" },
  { key: "hidePatientPhone", label: "Telefone", description: "Oculta ou mascara telefones dos pacientes" },
  { key: "hidePatientAddress", label: "Endereço Completo", description: "Oculta endereços completos dos pacientes" },
  { key: "hidePatientBirthDate", label: "Data de Nascimento", description: "Oculta datas de nascimento dos pacientes" },
  { key: "hidePatientNotes", label: "Observações", description: "Oculta observações e notas dos pacientes" },
  { key: "hidePatientProfileImage", label: "Foto de Perfil", description: "Oculta fotos de perfil dos pacientes" },
];

export default function UserPreferences({ 
  search = "", 
  searchMode = false, 
  preferences = null, 
  selectedCompanyId = null,
  onSave = null 
}) {
  // Usuário
  const [userFields, setUserFields] = useState({
    userCpfCnpj: true,
    userUnit: false,
    userBirthDate: true,
    userPhone: true,
    userCep: true,
    userRole: true,
    userProfilePhoto: false,
  });
  const [requireUser2FA, setRequireUser2FA] = useState(false);

  // Cliente
  const [clientFields, setClientFields] = useState({
    clientCpfCnpj: true,
    clientCep: true,
    clientPhone: true,
  });

  // Paciente
  const [patientFields, setPatientFields] = useState({
    patientCpfCnpj: true,
    patientBirthDate: true,
    patientGender: false,
    patientEmail: false,
    patientPhone: false,
    patientCep: false,
    patientAssociateClient: true,
    patientObservation: false,
  });

  // Privacidade e Dados Sensíveis - Usuários
  const [userPrivacyFields, setUserPrivacyFields] = useState({
    hideUserCpf: false,
    hideUserCnpj: false,
    hideUserEmail: false,
    hideUserPhone: false,
    hideUserAddress: false,
    hideUserBirthDate: false,
    hideUserLastLoginIp: false,
  });

  // Privacidade e Dados Sensíveis - Clientes
  const [clientPrivacyFields, setClientPrivacyFields] = useState({
    hideClientEmail: false,
    hideClientFullName: false,
  });

  // Privacidade e Dados Sensíveis - Pacientes
  const [patientPrivacyFields, setPatientPrivacyFields] = useState({
    hidePatientCpf: false,
    hidePatientEmail: false,
    hidePatientPhone: false,
    hidePatientAddress: false,
    hidePatientBirthDate: false,
    hidePatientNotes: false,
    hidePatientProfileImage: false,
  });

  const { toast_success, toast_error } = useToast();
  const [isSaving, setIsSaving] = useState(false);

  // Carregar preferências quando o componente receber os dados
  useEffect(() => {
    if (preferences) {
      // Carregar preferências de usuário
      if (preferences.user) {
        setUserFields({
          userCpfCnpj: preferences.user.userCpfCnpj ?? true,
          userUnit: preferences.user.userUnit ?? false,
          userBirthDate: preferences.user.userBirthDate ?? true,
          userPhone: preferences.user.userPhone ?? true,
          userCep: preferences.user.userCep ?? true,
          userRole: preferences.user.userRole ?? true,
          userProfilePhoto: preferences.user.userProfilePhoto ?? false,
        });
        setRequireUser2FA(preferences.user.require2FA ?? false);
      }

      // Carregar preferências de cliente
      if (preferences.client) {
        setClientFields({
          clientCpfCnpj: preferences.client.clientCpfCnpj ?? true,
          clientCep: preferences.client.clientCep ?? true,
          clientPhone: preferences.client.clientPhone ?? true,
        });
      }

      // Carregar preferências de paciente
      if (preferences.patient) {
        setPatientFields({
          patientCpfCnpj: preferences.patient.patientCpfCnpj ?? true,
          patientBirthDate: preferences.patient.patientBirthDate ?? true,
          patientGender: preferences.patient.patientGender ?? false,
          patientEmail: preferences.patient.patientEmail ?? false,
          patientPhone: preferences.patient.patientPhone ?? false,
          patientCep: preferences.patient.patientCep ?? false,
          patientAssociateClient: preferences.patient.patientAssociateClient ?? true,
          patientObservation: preferences.patient.patientObservation ?? false,
        });
      }

      // Carregar preferências de privacidade - Usuários
      if (preferences.userPrivacy) {
        setUserPrivacyFields({
          hideUserCpf: preferences.userPrivacy.hideUserCpf ?? false,
          hideUserCnpj: preferences.userPrivacy.hideUserCnpj ?? false,
          hideUserEmail: preferences.userPrivacy.hideUserEmail ?? false,
          hideUserPhone: preferences.userPrivacy.hideUserPhone ?? false,
          hideUserAddress: preferences.userPrivacy.hideUserAddress ?? false,
          hideUserBirthDate: preferences.userPrivacy.hideUserBirthDate ?? false,
          hideUserLastLoginIp: preferences.userPrivacy.hideUserLastLoginIp ?? false,
        });
      }

      // Carregar preferências de privacidade - Clientes
      if (preferences.clientPrivacy) {
        setClientPrivacyFields({
          hideClientEmail: preferences.clientPrivacy.hideClientEmail ?? false,
          hideClientFullName: preferences.clientPrivacy.hideClientFullName ?? false,
        });
      }

      // Carregar preferências de privacidade - Pacientes
      if (preferences.patientPrivacy) {
        setPatientPrivacyFields({
          hidePatientCpf: preferences.patientPrivacy.hidePatientCpf ?? false,
          hidePatientEmail: preferences.patientPrivacy.hidePatientEmail ?? false,
          hidePatientPhone: preferences.patientPrivacy.hidePatientPhone ?? false,
          hidePatientAddress: preferences.patientPrivacy.hidePatientAddress ?? false,
          hidePatientBirthDate: preferences.patientPrivacy.hidePatientBirthDate ?? false,
          hidePatientNotes: preferences.patientPrivacy.hidePatientNotes ?? false,
          hidePatientProfileImage: preferences.patientPrivacy.hidePatientProfileImage ?? false,
        });
      }
    }
  }, [preferences]);

  function handleUserFieldChange(key) {
    setUserFields(fields => ({ ...fields, [key]: !fields[key] }));
  }
  function handleClientFieldChange(key) {
    setClientFields(fields => ({ ...fields, [key]: !fields[key] }));
  }
  function handlePatientFieldChange(key) {
    setPatientFields(fields => ({ ...fields, [key]: !fields[key] }));
  }
  function handleUserPrivacyFieldChange(key) {
    setUserPrivacyFields(fields => ({ ...fields, [key]: !fields[key] }));
  }
  function handleClientPrivacyFieldChange(key) {
    setClientPrivacyFields(fields => ({ ...fields, [key]: !fields[key] }));
  }
  function handlePatientPrivacyFieldChange(key) {
    setPatientPrivacyFields(fields => ({ ...fields, [key]: !fields[key] }));
  }

  // Função para filtrar campos/seções pelo termo de busca
  const filter = (label) => label.toLowerCase().includes(search.toLowerCase());

  // Filtragem de campos
  const userFieldsFiltrados = USER_FIELDS.filter(campo => filter(campo.label)).sort((a, b) => a.label.localeCompare(b.label));
  const clientFieldsFiltrados = CLIENT_FIELDS.filter(campo => filter(campo.label)).sort((a, b) => a.label.localeCompare(b.label));
  const patientFieldsFiltrados = PATIENT_FIELDS.filter(campo => filter(campo.label)).sort((a, b) => a.label.localeCompare(b.label));
  const userPrivacyFieldsFiltrados = USER_PRIVACY_FIELDS.filter(campo => filter(campo.label) || filter(campo.description)).sort((a, b) => a.label.localeCompare(b.label));
  const clientPrivacyFieldsFiltrados = CLIENT_PRIVACY_FIELDS.filter(campo => filter(campo.label) || filter(campo.description)).sort((a, b) => a.label.localeCompare(b.label));
  const patientPrivacyFieldsFiltrados = PATIENT_PRIVACY_FIELDS.filter(campo => filter(campo.label) || filter(campo.description)).sort((a, b) => a.label.localeCompare(b.label));
  
  const showUser = !searchMode || userFieldsFiltrados.length > 0 || filter("2fa") || filter("autenticação");
  const showClient = !searchMode || clientFieldsFiltrados.length > 0 || filter("confirmação") || filter("documento") || filter("endereço");
  const showPatient = !searchMode || patientFieldsFiltrados.length > 0 || filter("paciente");
  const showUserPrivacy = !searchMode || userPrivacyFieldsFiltrados.length > 0 || filter("usuário") || filter("privacidade") || filter("esconder") || filter("ocultar") || filter("sensível") || filter("máscara");
  const showClientPrivacy = !searchMode || clientPrivacyFieldsFiltrados.length > 0 || filter("cliente") || filter("privacidade") || filter("esconder") || filter("ocultar") || filter("sensível") || filter("máscara");
  const showPatientPrivacy = !searchMode || patientPrivacyFieldsFiltrados.length > 0 || filter("paciente") || filter("privacidade") || filter("esconder") || filter("ocultar") || filter("sensível") || filter("máscara");

  // Filtragem dos checkboxes de confirmação/endereço
  const confirmEmailMatch = filter("confirmação de e-mail");
  const confirmPhoneMatch = filter("confirmação de telefone");
  const multiDocMatch = filter("múltiplos documentos") || filter("CPF") || filter("CNPJ");
  const endResidMatch = filter("endereço residencial");
  const endComMatch = filter("endereço comercial");
  const user2FAMatch = filter("2fa") || filter("autenticação");

  async function handleSave() {
    setIsSaving(true);
    const preferencesToSave = {
      user: { ...userFields, require2FA: requireUser2FA },
      client: { ...clientFields },
      patient: { ...patientFields },
      userPrivacy: { ...userPrivacyFields },
      clientPrivacy: { ...clientPrivacyFields },
      patientPrivacy: { ...patientPrivacyFields }
    };
    
    try {
      if (onSave) {
        // Usar função de salvamento fornecida pelo pai (suporta empresas específicas)
        await onSave(preferencesToSave);
      } else {
        // Fallback para o serviço tradicional
        await preferencesService.save(preferencesToSave);
        toast_success("Preferências salvas com sucesso!");
      }
    } catch (err) {
      console.error('Erro ao salvar preferências:', err);
      toast_error("Erro ao salvar preferências.");
    } finally {
      setIsSaving(false);
    }
  }

  if (searchMode && !showUser && !showClient && !showPatient && !showUserPrivacy && !showClientPrivacy && !showPatientPrivacy) {
    return null;
  }

  return (
    <div className="space-y-8">
      {showUser && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Preferências de Usuário</h4>
          <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">Ao marcar uma opção abaixo, o campo se tornará <span className="font-semibold text-primary-600 dark:text-primary-400">obrigatório</span> para cadastro do usuário. Os campos <b>Nome completo</b>, <b>E-mail</b>, <b>Login</b> e <b>Senha</b> são sempre obrigatórios.</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {userFieldsFiltrados.map(campo => (
              <ModuleCheckbox
                key={campo.key}
                checked={userFields[campo.key]}
                onChange={() => handleUserFieldChange(campo.key)}
                label={campo.label}
                moduleColor="admin"
              />
            ))}
            {(!searchMode || user2FAMatch) && (
              <ModuleCheckbox
                checked={requireUser2FA}
                onChange={() => setRequireUser2FA(v => !v)}
                label="Exigir autenticação em dois fatores (2FA)"
                moduleColor="admin"
              />
            )}
          </div>
        </Card>
      )}
      {showClient && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Preferências de Cliente</h4>
          <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">Ao marcar uma opção abaixo, o campo se tornará <span className="font-semibold text-primary-600 dark:text-primary-400">obrigatório</span> para cadastro do cliente. Os campos <b>Nome completo</b>, <b>E-mail</b>, <b>Login</b> e <b>Senha</b> são sempre obrigatórios.</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {clientFieldsFiltrados.map(campo => (
              <ModuleCheckbox
                key={campo.key}
                checked={clientFields[campo.key]}
                onChange={() => handleClientFieldChange(campo.key)}
                label={campo.label}
                moduleColor="admin"
              />
            ))}
          </div>
        </Card>
      )}
      {showPatient && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">Preferências de Paciente</h4>
          <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">Ao marcar uma opção abaixo, o campo se tornará <span className="font-semibold text-primary-600 dark:text-primary-400">obrigatório</span> para cadastro do paciente. O campo <b>Nome completo</b> é sempre obrigatório.</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {patientFieldsFiltrados.map(campo => (
              <ModuleCheckbox
                key={campo.key}
                checked={patientFields[campo.key]}
                onChange={() => handlePatientFieldChange(campo.key)}
                label={campo.label}
                moduleColor="admin"
              />
            ))}
          </div>
        </Card>
      )}
      {showUserPrivacy && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">🔒 Privacidade - Dados de Usuários</h4>
          <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">Configure quais dados sensíveis dos <span className="font-semibold text-blue-600 dark:text-blue-400">usuários</span> devem ser <span className="font-semibold text-red-600 dark:text-red-400">ocultados ou mascarados</span> em listagens, relatórios e interfaces do sistema.</p>
          <div className="grid grid-cols-1 gap-6">
            {userPrivacyFieldsFiltrados.map(campo => (
              <div key={campo.key} className="space-y-2">
                <ModuleCheckbox
                  checked={userPrivacyFields[campo.key]}
                  onChange={() => handleUserPrivacyFieldChange(campo.key)}
                  label={campo.label}
                  moduleColor="admin"
                />
                <p className="text-xs text-neutral-500 dark:text-neutral-400 ml-6">{campo.description}</p>
              </div>
            ))}
          </div>
        </Card>
      )}
      {showClientPrivacy && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">🔒 Privacidade - Dados de Clientes</h4>
          <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">Configure quais dados sensíveis dos <span className="font-semibold text-green-600 dark:text-green-400">clientes</span> devem ser <span className="font-semibold text-red-600 dark:text-red-400">ocultados ou mascarados</span> em listagens, relatórios e interfaces do sistema.</p>
          <div className="grid grid-cols-1 gap-6">
            {clientPrivacyFieldsFiltrados.map(campo => (
              <div key={campo.key} className="space-y-2">
                <ModuleCheckbox
                  checked={clientPrivacyFields[campo.key]}
                  onChange={() => handleClientPrivacyFieldChange(campo.key)}
                  label={campo.label}
                  moduleColor="admin"
                />
                <p className="text-xs text-neutral-500 dark:text-neutral-400 ml-6">{campo.description}</p>
              </div>
            ))}
          </div>
        </Card>
      )}
      {showPatientPrivacy && (
        <Card className="p-6 bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">🔒 Privacidade - Dados de Pacientes</h4>
          <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-6">Configure quais dados sensíveis dos <span className="font-semibold text-purple-600 dark:text-purple-400">pacientes</span> devem ser <span className="font-semibold text-red-600 dark:text-red-400">ocultados ou mascarados</span> em listagens, relatórios e interfaces do sistema.</p>
          <div className="grid grid-cols-1 gap-6">
            {patientPrivacyFieldsFiltrados.map(campo => (
              <div key={campo.key} className="space-y-2">
                <ModuleCheckbox
                  checked={patientPrivacyFields[campo.key]}
                  onChange={() => handlePatientPrivacyFieldChange(campo.key)}
                  label={campo.label}
                  moduleColor="admin"
                />
                <p className="text-xs text-neutral-500 dark:text-neutral-400 ml-6">{campo.description}</p>
              </div>
            ))}
          </div>
        </Card>
      )}
      {(showUserPrivacy || showClientPrivacy || showPatientPrivacy) && (
        <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 rounded-lg">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 w-5 h-5 text-amber-600 dark:text-amber-400">
              ⚠️
            </div>
            <div>
              <h5 className="text-sm font-medium text-amber-800 dark:text-amber-200 mb-1">Aviso Importante</h5>
              <p className="text-xs text-amber-700 dark:text-amber-300">
                As configurações de privacidade afetam apenas a <strong>visualização</strong> dos dados. 
                Os dados originais permanecem intactos no banco de dados e podem ser acessados por usuários com permissões adequadas.
              </p>
            </div>
          </div>
        </div>
      )}
      <div className="flex justify-end mt-8">
        <button
          className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md hover:shadow-lg"
          onClick={handleSave}
          disabled={isSaving}
        >
          <span>Salvar Preferências</span>
          <span className="text-lg">✔</span>
        </button>
      </div>
    </div>
  );
} 