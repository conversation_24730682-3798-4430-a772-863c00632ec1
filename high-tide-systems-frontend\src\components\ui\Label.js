'use client';

import React from 'react';

const Label = React.forwardRef(({ 
  className = '', 
  children, 
  htmlFor,
  required = false,
  ...props 
}, ref) => {
  return (
    <label
      className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-700 dark:text-gray-300 border-l-2 border-gray-300 dark:border-gray-600 pl-2 ${className}`}
      htmlFor={htmlFor}
      ref={ref}
      {...props}
    >
      {children}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
  );
});

Label.displayName = 'Label';

export default Label; 