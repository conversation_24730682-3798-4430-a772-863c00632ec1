const express = require('express');
const { NotificationPermissionController } = require('../controllers/notificationPermissionController');
const { authenticate } = require('../middlewares/auth');
const checkRole = require('../middlewares/roleCheck');

const router = express.Router();

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticate);

// Rotas para gerenciar permissões de notificação
router.get('/available', NotificationPermissionController.getAvailableNotificationPermissions);
router.get('/companies', checkRole(['SYSTEM_ADMIN']), NotificationPermissionController.getAvailableCompanies);
router.get('/user/:userId', NotificationPermissionController.getUserNotificationPermissions);
router.put('/user/:userId', checkRole(['SYSTEM_ADMIN', 'COMPANY_ADMIN']), NotificationPermissionController.updateUserNotificationPermissions);
router.delete('/user/:userId', checkRole(['SYSTEM_ADMIN', 'COMPANY_ADMIN']), NotificationPermissionController.removeAllNotificationPermissions);

module.exports = router;
