"use client";

import React, { useState, useEffect } from "react";
import {
  Users,
  Briefcase,
  Settings,
  FileText,
  Database,
  LayoutDashboard,
  Info,
  ShieldIcon,
  Building,
  Clock,
  Play,
  Pause,
  ChevronRight,
  ArrowRight,
  BarChart4,
  PieChart,
  LineChart,
  Activity,
  Plus,
  Lock,
  Mail,
  Edit,
  Trash,
  Power,
  RefreshCw,
  CheckCircle,
  XCircle,
  Shield,
  Send,
  Download,
  SlidersHorizontal
} from "lucide-react";
import { ModuleHeader } from "@/components/ui";

const IntroductionPage = () => {
  const [selectedTutorial, setSelectedTutorial] = useState(null);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [totalSlides, setTotalSlides] = useState(8);

  // Configuração dos tutoriais disponíveis
  const tutorials = [
    {
      id: 'users',
      title: 'Gerenciamento de Usuários',
      description: 'Aprenda a criar, editar, gerenciar permissões e excluir usuários do sistema',
      icon: <Users size={20} />,
      color: 'from-blue-600 to-blue-500',
      darkColor: 'from-blue-700 to-blue-600',
      slides: 8,
      features: [
        'Visualizar lista',
        'Criar usuários',
        'Editar perfis',
        'Gerenciar permissões',
        'Desativar/Excluir'
      ]
    },
    {
      id: 'professions',
      title: 'Gerenciamento de Profissões',
      description: 'Aprenda a criar, organizar e gerenciar profissões e grupos profissionais',
      icon: <Briefcase size={20} />,
      color: 'from-green-600 to-green-500',
      darkColor: 'from-green-700 to-green-600',
      slides: 8,
      features: [
        'Visualizar profissões',
        'Criar profissões',
        'Gerenciar grupos',
        'Associar usuários',
        'Filtrar e buscar'
      ]
    },
    {
      id: 'settings',
      title: 'Configurações do Sistema',
      description: 'Domine todas as configurações: geral, empresas, email, backup, segurança e preferências',
      icon: <Settings size={20} />,
      color: 'from-purple-600 to-purple-500',
      darkColor: 'from-purple-700 to-purple-600',
      slides: 8,
      features: [
        'Configurações gerais',
        'Gerenciar empresas',
        'Configurar email',
        'Backup e segurança',
        'Personalizar sistema'
      ]
    }
  ];

  // Auto-advance slides
  useEffect(() => {
    if (!isVideoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % totalSlides);
    }, 7000);
    
    return () => clearInterval(interval);
  }, [isVideoPlaying, totalSlides]);

  // Atualizar total de slides quando tutorial mudar
  useEffect(() => {
    if (selectedTutorial) {
      setTotalSlides(selectedTutorial.slides);
      setCurrentSlide(0);
    }
  }, [selectedTutorial]);

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Info size={24} className="mr-2 text-slate-600 dark:text-slate-400" />
          Introdução
        </h1>
      </div>

      {/* Main content */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-admin-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-slate-600 to-slate-400 dark:from-slate-700 dark:to-slate-600 px-6 py-4">
          <div className="flex items-center">
            <ShieldIcon className="mr-3 text-white" size={24} aria-hidden="true" />
            <h2 className="text-xl font-bold text-white">Módulo de Administração</h2>
          </div>
        </div>

        {/* Introduction text and video */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div>
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                Bem-vindo ao Módulo de Administração do High Tide Systems. Este módulo é o centro de controle do sistema,
                permitindo gerenciar usuários, configurações, profissões, logs e realizar backups.
                Aqui você encontrará todas as ferramentas necessárias para administrar o sistema de forma eficiente.
              </p>
            </div>

            {/* Lista de Tutoriais */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-lg">
              <div className="bg-gradient-to-r from-slate-600 to-slate-500 dark:from-slate-700 dark:to-slate-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-white flex items-center">
                    <Play className="mr-2" size={18} />
                    {selectedTutorial ? `Tutorial: ${selectedTutorial.title}` : 'Tutoriais Disponíveis'}
                  </h3>
                  {selectedTutorial && (
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => {
                          setSelectedTutorial(null);
                          setIsVideoPlaying(false);
                        }}
                        className="text-white hover:text-slate-200 transition-colors p-1 rounded"
                        title="Voltar à lista"
                      >
                        <ArrowRight size={18} className="rotate-180" />
                      </button>
                      <button
                        onClick={() => setIsVideoPlaying(!isVideoPlaying)}
                        className="text-white hover:text-slate-200 transition-colors p-1 rounded"
                        aria-label={isVideoPlaying ? "Pausar tutorial" : "Iniciar tutorial"}
                      >
                        {isVideoPlaying ? <Pause size={18} /> : <Play size={18} />}
                      </button>
                    </div>
                  )}
                </div>
              </div>
              <div className="relative aspect-video bg-gradient-to-br from-slate-900 to-slate-800 overflow-hidden">
                {!selectedTutorial ? (
                  // Lista de tutoriais
                  <div className="p-6 h-full overflow-y-auto">
                    <div className="grid grid-cols-1 gap-4 h-full">
                      {tutorials.map((tutorial) => (
                        <div
                          key={tutorial.id}
                          onClick={() => setSelectedTutorial(tutorial)}
                          className="bg-white/10 backdrop-blur-sm rounded-lg p-4 cursor-pointer hover:bg-white/20 transition-all duration-200 border border-white/20 hover:border-white/30"
                        >
                          <div className="flex items-start gap-3">
                            <div className={`p-2 rounded-lg bg-gradient-to-r ${tutorial.color} dark:${tutorial.darkColor}`}>
                              {tutorial.icon}
                            </div>
                            <div className="flex-1">
                              <h4 className="text-white font-semibold text-sm mb-1">{tutorial.title}</h4>
                              <p className="text-blue-200 text-xs mb-3">{tutorial.description}</p>
                              <div className="grid grid-cols-2 gap-1">
                                {tutorial.features.map((feature, index) => (
                                  <div key={index} className="flex items-center gap-1 text-xs text-green-300">
                                    <div className="w-1 h-1 rounded-full bg-green-400"></div>
                                    <span>{feature}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                            <div className="text-white/60">
                              <ChevronRight size={16} />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : isVideoPlaying ? (
                  <div className="w-full h-full relative">
                    {/* Slideshow navigation */}
                    <div className="absolute bottom-0 left-0 right-0 bg-black/50 p-3 z-20">
                      <div className="flex items-center justify-center gap-2">
                        {Array.from({length: totalSlides}).map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setCurrentSlide(index)}
                            className={`w-2 h-2 rounded-full transition-all duration-300 ${
                              currentSlide === index ? 'bg-blue-500 w-6' : 'bg-gray-400 hover:bg-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>

                    {/* Conteúdo do tutorial */}
                    <div className="w-full h-full flex items-center justify-center relative">
                      <div className="tutorial-content w-full h-full relative overflow-hidden">
                        
                        {/* Tutorial de Usuários */}
                        {selectedTutorial?.id === 'users' && (
                          <>
                        {/* Slide 1: Página Real de Usuários */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-md h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            {/* Header exato da página */}
                            <div className="p-2 border-b">
                              <div className="flex justify-between items-center mb-2">
                                <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                  <Users size={20} className="mr-2 text-slate-600" />
                                  Usuários
                                </h1>
                                <div className="flex items-center gap-1">
                                  <button className="flex items-center gap-1 px-2 py-1 bg-gray-500 text-white rounded text-xs">
                                    Exportar
                                  </button>
                                  <button className="flex items-center gap-1 px-2 py-1 bg-slate-600 text-white rounded">
                                    <Plus size={14} />
                                    <span className="text-xs">Novo Usuário</span>
                                  </button>
                                </div>
                              </div>
                            </div>
                            {/* Filtros */}
                            <div className="bg-white p-2 border-b">
                              <div className="flex items-center gap-2">
                                <div className="flex-1">
                                  <input 
                                    type="text" 
                                    placeholder="Buscar usuários..." 
                                    className="w-full px-2 py-1 border rounded text-xs"
                                  />
                                </div>
                                <select className="px-2 py-1 border rounded text-xs">
                                  <option>Todos os status</option>
                                </select>
                                <button className="px-2 py-1 bg-blue-600 text-white rounded text-xs">Filtrar</button>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-xs">
                              Passo 1: Interface da página de usuários
                            </div>
                          </div>
                        </div>

                        {/* Slide 2: Tabela Real de Usuários */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            <div className="bg-white border border-gray-200 rounded-lg m-2">
                              <div className="bg-gray-50 px-2 py-2 border-b flex items-center justify-between">
                                <h3 className="text-sm font-semibold text-gray-800">Lista de Usuários</h3>
                                <button className="p-1 text-gray-600 hover:text-blue-600">
                                  <RefreshCw size={14} />
                                </button>
                              </div>
                              <div className="overflow-x-auto">
                                <table className="min-w-full">
                                  <thead className="bg-gray-50">
                                    <tr>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Usuário</th>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Ações</th>
                                    </tr>
                                  </thead>
                                  <tbody className="bg-white divide-y divide-gray-200">
                                    {[1,2].map(i => (
                                      <tr key={i} className="hover:bg-gray-50">
                                        <td className="px-2 py-2">
                                          <div className="flex items-center">
                                            <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                                              <Users size={12} className="text-blue-600" />
                                            </div>
                                            <div className="ml-2">
                                              <p className="text-xs font-medium text-gray-800">João Silva {i}</p>
                                              <p className="text-xs text-gray-500">joao{i}</p>
                                            </div>
                                          </div>
                                        </td>
                                        <td className="px-2 py-2 text-xs text-gray-600">joao{i}@empresa.com</td>
                                        <td className="px-2 py-2">
                                          <span className="px-1 py-0.5 text-xs rounded-full bg-green-100 text-green-800">Ativo</span>
                                        </td>
                                        <td className="px-2 py-2">
                                          <div className="flex gap-1">
                                            <button className="p-0.5 text-gray-500 hover:text-blue-500" title="Editar">
                                              <Edit size={12} />
                                            </button>
                                            <button className="p-0.5 text-gray-500 hover:text-purple-500" title="Permissões">
                                              <Lock size={12} />
                                            </button>
                                            <button className="p-0.5 text-gray-500 hover:text-amber-500" title="Desativar">
                                              <Power size={12} />
                                            </button>
                                            <button className="p-0.5 text-gray-500 hover:text-red-500" title="Excluir">
                                              <Trash size={12} />
                                            </button>
                                          </div>
                                        </td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-green-600 text-white px-2 py-1 rounded text-xs">
                              Passo 2: Visualizando a tabela de usuários
                            </div>
                          </div>
                        </div>

                        {/* Slide 3: Modal Real de Criação */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-black/50 h-full w-full flex items-center justify-center p-4 relative">
                            <div className="bg-white rounded-lg shadow-xl w-full max-w-sm">
                              <div className="flex items-center justify-between p-3 border-b">
                                <h3 className="text-sm font-semibold text-gray-800">Novo Usuário</h3>
                                <button className="text-gray-400 hover:text-gray-600">
                                  <Plus size={16} className="rotate-45" />
                                </button>
                              </div>
                              <div className="p-3 space-y-3">
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Nome Completo *</label>
                                  <input 
                                    type="text" 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    placeholder="Digite o nome completo"
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Email *</label>
                                  <input 
                                    type="email" 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    placeholder="<EMAIL>"
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Profissão</label>
                                  <select className="w-full px-2 py-1 border border-gray-300 rounded text-xs">
                                    <option>Selecione uma profissão</option>
                                    <option>Desenvolvedor</option>
                                  </select>
                                </div>
                              </div>
                              <div className="flex items-center justify-end gap-2 p-3 border-t bg-gray-50">
                                <button className="px-3 py-1 text-gray-700 bg-white border border-gray-300 rounded text-xs">
                                  Cancelar
                                </button>
                                <button className="px-3 py-1 bg-blue-600 text-white rounded text-xs">
                                  Criar Usuário
                                </button>
                              </div>
                            </div>
                            <div className="absolute bottom-4 right-4 bg-purple-600 text-white px-2 py-1 rounded text-xs">
                              Passo 3: Clicando em "Novo Usuário"
                            </div>
                          </div>
                        </div>

                        {/* Slide 4: Ações da Tabela */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg p-2 relative">
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2 mb-2">
                              <h4 className="text-sm font-semibold text-yellow-800 mb-2">Ações Disponíveis:</h4>
                              <div className="grid grid-cols-2 gap-2 text-xs">
                                <div className="flex items-center gap-1">
                                  <Edit size={12} className="text-blue-600" />
                                  <span>Editar usuário</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <ShieldIcon size={12} className="text-blue-600" />
                                  <span>Gerenciar módulos</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Lock size={12} className="text-purple-600" />
                                  <span>Gerenciar permissões</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Power size={12} className="text-amber-600" />
                                  <span>Desativar usuário</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Trash size={12} className="text-red-600" />
                                  <span>Excluir usuário</span>
                                </div>
                              </div>
                            </div>
                            <div className="border border-orange-300 rounded-lg bg-orange-50">
                              <table className="w-full">
                                <tbody>
                                  <tr className="bg-orange-100">
                                    <td className="px-2 py-2">
                                      <div className="flex items-center">
                                        <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                                          <Users size={12} className="text-blue-600" />
                                        </div>
                                        <div className="ml-2">
                                          <p className="text-xs font-medium text-gray-800">João Silva</p>
                                          <p className="text-xs text-gray-500">joao</p>
                                        </div>
                                      </div>
                                    </td>
                                    <td className="px-2 py-2">
                                      <div className="flex gap-1">
                                        <button className="p-0.5 text-blue-600 bg-blue-100 rounded animate-pulse" title="Editar">
                                          <Edit size={12} />
                                        </button>
                                        <button className="p-0.5 text-blue-600" title="Módulos">
                                          <ShieldIcon size={12} />
                                        </button>
                                        <button className="p-0.5 text-purple-600" title="Permissões">
                                          <Lock size={12} />
                                        </button>
                                        <button className="p-0.5 text-amber-600" title="Desativar">
                                          <Power size={12} />
                                        </button>
                                        <button className="p-0.5 text-red-600" title="Excluir">
                                          <Trash size={12} />
                                        </button>
                                      </div>
                                    </td>
                                  </tr>
                                </tbody>
                              </table>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-orange-600 text-white px-2 py-1 rounded text-xs">
                              Passo 4: Usando as ações da tabela
                            </div>
                          </div>
                        </div>

                        {/* Slide 5: Modal de Edição Real */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-black/50 h-full w-full flex items-center justify-center p-4 relative">
                            <div className="bg-white rounded-lg shadow-xl w-full max-w-sm">
                              <div className="flex items-center justify-between p-3 border-b">
                                <h3 className="text-sm font-semibold text-gray-800">Editar Usuário</h3>
                                <button className="text-gray-400 hover:text-gray-600">
                                  <Plus size={16} className="rotate-45" />
                                </button>
                              </div>
                              <div className="p-3 space-y-3">
                                <div className="flex items-center gap-2 mb-3">
                                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                    <Users size={16} className="text-blue-600" />
                                  </div>
                                  <div>
                                    <p className="text-xs font-medium text-gray-800">João Silva</p>
                                    <p className="text-xs text-gray-500"><EMAIL></p>
                                  </div>
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Nome Completo</label>
                                  <input 
                                    type="text" 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    defaultValue="João Silva"
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Email</label>
                                  <input 
                                    type="email" 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    defaultValue="<EMAIL>"
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Status</label>
                                  <select className="w-full px-2 py-1 border border-gray-300 rounded text-xs">
                                    <option value="true">Ativo</option>
                                    <option value="false">Inativo</option>
                                  </select>
                                </div>
                              </div>
                              <div className="flex items-center justify-end gap-2 p-3 border-t bg-gray-50">
                                <button className="px-3 py-1 text-gray-700 bg-white border border-gray-300 rounded text-xs">
                                  Cancelar
                                </button>
                                <button className="px-3 py-1 bg-blue-600 text-white rounded text-xs">
                                  Salvar Alterações
                                </button>
                              </div>
                            </div>
                            <div className="absolute bottom-4 right-4 bg-indigo-600 text-white px-2 py-1 rounded text-xs">
                              Passo 5: Modal de edição de usuário
                            </div>
                          </div>
                        </div>

                        {/* Slide 6: Modal de Permissões */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-black/50 h-full w-full flex items-center justify-center p-2 relative">
                            <div className="bg-white rounded-lg shadow-xl w-full max-w-lg">
                              <div className="flex items-center justify-between p-3 border-b">
                                <h3 className="text-sm font-semibold text-gray-800">Permissões - João Silva</h3>
                                <button className="text-gray-400 hover:text-gray-600">
                                  <Plus size={16} className="rotate-45" />
                                </button>
                              </div>
                              <div className="p-3">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <h4 className="text-xs font-semibold text-gray-700 mb-2">Módulos Disponíveis</h4>
                                    <div className="space-y-2">
                                      {[
                                        { name: 'Administração', checked: true, color: 'text-blue-700' },
                                        { name: 'RH', checked: true, color: 'text-green-700' },
                                        { name: 'Financeiro', checked: false, color: 'text-gray-600' },
                                        { name: 'Agendamento', checked: false, color: 'text-gray-600' }
                                      ].map((module, i) => (
                                        <label key={i} className="flex items-center gap-2 text-xs cursor-pointer">
                                          <input 
                                            type="checkbox" 
                                            className="rounded border-gray-300 text-blue-600 w-3 h-3" 
                                            defaultChecked={module.checked} 
                                          />
                                          <span className={`${module.color} ${module.checked ? 'font-medium' : ''}`}>
                                            {module.name}
                                          </span>
                                        </label>
                                      ))}
                                    </div>
                                  </div>
                                  <div>
                                    <h4 className="text-xs font-semibold text-gray-700 mb-2">Permissões Específicas</h4>
                                    <div className="space-y-2">
                                      {[
                                        { name: 'Criar usuários', checked: true, color: 'text-green-700' },
                                        { name: 'Editar usuários', checked: true, color: 'text-green-700' },
                                        { name: 'Excluir usuários', checked: false, color: 'text-gray-400' },
                                        { name: 'Ver relatórios', checked: true, color: 'text-green-700' }
                                      ].map((perm, i) => (
                                        <label key={i} className="flex items-center gap-2 text-xs cursor-pointer">
                                          <input 
                                            type="checkbox" 
                                            className="rounded border-gray-300 text-blue-600 w-3 h-3" 
                                            defaultChecked={perm.checked} 
                                          />
                                          <span className={perm.color}>{perm.name}</span>
                                        </label>
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center justify-end gap-2 p-3 border-t bg-gray-50">
                                <button className="px-3 py-1 text-gray-700 bg-white border border-gray-300 rounded text-xs">
                                  Cancelar
                                </button>
                                <button className="px-3 py-1 bg-purple-600 text-white rounded text-xs">
                                  Salvar Permissões
                                </button>
                              </div>
                            </div>
                            <div className="absolute bottom-4 right-4 bg-purple-600 text-white px-2 py-1 rounded text-xs">
                              Passo 6: Gerenciando permissões e módulos
                            </div>
                          </div>
                        </div>

                        {/* Slide 7: Desativar/Excluir Usuário */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-black/50 h-full w-full flex items-center justify-center p-4 relative">
                            <div className="bg-white rounded-lg shadow-xl w-full max-w-sm">
                              <div className="flex items-center justify-between p-3 border-b">
                                <h3 className="text-sm font-semibold text-gray-800">Confirmar Ação</h3>
                                <button className="text-gray-400 hover:text-gray-600">
                                  <Plus size={16} className="rotate-45" />
                                </button>
                              </div>
                              <div className="p-4 text-center">
                                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                  <Trash size={20} className="text-red-600" />
                                </div>
                                <h4 className="text-sm font-semibold text-gray-800 mb-2">Excluir Usuário</h4>
                                <p className="text-xs text-gray-600 mb-3">
                                  Tem certeza que deseja excluir permanentemente o usuário <strong>João Silva</strong>?
                                </p>
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2 mb-3">
                                  <p className="text-yellow-800 text-xs">
                                    ⚠️ Esta ação não pode ser desfeita.
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center justify-end gap-2 p-3 border-t bg-gray-50">
                                <button className="px-3 py-1 text-gray-700 bg-white border border-gray-300 rounded text-xs">
                                  Cancelar
                                </button>
                                <button className="px-3 py-1 bg-red-600 text-white rounded text-xs">
                                  Sim, Excluir
                                </button>
                              </div>
                            </div>
                            <div className="absolute bottom-4 right-4 bg-red-600 text-white px-2 py-1 rounded text-xs">
                              Passo 7: Confirmação de exclusão
                            </div>
                          </div>
                        </div>

                        {/* Slide 8: Conclusão */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'}`}>
                          <div className="flex flex-col items-center justify-center h-full p-6">
                            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center max-w-lg">
                              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <Users size={32} className="text-green-400" />
                              </div>
                              <h2 className="text-2xl font-bold text-white mb-3">Tutorial Concluído!</h2>
                              <p className="text-blue-200 text-sm mb-6">
                                Agora você domina o gerenciamento completo de usuários: visualizar, criar, editar, 
                                configurar permissões, desativar e excluir usuários.
                              </p>
                              <div className="grid grid-cols-2 gap-3 text-green-300 text-sm">
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Visualizar lista</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Criar usuários</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Editar perfis</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Gerenciar permissões</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Desativar usuários</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Excluir usuários</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                          </>
                        )}

                        {/* Tutorial de Profissões */}
                        {selectedTutorial?.id === 'professions' && (
                          <>
                        {/* Slide 1: Página Real de Profissões */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-md h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            <div className="p-2 border-b">
                              <div className="flex justify-between items-center mb-2">
                                <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                  <Briefcase size={20} className="mr-2 text-slate-600" />
                                  Profissões
                                </h1>
                                <div className="flex items-center gap-1">
                                  <button className="flex items-center gap-1 px-2 py-1 bg-gray-500 text-white rounded text-xs">
                                    Exportar
                                  </button>
                                  <button className="flex items-center gap-1 px-2 py-1 bg-slate-600 text-white rounded">
                                    <Plus size={14} />
                                    <span className="text-xs">Nova Profissão</span>
                                  </button>
                                </div>
                              </div>
                            </div>
                            <div className="bg-white p-2 border-b">
                              <div className="flex items-center gap-2">
                                <div className="flex-1">
                                  <input 
                                    type="text" 
                                    placeholder="Buscar profissões..." 
                                    className="w-full px-2 py-1 border rounded text-xs"
                                  />
                                </div>
                                <select className="px-2 py-1 border rounded text-xs">
                                  <option>Todos os grupos</option>
                                </select>
                                <button className="px-2 py-1 bg-green-600 text-white rounded text-xs">Filtrar</button>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-green-600 text-white px-2 py-1 rounded text-xs">
                              Passo 1: Interface da página de profissões
                            </div>
                          </div>
                        </div>

                        {/* Slide 2: Tabela Real de Profissões */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            <div className="bg-white border border-gray-200 rounded-lg m-2">
                              <div className="bg-gray-50 px-2 py-2 border-b flex items-center justify-between">
                                <h3 className="text-sm font-semibold text-gray-800">Lista de Profissões</h3>
                                <button className="p-1 text-gray-600 hover:text-green-600">
                                  <RefreshCw size={14} />
                                </button>
                              </div>
                              <div className="overflow-x-auto">
                                <table className="min-w-full">
                                  <thead className="bg-gray-50">
                                    <tr>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Profissão</th>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Grupo</th>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Ações</th>
                                    </tr>
                                  </thead>
                                  <tbody className="bg-white divide-y divide-gray-200">
                                    {[1,2].map(i => (
                                      <tr key={i} className="hover:bg-gray-50">
                                        <td className="px-2 py-2">
                                          <div className="flex items-center">
                                            <div className="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center">
                                              <Briefcase size={12} className="text-green-600" />
                                            </div>
                                            <div className="ml-2">
                                              <p className="text-xs font-medium text-gray-800">Desenvolvedor {i}</p>
                                            </div>
                                          </div>
                                        </td>
                                        <td className="px-2 py-2">
                                          <span className="px-1 py-0.5 text-xs rounded-full bg-indigo-100 text-indigo-800">TI</span>
                                        </td>
                                        <td className="px-2 py-2">
                                          <span className="px-1 py-0.5 text-xs rounded-full bg-green-100 text-green-800">Ativa</span>
                                        </td>
                                        <td className="px-2 py-2">
                                          <div className="flex gap-1">
                                            <button className="p-0.5 text-gray-500 hover:text-green-500" title="Editar">
                                              <Edit size={12} />
                                            </button>
                                            <button className="p-0.5 text-gray-500 hover:text-indigo-500" title="Ver usuários">
                                              <Users size={12} />
                                            </button>
                                            <button className="p-0.5 text-gray-500 hover:text-red-500" title="Excluir">
                                              <Trash size={12} />
                                            </button>
                                          </div>
                                        </td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-xs">
                              Passo 2: Visualizando a tabela de profissões
                            </div>
                          </div>
                        </div>

                        {/* Slide 3: Modal de Criação de Profissão */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-black/50 h-full w-full flex items-center justify-center p-4 relative">
                            <div className="bg-white rounded-lg shadow-xl w-full max-w-sm">
                              <div className="flex items-center justify-between p-3 border-b">
                                <h3 className="text-sm font-semibold text-gray-800">Nova Profissão</h3>
                                <button className="text-gray-400 hover:text-gray-600">
                                  <Plus size={16} className="rotate-45" />
                                </button>
                              </div>
                              <div className="p-3 space-y-3">
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Nome da Profissão *</label>
                                  <input 
                                    type="text" 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    placeholder="Digite o nome da profissão"
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Grupo</label>
                                  <select className="w-full px-2 py-1 border border-gray-300 rounded text-xs">
                                    <option>Selecione um grupo</option>
                                    <option>TI</option>
                                    <option>Saúde</option>
                                  </select>
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Descrição</label>
                                  <textarea 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    rows="2"
                                    placeholder="Descreva a profissão"
                                  />
                                </div>
                              </div>
                              <div className="flex items-center justify-end gap-2 p-3 border-t bg-gray-50">
                                <button className="px-3 py-1 text-gray-700 bg-white border border-gray-300 rounded text-xs">
                                  Cancelar
                                </button>
                                <button className="px-3 py-1 bg-green-600 text-white rounded text-xs">
                                  Criar Profissão
                                </button>
                              </div>
                            </div>
                            <div className="absolute bottom-4 right-4 bg-purple-600 text-white px-2 py-1 rounded text-xs">
                              Passo 3: Clicando em "Nova Profissão"
                            </div>
                          </div>
                        </div>

                        {/* Slide 4: Aba de Grupos */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            <div className="p-2 border-b">
                              <div className="flex items-center gap-4 mb-2">
                                <button className="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                                  Profissões
                                </button>
                                <button className="px-3 py-1 text-xs bg-green-600 text-white rounded">
                                  Grupos de Profissões
                                </button>
                              </div>
                            </div>
                            <div className="bg-white border border-gray-200 rounded-lg m-2">
                              <div className="bg-gray-50 px-2 py-2 border-b flex items-center justify-between">
                                <h3 className="text-sm font-semibold text-gray-800">Lista de Grupos</h3>
                                <button className="flex items-center gap-1 px-2 py-1 bg-green-600 text-white rounded text-xs">
                                  <Plus size={12} />
                                  Novo Grupo
                                </button>
                              </div>
                              <div className="overflow-x-auto">
                                <table className="min-w-full">
                                  <thead className="bg-gray-50">
                                    <tr>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Grupo</th>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Profissões</th>
                                      <th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase">Ações</th>
                                    </tr>
                                  </thead>
                                  <tbody className="bg-white divide-y divide-gray-200">
                                    {['TI', 'Saúde'].map((group, i) => (
                                      <tr key={i} className="hover:bg-gray-50">
                                        <td className="px-2 py-2">
                                          <div className="flex items-center">
                                            <div className="w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center">
                                              <Building size={12} className="text-indigo-600" />
                                            </div>
                                            <div className="ml-2">
                                              <p className="text-xs font-medium text-gray-800">{group}</p>
                                            </div>
                                          </div>
                                        </td>
                                        <td className="px-2 py-2 text-xs text-gray-600">{i + 2} profissões</td>
                                        <td className="px-2 py-2">
                                          <div className="flex gap-1">
                                            <button className="p-0.5 text-gray-500 hover:text-green-500" title="Editar">
                                              <Edit size={12} />
                                            </button>
                                            <button className="p-0.5 text-gray-500 hover:text-red-500" title="Excluir">
                                              <Trash size={12} />
                                            </button>
                                          </div>
                                        </td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-indigo-600 text-white px-2 py-1 rounded text-xs">
                              Passo 4: Navegando para grupos de profissões
                            </div>
                          </div>
                        </div>

                        {/* Slide 5: Modal de Criação de Grupo */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-black/50 h-full w-full flex items-center justify-center p-4 relative">
                            <div className="bg-white rounded-lg shadow-xl w-full max-w-sm">
                              <div className="flex items-center justify-between p-3 border-b">
                                <h3 className="text-sm font-semibold text-gray-800">Novo Grupo</h3>
                                <button className="text-gray-400 hover:text-gray-600">
                                  <Plus size={16} className="rotate-45" />
                                </button>
                              </div>
                              <div className="p-3 space-y-3">
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Nome do Grupo *</label>
                                  <input 
                                    type="text" 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    placeholder="Digite o nome do grupo"
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Descrição</label>
                                  <textarea 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    rows="3"
                                    placeholder="Descreva o grupo de profissões"
                                  />
                                </div>
                              </div>
                              <div className="flex items-center justify-end gap-2 p-3 border-t bg-gray-50">
                                <button className="px-3 py-1 text-gray-700 bg-white border border-gray-300 rounded text-xs">
                                  Cancelar
                                </button>
                                <button className="px-3 py-1 bg-indigo-600 text-white rounded text-xs">
                                  Criar Grupo
                                </button>
                              </div>
                            </div>
                            <div className="absolute bottom-4 right-4 bg-green-600 text-white px-2 py-1 rounded text-xs">
                              Passo 5: Criando um novo grupo
                            </div>
                          </div>
                        </div>

                        {/* Slide 6: Filtros Avançados */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg p-2 relative">
                            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2 mb-2">
                              <h4 className="text-sm font-semibold text-yellow-800 mb-2">Filtros Disponíveis:</h4>
                              <div className="grid grid-cols-2 gap-2 text-xs">
                                <div className="flex items-center gap-1">
                                  <Building size={12} className="text-blue-600" />
                                  <span>Por empresa</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Briefcase size={12} className="text-green-600" />
                                  <span>Por grupo</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Users size={12} className="text-purple-600" />
                                  <span>Por usuários</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <CheckCircle size={12} className="text-amber-600" />
                                  <span>Por status</span>
                                </div>
                              </div>
                            </div>
                            <div className="border border-green-300 rounded-lg bg-green-50 p-2">
                              <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                  <input 
                                    type="text" 
                                    placeholder="Buscar por nome..." 
                                    className="flex-1 px-2 py-1 border rounded text-xs bg-white"
                                  />
                                  <select className="px-2 py-1 border rounded text-xs bg-white">
                                    <option>Todos os grupos</option>
                                    <option>TI</option>
                                    <option>Saúde</option>
                                  </select>
                                </div>
                                <div className="flex items-center gap-2">
                                  <select className="flex-1 px-2 py-1 border rounded text-xs bg-white">
                                    <option>Todos os status</option>
                                    <option>Ativas</option>
                                    <option>Inativas</option>
                                  </select>
                                  <button className="px-3 py-1 bg-green-600 text-white rounded text-xs">
                                    Filtrar
                                  </button>
                                </div>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-amber-600 text-white px-2 py-1 rounded text-xs">
                              Passo 6: Usando filtros avançados
                            </div>
                          </div>
                        </div>

                        {/* Slide 7: Associação com Usuários */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-black/50 h-full w-full flex items-center justify-center p-2 relative">
                            <div className="bg-white rounded-lg shadow-xl w-full max-w-lg">
                              <div className="flex items-center justify-between p-3 border-b">
                                <h3 className="text-sm font-semibold text-gray-800">Usuários - Desenvolvedor</h3>
                                <button className="text-gray-400 hover:text-gray-600">
                                  <Plus size={16} className="rotate-45" />
                                </button>
                              </div>
                              <div className="p-3">
                                <div className="mb-3">
                                  <h4 className="text-xs font-semibold text-gray-700 mb-2">Usuários com esta profissão:</h4>
                                  <div className="space-y-2 max-h-32 overflow-y-auto">
                                    {[
                                      { name: 'João Silva', email: '<EMAIL>', active: true },
                                      { name: 'Maria Santos', email: '<EMAIL>', active: true },
                                      { name: 'Pedro Costa', email: '<EMAIL>', active: false }
                                    ].map((user, i) => (
                                      <div key={i} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                                        <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center">
                                          <Users size={12} className="text-blue-600" />
                                        </div>
                                        <div className="flex-1">
                                          <p className="text-xs font-medium text-gray-800">{user.name}</p>
                                          <p className="text-xs text-gray-500">{user.email}</p>
                                        </div>
                                        <span className={`px-1 py-0.5 text-xs rounded-full ${
                                          user.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                        }`}>
                                          {user.active ? 'Ativo' : 'Inativo'}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center justify-end gap-2 p-3 border-t bg-gray-50">
                                <button className="px-3 py-1 text-gray-700 bg-white border border-gray-300 rounded text-xs">
                                  Fechar
                                </button>
                              </div>
                            </div>
                            <div className="absolute bottom-4 right-4 bg-blue-600 text-white px-2 py-1 rounded text-xs">
                              Passo 7: Visualizando usuários associados
                            </div>
                          </div>
                        </div>

                        {/* Slide 8: Conclusão */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'}`}>
                          <div className="flex flex-col items-center justify-center h-full p-6">
                            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center max-w-lg">
                              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <Briefcase size={32} className="text-green-400" />
                              </div>
                              <h2 className="text-2xl font-bold text-white mb-3">Tutorial Concluído!</h2>
                              <p className="text-blue-200 text-sm mb-6">
                                Agora você domina o gerenciamento completo de profissões: criar, organizar em grupos, 
                                filtrar, associar usuários e gerenciar todo o sistema profissional.
                              </p>
                              <div className="grid grid-cols-2 gap-3 text-green-300 text-sm">
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Visualizar profissões</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Criar profissões</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Gerenciar grupos</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Associar usuários</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Filtrar e buscar</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Organizar sistema</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                          </>
                        )}

                        {/* Tutorial de Configurações */}
                        {selectedTutorial?.id === 'settings' && (
                          <>
                        {/* Slide 1: Interface Principal de Configurações */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 0 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            <div className="p-2 border-b">
                              <div className="flex justify-between items-center mb-2">
                                <h1 className="text-lg font-bold text-slate-800 flex items-center">
                                  <Settings size={20} className="mr-2 text-slate-600" />
                                  Configurações do Sistema
                                </h1>
                              </div>
                            </div>
                            <div className="bg-white p-2 border-b">
                              <div className="flex items-center gap-2 text-xs">
                                <button className="px-2 py-1 bg-purple-600 text-white rounded">Geral</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Empresas</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Email</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Backup</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Segurança</button>
                              </div>
                            </div>
                            <div className="p-2">
                              <div className="grid grid-cols-2 gap-2 text-xs">
                                <div>
                                  <label className="block text-gray-700 mb-1">Nome do Site</label>
                                  <input className="w-full px-2 py-1 border rounded" defaultValue="High Tide" readOnly />
                                </div>
                                <div>
                                  <label className="block text-gray-700 mb-1">URL do Site</label>
                                  <input className="w-full px-2 py-1 border rounded" defaultValue="https://hightide.site" readOnly />
                                </div>
                                <div>
                                  <label className="block text-gray-700 mb-1">Fuso Horário</label>
                                  <select className="w-full px-2 py-1 border rounded" disabled>
                                    <option>América/São Paulo</option>
                                  </select>
                                </div>
                                <div>
                                  <label className="block text-gray-700 mb-1">Formato de Data</label>
                                  <select className="w-full px-2 py-1 border rounded" disabled>
                                    <option>DD/MM/YYYY</option>
                                  </select>
                                </div>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-purple-600 text-white px-2 py-1 rounded text-xs">
                              Passo 1: Interface principal das configurações
                            </div>
                          </div>
                        </div>

                        {/* Slide 2: Aba de Empresas */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 1 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            <div className="p-2 border-b">
                              <div className="flex items-center gap-2 text-xs">
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Geral</button>
                                <button className="px-2 py-1 bg-purple-600 text-white rounded">Empresas</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Email</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Backup</button>
                              </div>
                            </div>
                            <div className="p-2">
                              <div className="flex justify-between items-center mb-2">
                                <h3 className="text-sm font-semibold">Gerenciamento de Empresas</h3>
                                <button className="px-2 py-1 bg-purple-600 text-white rounded text-xs flex items-center gap-1">
                                  <Plus size={12} />
                                  Nova Empresa
                                </button>
                              </div>
                              <div className="border rounded">
                                <table className="w-full text-xs">
                                  <thead className="bg-gray-50">
                                    <tr>
                                      <th className="px-2 py-1 text-left">Empresa</th>
                                      <th className="px-2 py-1 text-left">CNPJ</th>
                                      <th className="px-2 py-1 text-left">Status</th>
                                      <th className="px-2 py-1 text-left">Ações</th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    {[1,2].map(i => (
                                      <tr key={i} className="border-t">
                                        <td className="px-2 py-1">
                                          <div className="flex items-center gap-1">
                                            <Building size={12} className="text-purple-600" />
                                            <span>Empresa {i}</span>
                                          </div>
                                        </td>
                                        <td className="px-2 py-1">12.345.678/000{i}-90</td>
                                        <td className="px-2 py-1">
                                          <span className="px-1 py-0.5 bg-green-100 text-green-800 rounded text-xs">Ativa</span>
                                        </td>
                                        <td className="px-2 py-1">
                                          <div className="flex gap-1">
                                            <button className="p-0.5 text-gray-500 hover:text-purple-500">
                                              <Edit size={10} />
                                            </button>
                                            <button className="p-0.5 text-gray-500 hover:text-red-500">
                                              <Trash size={10} />
                                            </button>
                                          </div>
                                        </td>
                                      </tr>
                                    ))}
                                  </tbody>
                                </table>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-blue-600 text-white px-2 py-1 rounded text-xs">
                              Passo 2: Gerenciando empresas do sistema
                            </div>
                          </div>
                        </div>

                        {/* Slide 3: Configurações de Email */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 2 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            <div className="p-2 border-b">
                              <div className="flex items-center gap-2 text-xs">
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Geral</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Empresas</button>
                                <button className="px-2 py-1 bg-purple-600 text-white rounded">Email</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Backup</button>
                              </div>
                            </div>
                            <div className="p-2">
                              <div className="flex justify-between items-center mb-2">
                                <h3 className="text-sm font-semibold flex items-center gap-1">
                                  <Mail size={14} className="text-purple-600" />
                                  Configurações de Email
                                </h3>
                                <button className="px-2 py-1 bg-purple-600 text-white rounded text-xs flex items-center gap-1">
                                  <Plus size={12} />
                                  Nova Config
                                </button>
                              </div>
                              <div className="space-y-2">
                                <div className="border rounded p-2 bg-gray-50">
                                  <div className="flex justify-between items-start">
                                    <div>
                                      <p className="text-xs font-medium">Servidor Principal</p>
                                      <p className="text-xs text-gray-600">smtp.gmail.com:587</p>
                                      <p className="text-xs text-gray-600"><EMAIL></p>
                                    </div>
                                    <div className="flex gap-1">
                                      <span className="px-1 py-0.5 bg-green-100 text-green-800 rounded text-xs">Ativo</span>
                                    </div>
                                  </div>
                                  <div className="flex gap-1 mt-2">
                                    <button className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs flex items-center gap-1">
                                      <Send size={10} />
                                      Testar
                                    </button>
                                    <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                                      <Edit size={10} />
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-green-600 text-white px-2 py-1 rounded text-xs">
                              Passo 3: Configurando servidores de email
                            </div>
                          </div>
                        </div>

                        {/* Slide 4: Modal de Configuração de Email */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 3 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-black/50 h-full w-full flex items-center justify-center p-4 relative">
                            <div className="bg-white rounded-lg shadow-xl w-full max-w-sm">
                              <div className="flex items-center justify-between p-3 border-b">
                                <h3 className="text-sm font-semibold text-gray-800 flex items-center gap-1">
                                  <Mail size={14} />
                                  Nova Configuração de Email
                                </h3>
                                <button className="text-gray-400 hover:text-gray-600">
                                  <Plus size={16} className="rotate-45" />
                                </button>
                              </div>
                              <div className="p-3 space-y-2">
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Servidor SMTP</label>
                                  <input 
                                    type="text" 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    placeholder="smtp.gmail.com"
                                  />
                                </div>
                                <div className="grid grid-cols-2 gap-2">
                                  <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Porta</label>
                                    <input 
                                      type="number" 
                                      className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                      placeholder="587"
                                    />
                                  </div>
                                  <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Segurança</label>
                                    <select className="w-full px-2 py-1 border border-gray-300 rounded text-xs">
                                      <option>TLS</option>
                                      <option>SSL</option>
                                    </select>
                                  </div>
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Usuário</label>
                                  <input 
                                    type="email" 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    placeholder="<EMAIL>"
                                  />
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Senha</label>
                                  <input 
                                    type="password" 
                                    className="w-full px-2 py-1 border border-gray-300 rounded text-xs" 
                                    placeholder="••••••••"
                                  />
                                </div>
                              </div>
                              <div className="flex items-center justify-end gap-2 p-3 border-t bg-gray-50">
                                <button className="px-3 py-1 text-gray-700 bg-white border border-gray-300 rounded text-xs">
                                  Cancelar
                                </button>
                                <button className="px-3 py-1 bg-purple-600 text-white rounded text-xs">
                                  Salvar Config
                                </button>
                              </div>
                            </div>
                            <div className="absolute bottom-4 right-4 bg-orange-600 text-white px-2 py-1 rounded text-xs">
                              Passo 4: Criando nova configuração de email
                            </div>
                          </div>
                        </div>

                        {/* Slide 5: Configurações de Backup */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 4 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            <div className="p-2 border-b">
                              <div className="flex items-center gap-2 text-xs">
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Geral</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Empresas</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Email</button>
                                <button className="px-2 py-1 bg-purple-600 text-white rounded">Backup</button>
                              </div>
                            </div>
                            <div className="p-2">
                              <h3 className="text-sm font-semibold mb-2 flex items-center gap-1">
                                <Database size={14} className="text-purple-600" />
                                Configurações de Backup
                              </h3>
                              <div className="space-y-2">
                                <div className="grid grid-cols-2 gap-2">
                                  <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Frequência</label>
                                    <select className="w-full px-2 py-1 border rounded text-xs" disabled>
                                      <option>Diário</option>
                                      <option>Semanal</option>
                                      <option>Mensal</option>
                                    </select>
                                  </div>
                                  <div>
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Horário</label>
                                    <input type="time" className="w-full px-2 py-1 border rounded text-xs" defaultValue="01:00" readOnly />
                                  </div>
                                </div>
                                <div>
                                  <label className="block text-xs font-medium text-gray-700 mb-1">Armazenamento</label>
                                  <select className="w-full px-2 py-1 border rounded text-xs" disabled>
                                    <option>Servidor Local</option>
                                    <option>Amazon S3</option>
                                    <option>Google Cloud</option>
                                  </select>
                                </div>
                                <div className="border rounded p-2 bg-gray-50">
                                  <h4 className="text-xs font-medium mb-1">Backups Recentes</h4>
                                  <div className="space-y-1">
                                    {['backup_20240601.sql', 'backup_20240531.sql'].map((backup, i) => (
                                      <div key={i} className="flex justify-between items-center text-xs">
                                        <span className="flex items-center gap-1">
                                          <Database size={10} className="text-gray-500" />
                                          {backup}
                                        </span>
                                        <div className="flex gap-1">
                                          <button className="p-0.5 text-blue-500 hover:text-blue-700">
                                            <Download size={10} />
                                          </button>
                                          <button className="p-0.5 text-red-500 hover:text-red-700">
                                            <Trash size={10} />
                                          </button>
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-indigo-600 text-white px-2 py-1 rounded text-xs">
                              Passo 5: Configurando backups automáticos
                            </div>
                          </div>
                        </div>

                        {/* Slide 6: Configurações de Segurança */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 5 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            <div className="p-2 border-b">
                              <div className="flex items-center gap-2 text-xs">
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Geral</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Empresas</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Email</button>
                                <button className="px-2 py-1 bg-purple-600 text-white rounded">Segurança</button>
                              </div>
                            </div>
                            <div className="p-2">
                              <h3 className="text-sm font-semibold mb-2 flex items-center gap-1">
                                <Shield size={14} className="text-purple-600" />
                                Configurações de Segurança
                              </h3>
                              <div className="space-y-2">
                                <div className="border rounded p-2">
                                  <h4 className="text-xs font-medium mb-1">Política de Senhas</h4>
                                  <div className="space-y-1">
                                    <label className="flex items-center gap-1 text-xs">
                                      <input type="checkbox" className="w-3 h-3" defaultChecked />
                                      <span>Exigir letra maiúscula</span>
                                    </label>
                                    <label className="flex items-center gap-1 text-xs">
                                      <input type="checkbox" className="w-3 h-3" defaultChecked />
                                      <span>Exigir número</span>
                                    </label>
                                    <label className="flex items-center gap-1 text-xs">
                                      <input type="checkbox" className="w-3 h-3" />
                                      <span>Exigir caractere especial</span>
                                    </label>
                                  </div>
                                  <div className="mt-2">
                                    <label className="block text-xs font-medium text-gray-700 mb-1">Tamanho mínimo</label>
                                    <input type="number" className="w-full px-2 py-1 border rounded text-xs" defaultValue="8" readOnly />
                                  </div>
                                </div>
                                <div className="border rounded p-2">
                                  <h4 className="text-xs font-medium mb-1">Segurança de Login</h4>
                                  <div className="space-y-1">
                                    <label className="flex items-center gap-1 text-xs">
                                      <input type="checkbox" className="w-3 h-3" defaultChecked />
                                      <span>Limitar tentativas de login</span>
                                    </label>
                                    <label className="flex items-center gap-1 text-xs">
                                      <input type="checkbox" className="w-3 h-3" defaultChecked />
                                      <span>Rastrear IPs de login</span>
                                    </label>
                                  </div>
                                  <div className="mt-2 grid grid-cols-2 gap-2">
                                    <div>
                                      <label className="block text-xs font-medium text-gray-700 mb-1">Máx tentativas</label>
                                      <input type="number" className="w-full px-2 py-1 border rounded text-xs" defaultValue="5" readOnly />
                                    </div>
                                    <div>
                                      <label className="block text-xs font-medium text-gray-700 mb-1">Timeout (min)</label>
                                      <input type="number" className="w-full px-2 py-1 border rounded text-xs" defaultValue="30" readOnly />
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-red-600 text-white px-2 py-1 rounded text-xs">
                              Passo 6: Configurando políticas de segurança
                            </div>
                          </div>
                        </div>

                        {/* Slide 7: Preferências do Sistema */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 6 ? 'opacity-100' : 'opacity-0'} flex items-center justify-center`}>
                          <div className="bg-white w-full max-w-lg h-4/5 overflow-hidden rounded-lg shadow-lg relative">
                            <div className="p-2 border-b">
                              <div className="flex items-center gap-2 text-xs">
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Geral</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Empresas</button>
                                <button className="px-2 py-1 bg-gray-100 text-gray-600 rounded">Email</button>
                                <button className="px-2 py-1 bg-purple-600 text-white rounded">Preferências</button>
                              </div>
                            </div>
                            <div className="p-2">
                              <h3 className="text-sm font-semibold mb-2 flex items-center gap-1">
                                <SlidersHorizontal size={14} className="text-purple-600" />
                                Preferências do Sistema
                              </h3>
                              <div className="space-y-2">
                                <div className="border rounded p-2">
                                  <h4 className="text-xs font-medium mb-1">Personalização</h4>
                                  <div className="space-y-1">
                                    <div>
                                      <label className="block text-xs font-medium text-gray-700 mb-1">Tema</label>
                                      <select className="w-full px-2 py-1 border rounded text-xs" disabled>
                                        <option>Claro</option>
                                        <option>Escuro</option>
                                        <option>Automático</option>
                                      </select>
                                    </div>
                                    <div>
                                      <label className="block text-xs font-medium text-gray-700 mb-1">Idioma</label>
                                      <select className="w-full px-2 py-1 border rounded text-xs" disabled>
                                        <option>Português (BR)</option>
                                        <option>English</option>
                                        <option>Español</option>
                                      </select>
                                    </div>
                                  </div>
                                </div>
                                <div className="border rounded p-2">
                                  <h4 className="text-xs font-medium mb-1">Notificações</h4>
                                  <div className="space-y-1">
                                    <label className="flex items-center gap-1 text-xs">
                                      <input type="checkbox" className="w-3 h-3" defaultChecked />
                                      <span>Notificações por email</span>
                                    </label>
                                    <label className="flex items-center gap-1 text-xs">
                                      <input type="checkbox" className="w-3 h-3" defaultChecked />
                                      <span>Notificações no sistema</span>
                                    </label>
                                    <label className="flex items-center gap-1 text-xs">
                                      <input type="checkbox" className="w-3 h-3" />
                                      <span>Notificações push</span>
                                    </label>
                                  </div>
                                </div>
                                <div className="border rounded p-2">
                                  <h4 className="text-xs font-medium mb-1">Módulos Ativos</h4>
                                  <div className="grid grid-cols-2 gap-1 text-xs">
                                    <label className="flex items-center gap-1">
                                      <input type="checkbox" className="w-3 h-3" defaultChecked />
                                      <span>Administração</span>
                                    </label>
                                    <label className="flex items-center gap-1">
                                      <input type="checkbox" className="w-3 h-3" defaultChecked />
                                      <span>RH</span>
                                    </label>
                                    <label className="flex items-center gap-1">
                                      <input type="checkbox" className="w-3 h-3" />
                                      <span>Financeiro</span>
                                    </label>
                                    <label className="flex items-center gap-1">
                                      <input type="checkbox" className="w-3 h-3" defaultChecked />
                                      <span>Agendamento</span>
                                    </label>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="absolute bottom-2 right-2 bg-teal-600 text-white px-2 py-1 rounded text-xs">
                              Passo 7: Personalizando preferências
                            </div>
                          </div>
                        </div>

                        {/* Slide 8: Conclusão */}
                        <div className={`absolute inset-0 transition-opacity duration-500 ${currentSlide === 7 ? 'opacity-100' : 'opacity-0'}`}>
                          <div className="flex flex-col items-center justify-center h-full p-6">
                            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-center max-w-lg">
                              <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <Settings size={32} className="text-purple-400" />
                              </div>
                              <h2 className="text-2xl font-bold text-white mb-3">Tutorial Concluído!</h2>
                              <p className="text-blue-200 text-sm mb-6">
                                Agora você domina todas as configurações do sistema: desde configurações gerais, 
                                gerenciamento de empresas, email, backup, segurança até preferências personalizadas.
                              </p>
                              <div className="grid grid-cols-2 gap-3 text-green-300 text-sm">
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Configurações gerais</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Gerenciar empresas</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Configurar email</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Backup e segurança</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Personalizar sistema</span>
                                </div>
                                <div className="flex items-center gap-1">
                                  <div className="w-2 h-2 rounded-full bg-green-400"></div>
                                  <span>Gerenciar preferências</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                          </>
                        )}
                      </div>
                    </div>


                  </div>
                ) : (
                  <div className="text-center p-4">
                    <div className="mb-4">
                      <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${selectedTutorial?.color || 'from-blue-500/20 to-purple-500/20'} flex items-center justify-center mx-auto mb-3 hover:opacity-80 transition-all duration-300 cursor-pointer group`}
                           onClick={() => setIsVideoPlaying(true)}>
                        <Play size={24} className="text-white ml-1 group-hover:scale-110 transition-transform" />
                      </div>
                      <h3 className="text-white text-sm font-semibold mb-2">{selectedTutorial?.title || 'Tutorial Completo'}</h3>
                      <p className="text-blue-200 text-xs mb-3">
                        {selectedTutorial?.description || 'Aprenda a usar todas as funcionalidades'}
                      </p>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs text-blue-300 mb-4">
                      {(selectedTutorial?.features || ['Funcionalidade 1', 'Funcionalidade 2', 'Funcionalidade 3', 'Funcionalidade 4', 'Funcionalidade 5']).map((feature, index) => (
                        <div key={index} className="flex items-center gap-1">
                          <div className="w-1.5 h-1.5 rounded-full bg-green-400"></div>
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                    <button
                      onClick={() => setIsVideoPlaying(true)}
                      className={`bg-gradient-to-r ${selectedTutorial?.color || 'from-blue-600 to-purple-600'} hover:opacity-90 text-white px-4 py-2 rounded-lg text-xs font-medium transition-all duration-200 flex items-center gap-2 mx-auto`}
                    >
                      <Play size={14} />
                      Iniciar Tutorial
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Section cards */}
          <h3 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center">
            <Info className="mr-2 text-primary-500" size={20} />
            Seções do Módulo
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            {/* Users section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Users className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Usuários</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie todos os usuários do sistema, incluindo administradores, funcionários e suas permissões.
                      Você pode criar novos usuários, editar perfis existentes, definir funções e controlar o acesso aos módulos.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Criar, editar e desativar usuários;
                        Gerenciar permissões; Atribuir módulos; Definir funções.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-full">
                      <div className="relative">
                        <div className="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-800/50 flex items-center justify-center">
                          <Users size={32} className="text-blue-500 dark:text-blue-400" />
                        </div>
                        <div className="absolute -bottom-1 -right-1 w-8 h-8 rounded-full bg-green-100 dark:bg-green-800/50 flex items-center justify-center border-2 border-white dark:border-gray-700">
                          <Plus size={16} className="text-green-500 dark:text-green-400" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Professions section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Briefcase className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Profissões</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Configure as profissões e grupos profissionais disponíveis no sistema.
                      Estas profissões são utilizadas para categorizar os usuários e definir suas funções dentro da organização.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Criar e gerenciar profissões;
                        Organizar profissões em grupos; Associar profissões a empresas.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="relative">
                      <div className="w-24 h-16 bg-green-100 dark:bg-green-800/30 rounded-lg flex items-center justify-center">
                        <Briefcase size={24} className="text-green-600 dark:text-green-400" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-10 h-10 bg-blue-100 dark:bg-blue-800/30 rounded-lg flex items-center justify-center transform rotate-12">
                        <Building size={18} className="text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="absolute -bottom-2 -left-2 w-10 h-10 bg-amber-100 dark:bg-amber-800/30 rounded-lg flex items-center justify-center transform -rotate-12">
                        <Users size={18} className="text-amber-600 dark:text-amber-400" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Settings section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Settings className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Configurações</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Ajuste as configurações gerais do sistema, incluindo parâmetros de segurança,
                      configurações de e-mail e outras opções que afetam o funcionamento global da aplicação.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Configurações gerais;
                        Configurações de segurança; Configurações de e-mail; Personalização do sistema.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="relative">
                      <div className="w-20 h-20 rounded-full border-4 border-dashed border-purple-300 dark:border-purple-700 flex items-center justify-center animate-spin-slow">
                        <div className="w-14 h-14 rounded-full bg-purple-100 dark:bg-purple-800/40 flex items-center justify-center">
                          <Settings size={24} className="text-purple-600 dark:text-purple-400" />
                        </div>
                      </div>
                      <style jsx>{`
                        @keyframes spin-slow {
                          from { transform: rotate(0deg); }
                          to { transform: rotate(360deg); }
                        }
                        .animate-spin-slow {
                          animation: spin-slow 20s linear infinite;
                        }
                      `}</style>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Logs section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <FileText className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Logs</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Monitore todas as atividades realizadas no sistema. Os logs registram ações dos usuários,
                      alterações em registros e eventos do sistema, permitindo auditoria completa das operações.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Visualizar histórico de atividades;
                        Filtrar logs por tipo, usuário ou data; Exportar logs para análise.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
                      <div className="space-y-2 w-32">
                        <div className="h-2 bg-amber-200 dark:bg-amber-700 rounded-full w-full"></div>
                        <div className="h-2 bg-amber-200 dark:bg-amber-700 rounded-full w-3/4"></div>
                        <div className="h-2 bg-amber-200 dark:bg-amber-700 rounded-full w-5/6"></div>
                        <div className="h-2 bg-amber-200 dark:bg-amber-700 rounded-full w-2/3"></div>
                        <div className="h-2 bg-amber-200 dark:bg-amber-700 rounded-full w-full"></div>
                        <div className="absolute right-0 bottom-0">
                          <FileText size={20} className="text-amber-500 dark:text-amber-400" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Backup section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Database className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Backup</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie backups do banco de dados e arquivos do sistema. Esta seção permite
                      criar, restaurar e programar backups automáticos para garantir a segurança dos dados.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Criar backups manuais;
                        Configurar backups automáticos; Restaurar dados a partir de backups; Gerenciar histórico de backups.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="relative">
                      <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                        <Database size={24} className="text-red-600 dark:text-red-400" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-red-200 dark:bg-red-800/40 rounded-full flex items-center justify-center">
                        <div className="w-6 h-6 bg-red-300 dark:bg-red-700/50 rounded-full flex items-center justify-center">
                          <div className="w-4 h-4 bg-red-400 dark:bg-red-600/60 rounded-full flex items-center justify-center">
                            <div className="w-2 h-2 bg-red-500 dark:bg-red-500/70 rounded-full"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Dashboard section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <LayoutDashboard className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Dashboard</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Visualize estatísticas e informações gerais sobre o sistema. O dashboard apresenta
                      gráficos, indicadores e dados relevantes para monitorar o desempenho e uso do sistema.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Estatísticas de usuários;
                        Atividades recentes; Distribuição de módulos; Informações do sistema.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded flex items-center justify-center">
                        <BarChart4 size={20} className="text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded flex items-center justify-center">
                        <PieChart size={20} className="text-green-600 dark:text-green-400" />
                      </div>
                      <div className="bg-purple-100 dark:bg-purple-900/30 p-2 rounded flex items-center justify-center">
                        <LineChart size={20} className="text-purple-600 dark:text-purple-400" />
                      </div>
                      <div className="bg-amber-100 dark:bg-amber-900/30 p-2 rounded flex items-center justify-center">
                        <Activity size={20} className="text-amber-600 dark:text-amber-400" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tips section */}
          <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800 overflow-hidden">
            <div className="bg-blue-100 dark:bg-blue-800/30 px-5 py-3 border-b border-blue-200 dark:border-blue-700">
              <div className="flex items-center">
                <Info className="mr-2 text-blue-600 dark:text-blue-400" size={20} />
                <h3 className="font-semibold text-blue-700 dark:text-blue-300">Dicas de Uso</h3>
              </div>
            </div>
            <div className="p-5">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <ul className="space-y-3 text-sm text-blue-700 dark:text-blue-300">
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Comece configurando as profissões e grupos antes de criar usuários</span>
                    </li>
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Verifique regularmente os logs para monitorar atividades suspeitas</span>
                    </li>
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Configure backups automáticos para garantir a segurança dos dados</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <ul className="space-y-3 text-sm text-blue-700 dark:text-blue-300">
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Utilize o dashboard para obter uma visão geral do sistema</span>
                    </li>
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Atribua permissões com cuidado, seguindo o princípio do menor privilégio</span>
                    </li>
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Mantenha-se atualizado sobre as novas funcionalidades do sistema</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="mt-4 bg-white dark:bg-blue-900/40 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                <p className="text-xs text-blue-600 dark:text-blue-300 italic">
                  <span className="font-semibold">Dica Pro:</span> Assista ao vídeo tutorial acima para uma explicação detalhada de como utilizar cada recurso do módulo de administração de forma eficiente.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntroductionPage;
