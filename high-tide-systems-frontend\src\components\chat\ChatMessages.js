'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useChat } from '../../contexts/ChatContext';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import { Trash2, Check, Clock, Send, MessageCircle } from 'lucide-react';
import { ConfirmationDialog } from '../../components/ui';
import { motion, AnimatePresence } from 'framer-motion';
import SharedItemMessage from './SharedItemMessage';
import MessageBubble from './MessageBubble';

const ChatMessages = ({ conversationId, onSharedItemClick }) => {
  const {
    messages,
    loadMessages,
    isLoading,
    conversations,
    deleteMessages
  } = useChat();

  const { user } = useAuth();
  const messagesEndRef = useRef(null);
  const conversationMessages = messages[conversationId] || [];
  
  // Debug das mensagens
  console.log('DEBUG ChatMessages: conversationId =', conversationId);
  console.log('DEBUG ChatMessages: messages =', messages);
  console.log('DEBUG ChatMessages: conversationMessages =', conversationMessages);

  // Estados para gerenciar seleção e exclusão de mensagens
  const [selectedMessages, setSelectedMessages] = useState([]);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Função para alternar seleção de mensagem
  const toggleMessageSelection = useCallback((message) => {
    if (!message || !message.id) return;

    // Verificar se a mensagem é do usuário atual (só pode apagar as próprias mensagens)
    if (message.senderId !== user?.id) {
      alert('Você só pode apagar suas próprias mensagens');
      return;
    }

    // Verificar se a mensagem já está selecionada
    setSelectedMessages(prev => {
      const isSelected = prev.some(m => m.id === message.id);

      if (isSelected) {
        // Remover da seleção
        return prev.filter(m => m.id !== message.id);
      } else {
        // Adicionar à seleção
        return [...prev, message];
      }
    });
  }, [user?.id]);

  // Função para verificar se uma mensagem está selecionada
  const isMessageSelected = useCallback((messageId) => {
    return selectedMessages.some(m => m.id === messageId);
  }, [selectedMessages]);

  // Função para limpar seleção
  const clearSelection = useCallback(() => {
    setSelectedMessages([]);
  }, []);

  // Função para confirmar exclusão de mensagens
  const confirmDeleteMessages = useCallback(() => {
    if (selectedMessages.length === 0) return;

    setConfirmDialogOpen(true);
  }, [selectedMessages]);

  // Função para executar a exclusão de mensagens
  const handleDeleteMessages = useCallback(async () => {
    if (selectedMessages.length === 0) return;

    setIsDeleting(true);

    try {
      const messageIds = selectedMessages.map(m => m.id);
      // Passar o ID da conversa para a função deleteMessages
      const result = await deleteMessages(messageIds, conversationId);

      if (result.success) {
        console.log(`${result.successCount} mensagens apagadas com sucesso`);

        if (result.errorCount > 0) {
          console.warn(`${result.errorCount} mensagens não puderam ser apagadas`);
          alert(`${result.successCount} mensagens foram apagadas, mas ${result.errorCount} não puderam ser apagadas.`);
        }
      } else {
        console.error('Falha ao apagar mensagens');
        alert('Não foi possível apagar as mensagens. Tente novamente mais tarde.');
      }
    } catch (error) {
      console.error('Erro ao apagar mensagens:', error);
      alert('Ocorreu um erro ao apagar as mensagens. Tente novamente mais tarde.');
    } finally {
      setIsDeleting(false);
      setSelectedMessages([]);
      setConfirmDialogOpen(false);
    }
  }, [selectedMessages, deleteMessages, conversationId]);

  // Carregar mensagens quando o componente montar ou o conversationId mudar
  useEffect(() => {
    if (conversationId) {
      loadMessages(conversationId);
    }
  }, [conversationId, loadMessages]);

  // Rolar para a última mensagem quando novas mensagens chegarem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversationMessages.length]);

  // Agrupar mensagens por data
  const groupMessagesByDate = (messages) => {
    if (!messages || messages.length === 0) {
      console.log("Nenhuma mensagem para agrupar");
      return [];
    }

    console.log(`Agrupando ${messages.length} mensagens`);

    try {
      const groups = {};

      // Ordenar mensagens por data (mais antigas primeiro)
      const sortedMessages = [...messages].sort((a, b) =>
        new Date(a.createdAt) - new Date(b.createdAt)
      );

      sortedMessages.forEach(message => {
        if (!message || !message.createdAt) {
          console.warn("Mensagem inválida encontrada:", message);
          return;
        }

        try {
          const messageDate = new Date(message.createdAt);
          const date = messageDate.toDateString();

          if (!groups[date]) {
            groups[date] = [];
          }

          groups[date].push(message);
        } catch (err) {
          console.error("Erro ao processar mensagem:", err, message);
        }
      });

      // Verificar se temos grupos
      if (Object.keys(groups).length === 0) {
        console.warn("Nenhum grupo criado após processamento");
        return [];
      }

      // Ordenar os grupos por data (mais antigos primeiro)
      const result = Object.entries(groups)
        .sort(([dateA], [dateB]) => new Date(dateA) - new Date(dateB))
        .map(([date, messages]) => ({
          date,
          messages
        }));

      console.log(`Criados ${result.length} grupos de mensagens`);
      return result;
    } catch (error) {
      console.error("Erro ao agrupar mensagens:", error);
      return [];
    }
  };

  const messageGroups = groupMessagesByDate(conversationMessages);

  // Formatar hora da mensagem
  const formatMessageTime = (dateString) => {
    return format(new Date(dateString), 'HH:mm', { locale: ptBR });
  };

  // Formatar data do grupo
  const formatGroupDate = (dateString) => {
    const messageDate = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (messageDate.toDateString() === today.toDateString()) {
      return 'Hoje';
    } else if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Ontem';
    } else {
      return format(messageDate, 'dd/MM/yyyy', { locale: ptBR });
    }
  };

  // Encontrar o nome do remetente
  const getSenderName = (senderId) => {
    if (senderId === user?.id) {
      return 'Você';
    }

    // Procurar o remetente na conversa atual
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation) return 'Usuário';

    const sender = conversation.participants?.find(p => p.userId === senderId);
    return sender?.user?.fullName || 'Usuário';
  };

  const messageVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  const bubbleVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: { 
      scale: 1, 
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  if (isLoading && conversationMessages.length === 0) {
    return (
      <div className="messages-loading flex flex-col items-center justify-center h-full bg-gradient-to-b from-transparent to-cyan-50/30 dark:to-gray-800/30">
        <motion.div 
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <div className="relative mb-4">
                    <div className="w-12 h-12 border-4 border-cyan-200 dark:border-cyan-700 rounded-full"></div>
        <div className="absolute top-0 left-0 w-12 h-12 border-4 border-cyan-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
          <p className="text-gray-600 dark:text-gray-400 font-medium">Carregando mensagens...</p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-1">Aguarde um momento</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="messages-container flex-1 overflow-y-auto bg-gradient-to-b from-transparent to-cyan-50/20 dark:to-gray-800/20 relative scrollbar-thin scrollbar-thumb-cyan-300 scrollbar-track-transparent">
      {/* Barra de ações para mensagens selecionadas */}
      <AnimatePresence>
        {selectedMessages.length > 0 && (
          <motion.div 
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed bottom-24 left-1/2 transform -translate-x-1/2 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm shadow-2xl rounded-2xl px-6 py-4 flex items-center gap-4 border border-cyan-200/50 dark:border-cyan-800/50 z-20"
            style={{
              boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
            }}
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-cyan-500 rounded-full"></div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {selectedMessages.length} {selectedMessages.length === 1 ? 'mensagem selecionada' : 'mensagens selecionadas'}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={clearSelection}
                className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200 font-medium"
              >
                Cancelar
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={confirmDeleteMessages}
                className="px-4 py-2 text-sm bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transition-all duration-200 flex items-center gap-2 font-medium shadow-lg"
                disabled={isDeleting}
                style={{
                  boxShadow: "0 4px 14px 0 rgba(239, 68, 68, 0.4)"
                }}
              >
                {isDeleting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Apagando...
                  </>
                ) : (
                  <>
                    <Trash2 size={16} />
                    Apagar
                  </>
                )}
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <div className="p-4 space-y-6">
        {messageGroups.map(group => (
          <motion.div 
            key={group.date} 
            className="message-group"
            variants={messageVariants}
            initial="hidden"
            animate="visible"
          >
            <div className="date-divider flex items-center justify-center my-6">
              <div className="h-px bg-gradient-to-r from-transparent via-cyan-300 dark:via-cyan-600 to-transparent flex-1"></div>
              <span className="px-4 py-1 text-xs font-medium text-gray-500 dark:text-gray-400 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-full border border-cyan-200/50 dark:border-cyan-800/50">
                {formatGroupDate(group.date)}
              </span>
              <div className="h-px bg-gradient-to-r from-transparent via-cyan-300 dark:via-cyan-600 to-transparent flex-1"></div>
            </div>

            <div className="space-y-3">
              {group.messages.map((message, index) => {
                const isCurrentUser = message.senderId === user?.id;
                const isSelected = isMessageSelected(message.id);

                // Verificar se é uma conversa de grupo
                const conversation = conversations.find(c => c.id === conversationId);
                const isGroupChat = conversation?.type === 'GROUP';

                // Obter iniciais do remetente
                const getSenderInitials = (senderId) => {
                  const name = getSenderName(senderId);
                  if (!name) return 'U';

                  try {
                    const names = name.split(' ');
                    if (names.length === 1) return names[0].charAt(0);
                    return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
                  } catch (error) {
                    console.error('Erro ao obter iniciais:', error);
                    return 'U';
                  }
                };

                console.log('DEBUG: Renderizando mensagem', message.id, 'contentType:', message.contentType, message);
                
                // Mensagens compartilhadas têm renderização especial
                if (message.contentType && ['SHARED_APPOINTMENT', 'SHARED_PERSON', 'SHARED_CLIENT', 'SHARED_USER', 'SHARED_SERVICE_TYPE', 'SHARED_LOCATION', 'SHARED_WORKING_HOURS', 'SHARED_INSURANCE', 'SHARED_INSURANCE_LIMIT'].includes(message.contentType)) {
                  console.log('DEBUG: Renderizando como SharedItemMessage', message.id);
                  return (
                    <SharedItemMessage 
                      key={message.id}
                      message={message} 
                      onItemClick={onSharedItemClick}
                    />
                  );
                }
                
                // Mensagens normais e com anexos
                console.log('DEBUG: Renderizando como mensagem normal/anexo', message.id);
                return (
                  <MessageBubble
                    key={message.id}
                    message={message}
                    isCurrentUser={isCurrentUser}
                    isSelected={isSelected}
                    isGroupChat={isGroupChat}
                    senderName={getSenderName(message.senderId)}
                    senderInitials={getSenderInitials(message.senderId)}
                    onToggleSelection={toggleMessageSelection}
                  />
                );
              })}
            </div>
          </motion.div>
        ))}
      </div>

      <div ref={messagesEndRef} />

      {/* Diálogo de confirmação para apagar mensagens */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        onConfirm={handleDeleteMessages}
        title="Apagar mensagens"
        message={`Tem certeza que deseja apagar ${selectedMessages.length} ${selectedMessages.length === 1 ? 'mensagem' : 'mensagens'}? Esta ação não pode ser desfeita.`}
        confirmText="Apagar"
        cancelText="Cancelar"
        variant="danger"
      />
    </div>
  );
};

export default ChatMessages;
