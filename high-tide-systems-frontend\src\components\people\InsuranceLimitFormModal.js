"use client";

import React, { useState, useEffect } from "react";
import { CreditCard, FileText, User, Loader2, AlertCircle } from "lucide-react";
import { ModuleSelect, ModuleInput, ModuleTextarea, ModuleFormGroup } from "@/components/ui";
import { insuranceServiceLimitService } from "@/app/modules/people/services/insuranceServiceLimitService";
import { personsService } from "@/app/modules/people/services/personsService";
import { insurancesService } from "@/app/modules/people/services/insurancesService";
import { serviceTypeService } from "@/app/modules/scheduler/services/serviceTypeService";
import { useToast } from "@/contexts/ToastContext";
import ShareButton from "@/components/common/ShareButton";

const InsuranceLimitFormModal = ({ isOpen, onClose, limit = null, onSuccess, personId = null }) => {
  const { toast_success, toast_error } = useToast();
  const [formData, setFormData] = useState({
    personId: personId || "",
    insuranceId: "",
    serviceTypeId: "",
    monthlyLimit: 0,
    notes: ""
  });
  const [persons, setPersons] = useState([]);
  const [insurances, setInsurances] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [personInsurances, setPersonInsurances] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingOptions, setIsLoadingOptions] = useState(false);
  const [error, setError] = useState(null);

  // Carregar dados iniciais
  useEffect(() => {
    loadOptions();
  }, []);

  // Carregar opções para os selects
  const loadOptions = async () => {
    setIsLoadingOptions(true);
    try {
      // Carregar pessoas (se não tiver personId fixo)
      if (!personId) {
        const personsData = await personsService.getPersons({ limit: 100 });
        const personsArray = personsData?.persons || personsData?.people || personsData?.data || [];
        setPersons(personsArray);
      }

      // Carregar tipos de serviço
      const serviceTypesData = await serviceTypeService.getServiceTypes();
      const serviceTypesArray = serviceTypesData?.serviceTypes || serviceTypesData?.data || [];
      setServiceTypes(serviceTypesArray);

      // Carregar convênios (todos)
      const insurancesData = await insurancesService.getInsurances();
      const insurancesArray = insurancesData?.insurances || insurancesData?.data || [];
      setInsurances(insurancesArray);

      // Se tiver personId, carregar convênios da pessoa
      if (formData.personId) {
        loadPersonInsurances(formData.personId);
      }
    } catch (error) {
      console.error("Erro ao carregar opções:", error);
      toast_error("Erro ao carregar opções. Por favor, tente novamente.");
    } finally {
      setIsLoadingOptions(false);
    }
  };

  // Carregar convênios da pessoa selecionada
  const loadPersonInsurances = async (personId) => {
    if (!personId) return;

    try {
      const data = await insurancesService.listPersonInsurances(personId);
      const personInsurancesArray = data?.insurances || data || [];
      setPersonInsurances(personInsurancesArray);
    } catch (error) {
      console.error("Erro ao carregar convênios da pessoa:", error);
      toast_error("Erro ao carregar convênios da pessoa.");
    }
  };

  // Preencher formulário quando editando
  useEffect(() => {
    if (limit) {
      setFormData({
        personId: limit.personId || "",
        insuranceId: limit.insuranceId || "",
        serviceTypeId: limit.serviceTypeId || "",
        monthlyLimit: limit.monthlyLimit || 0,
        notes: limit.notes || ""
      });

      // Carregar convênios da pessoa se tiver personId
      if (limit.personId) {
        loadPersonInsurances(limit.personId);
      }
    } else if (personId) {
      setFormData(prev => ({
        ...prev,
        personId
      }));
      loadPersonInsurances(personId);
    }
  }, [limit, personId]);

  // Atualizar convênios quando a pessoa mudar
  useEffect(() => {
    if (formData.personId) {
      loadPersonInsurances(formData.personId);
    }
  }, [formData.personId]);

  // Manipuladores de eventos
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Converter para número quando for monthlyLimit
    if (name === "monthlyLimit") {
      setFormData({
        ...formData,
        [name]: parseInt(value, 10) || 0
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    // Validar dados
    if (!formData.personId || !formData.insuranceId || !formData.serviceTypeId) {
      setError("Por favor, preencha todos os campos obrigatórios.");
      setIsSubmitting(false);
      return;
    }

    try {
      if (limit) {
        // Modo de edição
        await insuranceServiceLimitService.updateLimit(limit.id, {
          monthlyLimit: formData.monthlyLimit,
          notes: formData.notes
        });
        toast_success("Limite de convênio atualizado com sucesso");
      } else {
        // Modo de adição
        await insuranceServiceLimitService.createLimit(formData);
        toast_success("Limite de convênio criado com sucesso");
      }

      onSuccess();
      onClose();
    } catch (err) {
      console.error("Erro ao salvar limite de convênio:", err);
      setError(err.response?.data?.message || "Ocorreu um erro ao salvar o limite de convênio.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Normalizar dados de convênios da pessoa
  const normalizePersonInsurances = () => {
    if (!personInsurances || !Array.isArray(personInsurances) || personInsurances.length === 0) return [];

    return personInsurances.map(ins => {
      if (!ins) return null;
      // Se o objeto já tiver a estrutura correta
      if (ins.insurance) {
        return {
          id: ins.insurance.id,
          name: ins.insurance.name
        };
      }
      // Se for um objeto de convênio direto
      else if (ins.id && ins.name) {
        return {
          id: ins.id,
          name: ins.name
        };
      }
      // Se tiver apenas o ID do convênio
      else if (ins.insuranceId) {
        // Buscar o nome no array de todos os convênios
        const insuranceDetails = Array.isArray(insurances) ? insurances.find(i => i?.id === ins.insuranceId) : null;
        return {
          id: ins.insuranceId,
          name: insuranceDetails?.name || `Convênio ${ins.insuranceId}`
        };
      }
      return null;
    }).filter(Boolean);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo escuro */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="fixed left-[50%] top-[50%] z-[12050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-orange-300 dark:border-orange-600 bg-background shadow-lg duration-200 rounded-xl max-w-2xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="pb-4 border-b-2 border-orange-400 dark:border-orange-500 flex-shrink-0 px-6 pt-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg text-white">
              <CreditCard className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-orange-800 dark:text-white border-l-4 border-orange-400 dark:border-orange-500 pl-3">
                {limit ? 'Editar Limite de Convênio' : 'Novo Limite de Convênio'}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                {limit ? 'Modifique as informações do limite' : 'Preencha as informações para criar um novo limite'}
              </p>
            </div>
            {limit && (
              <div className="ml-auto">
                <ShareButton
                  itemType="insurance-limit"
                  itemId={limit.id}
                  itemTitle={`Limite: ${limit?.Person?.fullName || limit?.person?.fullName || 'Paciente'}`}
                  size="sm"
                  variant="ghost"
                />
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(90vh-120px)]">
          {/* Form Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <form id="insurance-limit-form" onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2">
                  <AlertCircle size={16} />
                  <span>{error}</span>
                </div>
              )}

              {/* Seleção de Pessoa (se não tiver personId fixo) */}
              {!personId && (
                <ModuleFormGroup
                  moduleColor="people"
                  label="Paciente *"
                  htmlFor="personId"
                  icon={<User size={16} />}
                  required
                >
                  <ModuleSelect
                    moduleColor="people"
                    id="personId"
                    name="personId"
                    value={formData.personId}
                    onChange={handleChange}
                    disabled={isLoadingOptions || isSubmitting || !!limit}
                    required
                    placeholder="Selecione um paciente"
                  >
                    {Array.isArray(persons) && persons.map((person) => (
                      <option key={person?.id} value={person?.id}>
                        {person?.fullName || 'Sem nome'}
                      </option>
                    ))}
                  </ModuleSelect>
                </ModuleFormGroup>
              )}

              {/* Seleção de Convênio */}
              <ModuleFormGroup
                moduleColor="people"
                label="Convênio *"
                htmlFor="insuranceId"
                icon={<CreditCard size={16} />}
                required
                helpText={formData.personId && normalizePersonInsurances().length === 0 ? "Este paciente não possui convênios associados." : ""}
              >
                <ModuleSelect
                  moduleColor="people"
                  id="insuranceId"
                  name="insuranceId"
                  value={formData.insuranceId}
                  onChange={handleChange}
                  disabled={isLoadingOptions || isSubmitting || !formData.personId || !!limit}
                  required
                  placeholder="Selecione um convênio"
                >
                  {Array.isArray(normalizePersonInsurances()) && normalizePersonInsurances().map((insurance) => (
                    <option key={insurance?.id} value={insurance?.id}>
                      {insurance?.name || 'Sem nome'}
                    </option>
                  ))}
                </ModuleSelect>
              </ModuleFormGroup>

              {/* Seleção de Tipo de Serviço */}
              <ModuleFormGroup
                moduleColor="people"
                label="Tipo de Serviço *"
                htmlFor="serviceTypeId"
                icon={<FileText size={16} />}
                required
              >
                <ModuleSelect
                  moduleColor="people"
                  id="serviceTypeId"
                  name="serviceTypeId"
                  value={formData.serviceTypeId}
                  onChange={handleChange}
                  disabled={isLoadingOptions || isSubmitting || !!limit}
                  required
                  placeholder="Selecione um tipo de serviço"
                >
                  {Array.isArray(serviceTypes) && serviceTypes.map((serviceType) => (
                    <option key={serviceType?.id} value={serviceType?.id}>
                      {serviceType?.name || 'Sem nome'}
                    </option>
                  ))}
                </ModuleSelect>
              </ModuleFormGroup>

              {/* Limite Mensal */}
              <ModuleFormGroup
                moduleColor="people"
                label="Limite Mensal"
                htmlFor="monthlyLimit"
                icon={<CreditCard size={16} />}
                helpText="0 = ilimitado"
              >
                <ModuleInput
                  moduleColor="people"
                  type="number"
                  id="monthlyLimit"
                  name="monthlyLimit"
                  value={formData.monthlyLimit}
                  onChange={handleChange}
                  min="0"
                  disabled={isSubmitting}
                  placeholder="Digite o limite mensal"
                />
              </ModuleFormGroup>

              {/* Observações */}
              <ModuleFormGroup
                moduleColor="people"
                label="Observações"
                htmlFor="notes"
                icon={<FileText size={16} />}
              >
                <ModuleTextarea
                  moduleColor="people"
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  disabled={isSubmitting}
                  placeholder="Observações adicionais sobre o limite"
                />
              </ModuleFormGroup>
            </form>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 border-t-2 border-gray-300 dark:border-gray-600 pt-4 flex-shrink-0 px-6 pb-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              Cancelar
            </button>
            <button
              type="submit"
              form="insurance-limit-form"
              className="px-4 py-2 bg-orange-500 dark:bg-orange-600 text-white rounded-lg hover:bg-orange-600 dark:hover:bg-orange-700 transition-colors flex items-center gap-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  <CreditCard size={16} />
                  <span>{limit ? "Atualizar" : "Salvar"}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsuranceLimitFormModal;
