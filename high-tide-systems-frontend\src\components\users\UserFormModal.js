"use client";

import React, { useState, useEffect, useRef } from "react";
import { User, Users, Building, Shield, AlertCircle, CheckCircle, Info, Loader2, FileText, UserCog, Lock, X } from "lucide-react";
import { userService } from "@/app/modules/admin/services/userService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { branchService } from "@/app/modules/admin/services/branchService";
import { professionsService } from "@/app/modules/admin/services/professionsService";
import { useToast } from "@/contexts/ToastContext";
import { usePreferences } from "@/hooks/usePreferences";
import { getAllPermissions } from "@/utils/permissionConfig";
import { api } from "@/utils/api";
import { validateBirthDate } from "@/utils/dateUtils";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog.jsx";
import Button from "@/components/ui/Button";
import Input from "@/components/ui/Input";
import Label from "@/components/ui/Label";
import CustomSelect from "@/components/ui/CustomSelect";

// Componentes das tabs
import UserFormHeader from "./UserFormHeader";
import UserFormFooter from "./UserFormFooter";
import UserInfoTab from "./UserInfoTab";
import UserRoleTab from "./UserRoleTab";
import UserModulesTab from "./UserModulesTab";
import UserPermissionsTab from "./UserPermissionsTab";
import UserDocumentsTab from "./UserDocumentsTab";

const UserFormModal = ({ isOpen, onClose, user, onSuccess, currentUser }) => {
  const profileImageUploadRef = useRef(null);
  const { toast_success, toast_error, toast_warning } = useToast();
  const { getRequiredFieldsForValidation } = usePreferences();
  
  // Estados principais
  const [activeTab, setActiveTab] = useState("info");
  const [savedUserId, setSavedUserId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  
  // Estados do formulário
  const [formData, setFormData] = useState({
    login: "",
    email: "",
    fullName: "",
    password: "",
    confirmPassword: "",
    cpf: "",
    cnpj: "",
    documentType: "cpf",
    birthDate: "",
    address: "",
    neighborhood: "",
    city: "",
    state: "",
    postalCode: "",
    phone: "",
    companyId: "",
    branchId: "",
    professionId: ""
  });

  // Estados para dados externos
  const [companies, setCompanies] = useState([]);
  const [loadingCompanies, setLoadingCompanies] = useState(false);
  
  const [branches, setBranches] = useState([]);
  const [loadingBranches, setLoadingBranches] = useState(false);
  const [professions, setProfessions] = useState([]);
  const [loadingProfessions, setLoadingProfessions] = useState(false);
  const [selectedProfession, setSelectedProfession] = useState(null);
  const [professionDefaultModules, setProfessionDefaultModules] = useState([]);
  const [professionDefaultPermissions, setProfessionDefaultPermissions] = useState([]);
  const [isUsingProfessionDefaults, setIsUsingProfessionDefaults] = useState(true);

  // Estados para as tabs adicionais
  const [selectedModules, setSelectedModules] = useState([]);
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const [selectedRole, setSelectedRole] = useState("EMPLOYEE");

  // Estados para controlar quais abas estão disponíveis
  const [availableTabs, setAvailableTabs] = useState({
    info: true,
    documents: false,
    role: false,
    modules: false,
    permissions: false
  });

  // Estado para documentos temporários
  const [tempDocuments, setTempDocuments] = useState([]);

  // Estados específicos para a tab de permissões
  const [expandedModules, setExpandedModules] = useState({});
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPermissions, setFilteredPermissions] = useState([]);

  // Verificações de permissão
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";
  const isAdmin = currentUser?.modules?.includes("ADMIN");
  const isEditing = !!user;

  // Formatação de campos
  const formatCpf = (value) => {
    if (!value) return "";
    const digits = String(value).replace(/\D/g, "");
    const cleaned = digits.slice(0, 11);
    
    if (cleaned.length <= 3) {
      return cleaned;
    } else if (cleaned.length <= 6) {
      return `${cleaned.slice(0, 3)}.${cleaned.slice(3)}`;
    } else if (cleaned.length <= 9) {
      return `${cleaned.slice(0, 3)}.${cleaned.slice(3, 6)}.${cleaned.slice(6)}`;
    } else {
      return `${cleaned.slice(0, 3)}.${cleaned.slice(3, 6)}.${cleaned.slice(6, 9)}-${cleaned.slice(9, 11)}`;
    }
  };

  const formatCnpj = (value) => {
    if (!value) return "";
    const digits = String(value).replace(/\D/g, "");
    const cleaned = digits.slice(0, 14);
    
    if (cleaned.length <= 2) {
      return cleaned;
    } else if (cleaned.length <= 5) {
      return `${cleaned.slice(0, 2)}.${cleaned.slice(2)}`;
    } else if (cleaned.length <= 8) {
      return `${cleaned.slice(0, 2)}.${cleaned.slice(2, 5)}.${cleaned.slice(5)}`;
    } else if (cleaned.length <= 12) {
      return `${cleaned.slice(0, 2)}.${cleaned.slice(2, 5)}.${cleaned.slice(5, 8)}/${cleaned.slice(8)}`;
    } else {
      return `${cleaned.slice(0, 2)}.${cleaned.slice(2, 5)}.${cleaned.slice(5, 8)}/${cleaned.slice(8, 12)}-${cleaned.slice(12, 14)}`;
    }
  };

  const formatPhone = (value) => {
    if (!value) return "";
    const digits = String(value).replace(/\D/g, "");
    const cleaned = digits.slice(0, 11);
    
    if (cleaned.length <= 2) {
      return cleaned.length ? `(${cleaned}` : "";
    } else if (cleaned.length <= 7) {
      return `(${cleaned.slice(0, 2)}) ${cleaned.slice(2)}`;
    } else {
      return `(${cleaned.slice(0, 2)}) ${cleaned.slice(2, 7)}-${cleaned.slice(7)}`;
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    if (isSystemAdmin && isOpen) {
      loadCompanies();
    }
    if (isOpen) {
      loadProfessions();
    }
  }, [isSystemAdmin, isOpen]);

  // Carregar unidades quando a empresa mudar
  useEffect(() => {
    if (formData.companyId) {
      loadBranches(formData.companyId);
    } else {
      setBranches([]);
    }
  }, [formData.companyId]);

  // Inicializar dados do usuário
  useEffect(() => {
    if (user) {
      setFormData({
        login: user?.login || "",
        email: user?.email || "",
        fullName: user?.fullName || "",
        password: "",
        confirmPassword: "",
        cpf: user?.cpf ? formatCpf(user.cpf) : "",
        cnpj: user?.cnpj ? formatCnpj(user.cnpj) : "",
        documentType: user?.cpf ? "cpf" : "cnpj",
        birthDate: user?.birthDate ? new Date(user.birthDate).toISOString().split('T')[0] : "",
        address: user?.address || "",
        neighborhood: user?.neighborhood || "",
        city: user?.city || "",
        state: user?.state || "",
        postalCode: user?.postalCode || "",
        phone: user?.phone ? formatPhone(user.phone) : "",
        companyId: user?.companyId || "",
        branchId: user?.branchId || "",
        professionId: user?.professionObj?.id || "",
        profileImageUrl: user?.profileImageFullUrl || ""
      });

      setSelectedModules(user.modules || ["BASIC"]);
      setSelectedPermissions(user.permissions || []);
      setSelectedRole(user.role || "EMPLOYEE");
      setSavedUserId(user.id);

      if (user?.professionObj?.id) {
        loadProfessionDetails(user.professionObj.id);
      }

      const initialExpandedState = {};
      user.modules?.forEach((moduleId) => {
        initialExpandedState[moduleId] = true;
      });
      setExpandedModules(initialExpandedState);
      setFilteredPermissions(getAllPermissions());

      setAvailableTabs({
        info: true,
        documents: true,
        role: true,
        modules: true,
        permissions: true
      });

      setIsUsingProfessionDefaults(false);
    } else {
      resetForm();
      if (!isSystemAdmin && currentUser?.companyId) {
        setFormData(prev => ({
          ...prev,
          companyId: currentUser.companyId
        }));
      }

      setSelectedModules(["BASIC"]);
      setSelectedPermissions([]);
      setSelectedRole("EMPLOYEE");
      setSavedUserId(null);

      setFilteredPermissions(getAllPermissions());
      setExpandedModules({ BASIC: true });

      setAvailableTabs({
        info: true,
        documents: false,
        role: false,
        modules: false,
        permissions: false
      });

      setIsUsingProfessionDefaults(true);
    }
  }, [user, isOpen, currentUser, isSystemAdmin]);

  // Filtragem de permissões baseada na busca
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredPermissions(getAllPermissions());
      return;
    }

    const lowerSearch = searchTerm.toLowerCase();
    const filtered = getAllPermissions().filter(
      (permission) =>
        permission.name.toLowerCase().includes(lowerSearch) ||
        permission.description.toLowerCase().includes(lowerSearch) ||
        permission.id.toLowerCase().includes(lowerSearch)
    );

    setFilteredPermissions(filtered);

    const modulesToExpand = {};
    filtered.forEach((permission) => {
      modulesToExpand[permission.moduleId] = true;
    });

    setExpandedModules((prev) => ({
      ...prev,
      ...modulesToExpand,
    }));
  }, [searchTerm]);


  const loadCompanies = async () => {
    setLoadingCompanies(true);
    try {
      const companies = await companyService.getCompaniesForSelect();
      
      // Fallback: try regular companies endpoint if select endpoint is empty
      if (!companies || companies.length === 0) {
        try {
          const fallbackResponse = await companyService.getCompanies({ limit: 50 });
          if (fallbackResponse?.companies && fallbackResponse.companies.length > 0) {
            setCompanies(fallbackResponse.companies);
            return;
          }
        } catch (fallbackError) {
          console.error("Fallback also failed:", fallbackError);
        }
      }
      
      setCompanies(companies || []);

      if (isSystemAdmin && !formData.companyId && companies && companies.length > 0) {
        setFormData(prev => ({
          ...prev,
          companyId: companies[0].id
        }));
      }
    } catch (error) {
      console.error("Error loading companies:", error);
      setErrors(prev => ({
        ...prev,
        companies: "Não foi possível carregar a lista de empresas"
      }));
      setCompanies([]);
    } finally {
      setLoadingCompanies(false);
    }
  };

  const loadBranches = async (companyId) => {
    if (!companyId) return;

    setLoadingBranches(true);
    try {
      const response = await branchService.getBranches({
        companyId,
        active: true,
        limit: 100
      });

      setBranches(response.branches || []);
    } catch (error) {
      console.error("Erro ao carregar unidades:", error);
      setErrors(prev => ({
        ...prev,
        branches: "Não foi possível carregar a lista de unidades"
      }));
      setBranches([]);
    } finally {
      setLoadingBranches(false);
    }
  };

  const loadProfessions = async () => {
    setLoadingProfessions(true);
    try {
      const data = await professionsService.getProfessions({ active: true });
      setProfessions(data || []);
    } catch (error) {
      console.error("Erro ao carregar profissões:", error);
      setErrors(prev => ({
        ...prev,
        professions: "Não foi possível carregar a lista de profissões"
      }));
      setProfessions([]);
    } finally {
      setLoadingProfessions(false);
    }
  };

  const loadProfessionDetails = async (professionId) => {
    if (!professionId) {
      setSelectedProfession(null);
      setProfessionDefaultModules(["BASIC"]);
      setProfessionDefaultPermissions([]);
      return;
    }

    try {
      const profession = await professionsService.getProfessionById(professionId);
      setSelectedProfession(profession);

      if (profession?.group) {
        setProfessionDefaultModules(profession.group.defaultModules || ["BASIC"]);
        setProfessionDefaultPermissions(profession.group.defaultPermissions || []);

        if (isUsingProfessionDefaults && !user) {
          setSelectedModules(profession.group.defaultModules || ["BASIC"]);
          setSelectedPermissions(profession.group.defaultPermissions || []);
        }
      } else {
        setProfessionDefaultModules(["BASIC"]);
        setProfessionDefaultPermissions([]);
      }
    } catch (error) {
      console.error("Erro ao carregar detalhes da profissão:", error);
      setSelectedProfession(null);
      setProfessionDefaultModules(["BASIC"]);
      setProfessionDefaultPermissions([]);
    }
  };

  const resetForm = () => {
    setFormData({
      login: "",
      email: "",
      fullName: "",
      password: "",
      confirmPassword: "",
      cpf: "",
      cnpj: "",
      documentType: "cpf",
      birthDate: "",
      address: "",
      neighborhood: "",
      city: "",
      state: "",
      postalCode: "",
      phone: "",
      companyId: "",
      branchId: "",
      professionId: "",
      profileImageUrl: ""
    });
    setErrors({});
    setSelectedModules(["BASIC"]);
    setSelectedPermissions([]);
    setSelectedRole("EMPLOYEE");
    setSavedUserId(null);
    setActiveTab("info");

    setAvailableTabs({
      info: true,
      documents: false,
      role: false,
      modules: false,
      permissions: false
    });

    setTempDocuments([]);
  };

  const validateForm = () => {
    const newErrors = {};
    const requiredFields = getRequiredFieldsForValidation('user');

    if (!formData.login) newErrors.login = "Login é obrigatório";
    if (!formData.fullName) newErrors.fullName = "Nome completo é obrigatório";

    if (!formData.email) {
      newErrors.email = "Email é obrigatório";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }

    if (!user || formData.password) {
      if (!formData.password) {
        newErrors.password = "Senha é obrigatória";
      } else if (formData.password.length < 6) {
        newErrors.password = "Senha deve ter no mínimo 6 caracteres";
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = "Senhas não conferem";
      }
    }

    if (requiredFields.userCpfCnpj && !formData.cpf && !formData.cnpj) {
      newErrors.cpf = "CPF/CNPJ é obrigatório";
    }

    if (requiredFields.userPhone && !formData.phone) {
      newErrors.phone = "Telefone é obrigatório";
    }

    if (requiredFields.userBirthDate && !formData.birthDate) {
      newErrors.birthDate = "Data de nascimento é obrigatória";
    }

    if (requiredFields.userCep && !formData.postalCode) {
      newErrors.postalCode = "CEP é obrigatório";
    }

    if (requiredFields.userRole && !selectedRole) {
      newErrors.role = "Perfil de acesso é obrigatório";
    }

    if (requiredFields.userUnit && !formData.branchId) {
      newErrors.branchId = "Unidade é obrigatória";
    }

    if (formData.documentType === "cpf" && formData.cpf) {
      const cleanCpf = formData.cpf.replace(/\D/g, "");
      if (cleanCpf.length !== 11) {
        newErrors.cpf = "CPF deve ter 11 dígitos";
      }
    }

    if (formData.documentType === "cnpj" && formData.cnpj) {
      const cleanCnpj = formData.cnpj.replace(/\D/g, "");
      if (cleanCnpj.length !== 14) {
        newErrors.cnpj = "CNPJ deve ter 14 dígitos";
      }
    }

    if (isSystemAdmin && !formData.companyId) {
      newErrors.companyId = "Empresa é obrigatória";
    }

    if (formData.professionId && formData.professionId.trim() === "") {
      newErrors.professionId = "Selecione uma profissão válida";
    }

    if (formData.birthDate) {
      const validation = validateBirthDate(formData.birthDate);
      if (!validation.isValid) {
        newErrors.birthDate = validation.message;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === "cpf") {
      setFormData(prev => ({ ...prev, cpf: formatCpf(value) }));
    } else if (name === "cnpj") {
      setFormData(prev => ({ ...prev, cnpj: formatCnpj(value) }));
    } else if (name === "phone") {
      setFormData(prev => ({ ...prev, phone: formatPhone(value) }));
    } else if (name === "professionId") {
      setFormData(prev => ({
        ...prev,
        professionId: value === '' ? null : value
      }));
      if (value && value !== '') {
        loadProfessionDetails(value);
      } else {
        setSelectedProfession(null);
        setProfessionDefaultModules([]);
        setProfessionDefaultPermissions([]);
      }
    } else if (name === "birthDate") {
      setFormData(prev => ({ ...prev, [name]: value }));

      if (value) {
        const validation = validateBirthDate(value);
        if (!validation.isValid) {
          setErrors(prev => ({ ...prev, birthDate: validation.message }));
        } else {
          setErrors(prev => ({ ...prev, birthDate: undefined }));
        }
      } else {
        setErrors(prev => ({ ...prev, birthDate: undefined }));
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    if (errors[name] && name !== "birthDate") {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleDocumentTypeChange = (value) => {
    setFormData((prev) => ({
      ...prev,
      documentType: value,
      cpf: value === "cpf" ? prev.cpf : "",
      cnpj: value === "cnpj" ? prev.cnpj : ""
    }));
  };

  const handleSelectChange = (name, value) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: undefined }));
    }
  };

  const handleImageUploaded = (imageUrl) => {
    if (imageUrl) {
      setFormData(prev => ({
        ...prev,
        profileImageUrl: imageUrl
      }));
    }
  };

  const handleTabChange = (tab) => {
    setErrors({});

    if (!availableTabs[tab]) {
      toast_warning({
        title: "Aba não disponível",
        message: "Complete a etapa atual antes de avançar para a próxima"
      });
      return;
    }

    setActiveTab(tab);
  };

  const handleBack = () => {
    const prevTab = {
      documents: "info",
      role: "documents",
      modules: "role",
      permissions: "modules"
    }[activeTab];

    if (prevTab) {
      setActiveTab(prevTab);
      setErrors({});
    }
  };

  const toggleModuleExpansion = (moduleId) => {
    setExpandedModules((prev) => ({
      ...prev,
      [moduleId]: !prev[moduleId],
    }));
  };

  const togglePermission = (permissionId) => {
    setSelectedPermissions((prev) => {
      if (prev.includes(permissionId)) {
        return prev.filter((id) => id !== permissionId);
      } else {
        return [...prev, permissionId];
      }
    });
  };

  const toggleModulePermissions = (moduleId) => {
    const modulePermissions = filteredPermissions.filter(
      (p) => p.moduleId === moduleId && !p.id.startsWith('notifications.')
    );
    
    const modulePermissionIds = modulePermissions.map(p => p.id);
    const allSelected = modulePermissionIds.every(id => selectedPermissions.includes(id));
    
    if (allSelected) {
      // Desmarcar todas as permissões do módulo
      setSelectedPermissions(prev => 
        prev.filter(id => !modulePermissionIds.includes(id))
      );
    } else {
      // Marcar todas as permissões do módulo
      setSelectedPermissions(prev => 
        [...new Set([...prev, ...modulePermissionIds])]
      );
    }
  };

  const handleSubmit = async (e) => {
    if (e) e.preventDefault();

    if (activeTab === "info") {
      const isValid = validateForm();
      if (!isValid) {
        return;
      }
    }

    setIsLoading(true);

    try {
      if (user || savedUserId) {
        // Handle editing existing user
        const targetId = user?.id || savedUserId;

        if (activeTab === "info") {
          const payload = {
            login: formData.login,
            email: formData.email,
            fullName: formData.fullName,
            ...(formData.password && { password: formData.password }),
            ...(formData.documentType === "cpf" && formData.cpf ? { cpf: formData.cpf.replace(/\D/g, "") } : {}),
            ...(formData.documentType === "cnpj" && formData.cnpj ? { cnpj: formData.cnpj.replace(/\D/g, "") } : {}),
            ...(formData.birthDate && { birthDate: formData.birthDate }),
            ...(formData.address && { address: formData.address }),
            ...(formData.neighborhood && { neighborhood: formData.neighborhood }),
            ...(formData.city && { city: formData.city }),
            ...(formData.state && { state: formData.state }),
            ...(formData.postalCode && { postalCode: formData.postalCode.replace(/\D/g, "") }),
            ...(formData.phone && { phone: formData.phone.replace(/\D/g, "") }),
            ...(formData.professionId && { professionId: formData.professionId }),
            ...(isSystemAdmin && formData.companyId && { companyId: formData.companyId })
          };

          await userService.update(targetId, payload);
          toast_success({
            title: "Usuário atualizado",
            message: "O usuário foi atualizado com sucesso"
          });
          onSuccess();
        } else if (activeTab === "modules") {
          const modulesToSave = selectedModules.includes("BASIC")
            ? selectedModules
            : [...selectedModules, "BASIC"];

          await userService.updateModules(targetId, modulesToSave);
          toast_success({
            title: "Módulos atualizados",
            message: "Os módulos do usuário foram atualizados com sucesso"
          });
          onSuccess();
        } else if (activeTab === "permissions") {
          console.log(`[handleSubmit] Atualizando permissões do usuário ${targetId}`);
          console.log(`[handleSubmit] Permissões selecionadas:`, selectedPermissions);
          await userService.updatePermissions(targetId, selectedPermissions);
          toast_success({
            title: "Permissões atualizadas",
            message: "As permissões do usuário foram atualizadas com sucesso"
          });
          onSuccess();
        } else if (activeTab === "role") {
          await userService.updateRole(targetId, selectedRole);
          toast_success({
            title: "Função atualizada",
            message: "A função do usuário foi atualizada com sucesso"
          });
          onSuccess();
        }
      } else {
        // Handle creating new user
        if (activeTab === "info") {
          setAvailableTabs(prev => ({
            ...prev,
            documents: true
          }));
          setActiveTab("documents");
          toast_success({
            title: "Informações válidas",
            message: "Informações básicas validadas com sucesso"
          });
        } else if (activeTab === "documents") {
          setAvailableTabs(prev => ({
            ...prev,
            role: true
          }));
          setActiveTab("role");
          toast_success({
            title: "Documentos adicionados",
            message: tempDocuments.length > 0
              ? `${tempDocuments.length} documento(s) adicionado(s) com sucesso`
              : "Você pode continuar sem adicionar documentos"
          });
        } else if (activeTab === "role") {
          if (selectedRole === "SYSTEM_ADMIN" || selectedRole === "COMPANY_ADMIN") {
            // Create user directly for admin roles
            const payload = {
              login: formData.login,
              email: formData.email,
              fullName: formData.fullName,
              password: formData.password,
              ...(formData.documentType === "cpf" && formData.cpf ? { cpf: formData.cpf.replace(/\D/g, "") } : {}),
              ...(formData.documentType === "cnpj" && formData.cnpj ? { cnpj: formData.cnpj.replace(/\D/g, "") } : {}),
              ...(formData.birthDate && { birthDate: formData.birthDate }),
              ...(formData.address && { address: formData.address }),
              ...(formData.neighborhood && { neighborhood: formData.neighborhood }),
              ...(formData.city && { city: formData.city }),
              ...(formData.state && { state: formData.state }),
              ...(formData.postalCode && { postalCode: formData.postalCode.replace(/\D/g, "") }),
              ...(formData.phone && { phone: formData.phone.replace(/\D/g, "") }),
              ...(formData.professionId && { professionId: formData.professionId }),
              companyId: isSystemAdmin ? formData.companyId : currentUser?.companyId,
              role: currentUser?.role === "EMPLOYEE" ? "EMPLOYEE" : selectedRole,
              modules: [],
              permissions: []
            };

            const response = await userService.create(payload);
            setSavedUserId(response.id);

            // Handle document upload if needed
            if (tempDocuments.length > 0 && response.id) {
              try {
                const formData = new FormData();
                tempDocuments.forEach(doc => {
                  formData.append("documents", doc.file);
                });

                const documentTypes = tempDocuments.map(doc => doc.type);
                formData.append("types", JSON.stringify(documentTypes));

                await api.post(`/documents/upload?targetId=${response.id}&targetType=user`, formData, {
                  headers: {
                    "Content-Type": "multipart/form-data",
                  },
                });
              } catch (docError) {
                console.error("Erro ao fazer upload dos documentos:", docError);
                toast_warning({
                  title: "Atenção",
                  message: "Usuário criado, mas houve um erro ao enviar os documentos."
                });
              }
            }

            toast_success({
              title: "Usuário criado",
              message: "O usuário administrador foi criado com sucesso"
            });

            onSuccess();
          } else {
            setAvailableTabs(prev => ({
              ...prev,
              modules: true
            }));
            setActiveTab("modules");
            toast_success({
              title: "Função selecionada",
              message: "Função selecionada com sucesso"
            });
          }
        } else if (activeTab === "modules") {
          setAvailableTabs(prev => ({
            ...prev,
            permissions: true
          }));
          setActiveTab("permissions");
          toast_success({
            title: "Módulos selecionados",
            message: "Módulos selecionados com sucesso"
          });
        } else if (activeTab === "permissions") {
          // Final step - create user with all information
          const payload = {
            login: formData.login,
            email: formData.email,
            fullName: formData.fullName,
            password: formData.password,
            ...(formData.documentType === "cpf" && formData.cpf ? { cpf: formData.cpf.replace(/\D/g, "") } : {}),
            ...(formData.documentType === "cnpj" && formData.cnpj ? { cnpj: formData.cnpj.replace(/\D/g, "") } : {}),
            ...(formData.birthDate && { birthDate: formData.birthDate }),
            ...(formData.address && { address: formData.address }),
            ...(formData.neighborhood && { neighborhood: formData.neighborhood }),
            ...(formData.city && { city: formData.city }),
            ...(formData.state && { state: formData.state }),
            ...(formData.postalCode && { postalCode: formData.postalCode.replace(/\D/g, "") }),
            ...(formData.phone && { phone: formData.phone.replace(/\D/g, "") }),
            ...(formData.professionId && { professionId: formData.professionId }),
            companyId: isSystemAdmin ? formData.companyId : currentUser?.companyId,
            role: currentUser?.role === "EMPLOYEE" ? "EMPLOYEE" : selectedRole
          };

          const modulesToSave = selectedModules.includes("BASIC")
            ? selectedModules
            : [...selectedModules, "BASIC"];

          payload.modules = modulesToSave;
          payload.permissions = selectedPermissions;

          console.log(`[handleSubmit] Criando usuário com payload:`, payload);
          console.log(`[handleSubmit] Módulos:`, payload.modules);
          console.log(`[handleSubmit] Permissões:`, payload.permissions);
          
          const response = await userService.create(payload);
          setSavedUserId(response.id);

          // Handle document upload if needed
          if (tempDocuments.length > 0 && response.id) {
            try {
              const formData = new FormData();
              tempDocuments.forEach(doc => {
                formData.append("documents", doc.file);
              });

              const documentTypes = tempDocuments.map(doc => doc.type);
              formData.append("types", JSON.stringify(documentTypes));

              await api.post(`/documents/upload?targetId=${response.id}&targetType=user`, formData, {
                headers: {
                  "Content-Type": "multipart/form-data",
                },
              });
            } catch (docError) {
              console.error("Erro ao fazer upload dos documentos:", docError);
              toast_warning({
                title: "Atenção",
                message: "Usuário criado, mas houve um erro ao enviar os documentos."
              });
            }
          }

          toast_success({
            title: "Usuário criado",
            message: "O usuário foi criado com sucesso"
          });

          onSuccess();
        }
      }
    } catch (error) {
      console.error("Erro ao salvar usuário:", error);
      const apiErrors = error.response?.data?.errors;
      const errorMessage = error.response?.data?.message || "Erro ao salvar usuário";

      toast_error({
        title: "Erro ao salvar usuário",
        message: errorMessage
      });

      if (apiErrors) {
        const formattedErrors = {};
        apiErrors.forEach(err => {
          formattedErrors[err.param] = err.msg;
        });
        setErrors(formattedErrors);
      } else {
        setErrors({});
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader className="pb-4 border-b-2 border-gray-400 dark:border-gray-500 flex-shrink-0">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-slate-500 to-slate-600 rounded-lg text-white">
              <User className="h-5 w-5" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold text-slate-800 dark:text-white border-l-4 border-gray-400 dark:border-gray-500 pl-3">
                {user ? 'Editar Usuário' : 'Novo Usuário'}
              </DialogTitle>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                {user ? 'Modifique as informações do usuário' : 'Preencha as informações para criar um novo usuário'}
              </p>
            </div>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(90vh-120px)]">
          {/* Tabs */}
          <div className="flex border-b-2 border-gray-400 dark:border-gray-500 flex-shrink-0">
            <button
              onClick={() => handleTabChange("info")}
              className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-t-lg ${
                activeTab === "info"
                  ? "border-b-2 border-slate-500 text-slate-700 dark:text-slate-300 bg-slate-100 dark:bg-slate-800 font-semibold"
                  : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
              }`}
              disabled={isLoading}
            >
              <User size={16} />
              <span>Informações</span>
            </button>
            <button
              onClick={() => handleTabChange("documents")}
              className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-t-lg ${
                activeTab === "documents"
                  ? "border-b-2 border-slate-500 text-slate-700 dark:text-slate-300 bg-slate-100 dark:bg-slate-800 font-semibold"
                  : availableTabs.documents
                    ? "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                    : "text-gray-400 dark:text-gray-500 cursor-not-allowed"
              }`}
              disabled={isLoading || !availableTabs.documents}
            >
              <FileText size={16} />
              <span>Documentos</span>
            </button>
            <button
              onClick={() => handleTabChange("role")}
              className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-t-lg ${
                activeTab === "role"
                  ? "border-b-2 border-slate-500 text-slate-700 dark:text-slate-300 bg-slate-100 dark:bg-slate-800 font-semibold"
                  : availableTabs.role
                    ? "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                    : "text-gray-400 dark:text-gray-500 cursor-not-allowed"
              }`}
              disabled={isLoading || !availableTabs.role}
            >
              <UserCog size={16} />
              <span>Função</span>
            </button>
            <button
              onClick={() => handleTabChange("modules")}
              className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-t-lg ${
                activeTab === "modules"
                  ? "border-b-2 border-slate-500 text-slate-700 dark:text-slate-300 bg-slate-100 dark:bg-slate-800 font-semibold"
                  : availableTabs.modules
                    ? "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                    : "text-gray-400 dark:text-gray-500 cursor-not-allowed"
              }`}
              disabled={isLoading || !availableTabs.modules}
            >
              <Shield size={16} />
              <span>Módulos</span>
            </button>
            <button
              onClick={() => handleTabChange("permissions")}
              className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-t-lg ${
                activeTab === "permissions"
                  ? "border-b-2 border-slate-500 text-slate-700 dark:text-slate-300 bg-slate-100 dark:bg-slate-800 font-semibold"
                  : availableTabs.permissions
                    ? "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                    : "text-gray-400 dark:text-gray-500 cursor-not-allowed"
              }`}
              disabled={isLoading || !availableTabs.permissions}
            >
              <Lock size={16} />
              <span>Permissões</span>
            </button>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-y-auto p-6">
          {activeTab === "info" && (
            <div>
              <UserInfoTab
                formData={formData}
                errors={errors}
                isLoading={isLoading}
                user={user}
                savedUserId={savedUserId}
                companies={companies}
                loadingCompanies={loadingCompanies}
                branches={branches}
                loadingBranches={loadingBranches}
                professions={professions}
                loadingProfessions={loadingProfessions}
                selectedProfession={selectedProfession}
                professionDefaultModules={professionDefaultModules}
                professionDefaultPermissions={professionDefaultPermissions}
                isUsingProfessionDefaults={isUsingProfessionDefaults}
                isSystemAdmin={isSystemAdmin}
                profileImageUploadRef={profileImageUploadRef}
                onChange={handleChange}
                onDocumentTypeChange={handleDocumentTypeChange}
                onImageUploaded={handleImageUploaded}
                onProfessionDefaultsToggle={() => {
                  setIsUsingProfessionDefaults(!isUsingProfessionDefaults);
                  if (!isUsingProfessionDefaults) {
                    setSelectedModules(professionDefaultModules);
                    setSelectedPermissions(professionDefaultPermissions);
                  }
                }}
              />
            </div>
          )}

          {activeTab === "documents" && (
            <UserDocumentsTab
              userId={savedUserId || user?.id}
              onClose={() => setActiveTab("info")}
              isCreating={!savedUserId && !user}
              onAddTempDocument={(doc) => {
                setTempDocuments(prev => [...prev, doc]);
              }}
              tempDocuments={tempDocuments}
            />
          )}

          {activeTab === "role" && (
            <UserRoleTab
              user={user}
              savedUserId={savedUserId}
              selectedRole={selectedRole}
              setSelectedRole={setSelectedRole}
              currentUser={currentUser}
              isSystemAdmin={isSystemAdmin}
              isLoading={isLoading}
            />
          )}

          {activeTab === "modules" && (
            <UserModulesTab
              user={user}
              savedUserId={savedUserId}
              selectedModules={selectedModules}
              setSelectedModules={setSelectedModules}
              isAdmin={isAdmin}
              isLoading={isLoading}
            />
          )}

          {activeTab === "permissions" && (
            <UserPermissionsTab
              user={user}
              savedUserId={savedUserId}
              selectedModules={selectedModules}
              selectedPermissions={selectedPermissions}
              setSelectedPermissions={setSelectedPermissions}
              filteredPermissions={filteredPermissions}
              expandedModules={expandedModules}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              toggleModuleExpansion={toggleModuleExpansion}
              togglePermission={togglePermission}
              toggleModulePermissions={toggleModulePermissions}
            />
          )}
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 border-t-2 border-gray-300 dark:border-gray-600 pt-4 flex-shrink-0">
            <UserFormFooter
              activeTab={activeTab}
              isLoading={isLoading}
              user={user}
              savedUserId={savedUserId}
              selectedRole={selectedRole}
              onBack={handleBack}
              onClose={onClose}
              onSubmit={handleSubmit}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserFormModal;