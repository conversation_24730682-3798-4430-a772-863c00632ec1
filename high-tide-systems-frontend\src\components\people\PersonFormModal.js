"use client";

import React, { useState, useEffect, useRef } from "react";
import { User, Mail, Calendar, Phone, FileText, Loader2, AlertCircle, CreditCard, Users, X, MapPin } from "lucide-react";
import { ModuleInput, ModuleSelect, ModuleTextarea, ModuleFormGroup } from "@/components/ui";
import AddressForm from "@/components/common/AddressForm";
import MaskedInput from "@/components/common/MaskedInput";
import ShareButton from "@/components/common/ShareButton";
import UserProfileImageUpload from "@/components/forms/UserProfileImageUpload";
import { personsService } from "@/app/modules/people/services/personsService";
import { clientsService } from "@/app/modules/people/services/clientsService";
import { usePreferences } from "@/hooks/usePreferences";
import { format } from "date-fns";
import PersonInfoTab from "./PersonInfoTab";
import DocumentsTab from "./DocumentsTab";
import ContactsTab from "./ContactsTab";
import PersonInsurancesTab from "./PersonInsurancesTab";
import { useToast } from "@/contexts/ToastContext";
import { validateBirthDate } from "@/utils/dateUtils";

const PersonFormModal = ({ isOpen, onClose, person, onSuccess, initialClientId }) => {
  const { getRequiredFieldsForValidation } = usePreferences();
  const { toast_error, toast_warning } = useToast();
  const [activeTab, setActiveTab] = useState("info"); // "info", "documents", "contacts", "insurances"
  const [savedPersonId, setSavedPersonId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [availableTabs, setAvailableTabs] = useState({
    info: true,
    documents: false,
    contacts: false,
    insurances: false
  });
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [tempDocuments, setTempDocuments] = useState([]);
  const [tempContacts, setTempContacts] = useState([]);
  const profileImageUploadRef = useRef(null);
  const [tempInsurances, setTempInsurances] = useState([]);
  const [tempProfileImage, setTempProfileImage] = useState(null);
  const [isLoadingInitialClient, setIsLoadingInitialClient] = useState(false);
  const hasLoadedInitialClient = useRef(false);
  const [formData, setFormData] = useState({
    fullName: "",
    cpf: "",
    birthDate: "",
    address: "",
    neighborhood: "",
    city: "",
    state: "",
    postalCode: "",
    phone: "",
    email: "",
    gender: "",
    notes: "",
    clientId: "",
    relationship: "",
    useClientEmail: false,
    useClientPhone: false,
  });
  const [errors, setErrors] = useState({});

  // Fetch clients and initialize form data when modal opens
  useEffect(() => {
    if (isOpen) {
      console.log('Modal aberto, inicializando dados do formulário');
      console.log('Dados da pessoa recebidos no modal:', person);
      initializeFormData();
    }
  }, [isOpen, person, initialClientId]);

  // Load client data when initialClientId is provided
  useEffect(() => {
    console.log('UseEffect executando com:', { initialClientId, person: !!person, isOpen, isLoadingInitialClient, hasLoadedInitialClient: hasLoadedInitialClient.current });
    
    if (initialClientId && !person && isOpen && !isLoadingInitialClient && !hasLoadedInitialClient.current) {
      console.log('Condições atendidas, tentando carregar dados do cliente inicial...');
      
      // Add a small delay to ensure formData is initialized
      const timer = setTimeout(() => {
        console.log('Timer executando, chamando loadInitialClientData...');
        loadInitialClientData();
      }, 500); // Increased delay to ensure formData is properly initialized
      
      return () => clearTimeout(timer);
    } else {
      console.log('Condições não atendidas para carregar cliente inicial');
    }
  }, [initialClientId, person, isOpen, isLoadingInitialClient]);

  const loadInitialClientData = async () => {
    console.log('=== loadInitialClientData INICIADO ===');
    console.log('isLoadingInitialClient:', isLoadingInitialClient);
    console.log('initialClientId:', initialClientId);
    console.log('formData.clientPersons:', formData.clientPersons);
    
    if (isLoadingInitialClient) {
      console.log('Já está carregando, ignorando chamada');
      return;
    }
    
    setIsLoadingInitialClient(true);
    hasLoadedInitialClient.current = true;
    
    try {
      console.log('Carregando dados do cliente inicial:', initialClientId);
      
      // Import the service using regular import (already imported at top)
      console.log('Chamando clientsService.getClient...');
      const clientData = await clientsService.getClient(initialClientId);
      
      console.log('Dados do cliente carregados com sucesso:', clientData);
      
      // Update the clientPersons data with the loaded client information
      setFormData(prev => {
        console.log('=== ATUALIZANDO FORMDATA ===');
        console.log('FormData anterior:', prev);
        console.log('ClientPersons anterior:', prev.clientPersons);
        
        // If clientPersons is empty, create it
        let updatedClientPersons;
        if (!prev.clientPersons || prev.clientPersons.length === 0) {
          console.log('Criando clientPersons do zero');
          updatedClientPersons = [{
            clientId: initialClientId,
            relationship: "Titular",
            isPrimary: true,
            client: {
              id: clientData.id,
              login: clientData.login,
              email: clientData.email
            }
          }];
        } else {
          console.log('Atualizando clientPersons existente');
          updatedClientPersons = prev.clientPersons.map(cp => {
            if (cp.clientId === initialClientId) {
              return {
                ...cp,
                client: {
                  id: clientData.id,
                  login: clientData.login,
                  email: clientData.email
                }
              };
            }
            return cp;
          });
        }
        
        console.log('ClientPersons atualizados:', updatedClientPersons);
        
        const newFormData = {
          ...prev,
          clientPersons: updatedClientPersons
        };
        
        console.log('Novo FormData:', newFormData);
        console.log('=== FIM ATUALIZACAO FORMDATA ===');
        
        return newFormData;
      });
      
      console.log('=== loadInitialClientData FINALIZADO COM SUCESSO ===');
    } catch (error) {
      console.error('Erro ao carregar dados do cliente inicial:', error);
      // Even on error, try to show something useful
      setFormData(prev => {
        const updatedClientPersons = prev.clientPersons && prev.clientPersons.length > 0
          ? prev.clientPersons.map(cp => 
              cp.clientId === initialClientId 
                ? {
                    ...cp,
                    client: {
                      id: initialClientId,
                      login: "Erro ao carregar",
                      email: ""
                    }
                  }
                : cp
            )
          : [{
              clientId: initialClientId,
              relationship: "Titular",
              isPrimary: true,
              client: {
                id: initialClientId,
                login: "Erro ao carregar",
                email: ""
              }
            }];
        
        return {
          ...prev,
          clientPersons: updatedClientPersons
        };
      });
      
      console.log('=== loadInitialClientData FINALIZADO COM ERRO ===');
    } finally {
      setIsLoadingInitialClient(false);
    }
  };

  const initializeFormData = () => {
    // Reset loading state
    setIsLoadingInitialClient(false);
    hasLoadedInitialClient.current = false;
    
    // Reset form data
    let newFormData = {
      fullName: "",
      cpf: "",
      birthDate: "",
      address: "",
      neighborhood: "",
      city: "",
      state: "",
      postalCode: "",
      phone: "",
      email: "",
      gender: "",
      notes: "",
      clientId: "",
      relationship: "",
      useClientEmail: false,
      useClientPhone: false,
      clientPersons: [],
    };

    // If editing an existing person
    if (person) {
      let formattedCpf = "";
      if (person.cpf) {
        const cleanCpf = person.cpf.replace(/\D/g, "");
        formattedCpf = cleanCpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
      }

      // Formatar telefone
      let formattedPhone = "";
      if (person.phone) {
        const cleanPhone = person.phone.replace(/\D/g, "");
        formattedPhone = cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
      }

      let birthDateFormatted = "";
      try {
        if (person.birthDate) {
          birthDateFormatted = format(new Date(person.birthDate), "yyyy-MM-dd");
        }
      } catch (e) {
        console.error("Error formatting date:", e);
      }

      // Formatar CEP
      let formattedPostalCode = "";
      if (person.postalCode) {
        const cleanPostalCode = person.postalCode.replace(/\D/g, "");
        formattedPostalCode = cleanPostalCode.replace(/(\d{5})(\d{3})/, "$1-$2");
      }

      // Determinar o clientId baseado nos relacionamentos existentes
      let clientId = person.clientId || "";
      let relationship = person.relationship || "";
      
      // Se não há clientId direto, mas há relacionamentos, usar o primeiro
      if (!clientId && person.clientPersons && person.clientPersons.length > 0) {
        const firstClientPerson = person.clientPersons[0];
        clientId = firstClientPerson.clientId || firstClientPerson.client?.id || "";
        relationship = firstClientPerson.relationship || relationship;
      }

      newFormData = {
        fullName: person.fullName || "",
        cpf: formattedCpf || "",
        birthDate: birthDateFormatted,
        address: person.address || "",
        neighborhood: person.neighborhood || "",
        city: person.city || "",
        state: person.state || "",
        postalCode: formattedPostalCode || "",
        phone: formattedPhone || "",
        email: person.email || "",
        gender: person.gender || "",
        notes: person.notes || "",
        clientId: clientId,
        relationship: relationship,
        profileImageFullUrl: person.profileImageFullUrl || null,
        useClientEmail: person.useClientEmail || false,
        useClientPhone: person.useClientPhone || false,
        clientPersons: person.clientPersons || [],
      };

      // Log para depuração
      console.log('Dados da pessoa carregados:', person);
      console.log('URL da imagem de perfil:', person.profileImageFullUrl);

      // Set saved person ID for document and contact tabs
      setSavedPersonId(person.id);

      // Enable all tabs when editing an existing person
      setAvailableTabs({
        info: true,
        documents: true,
        contacts: true,
        insurances: true
      });
    }
    // If creating a new person with a pre-selected client
    else if (initialClientId) {
      newFormData.clientId = initialClientId;
      newFormData.relationship = "Titular";
      // Initialize clientPersons with the initial client
      newFormData.clientPersons = [{
        clientId: initialClientId,
        relationship: "Titular",
        isPrimary: true,
        client: {
          id: initialClientId,
          login: "Carregando...", // Will be updated when client data is loaded
          email: ""
        }
      }];
    }

    setFormData(newFormData);
    setErrors({});

    // Default to info tab when opening modal
    setActiveTab("info");
  };

  const validateForm = () => {
    const newErrors = {};
    const requiredFields = getRequiredFieldsForValidation('patient');

    // Validar campos obrigatórios baseado nas preferências
    if (requiredFields.fullName) {
      const fullName = formData.fullName?.trim() || '';
      if (!fullName) {
        newErrors.fullName = 'Nome completo é obrigatório';
      } else if (fullName.length < 2) {
        newErrors.fullName = 'Nome completo deve ter pelo menos 2 caracteres';
      }
    }

    if (requiredFields.patientCpfCnpj) {
      const cleanCpf = formData.cpf ? formData.cpf.replace(/\D/g, '') : '';
      if (!cleanCpf || cleanCpf.length !== 11) {
        newErrors.cpf = 'CPF é obrigatório';
      }
    }

    if (requiredFields.patientBirthDate) {
      const birthDate = formData.birthDate;
      if (!birthDate) {
        newErrors.birthDate = 'Data de nascimento é obrigatória';
      } else {
        const validation = validateBirthDate(birthDate);
        if (!validation.isValid) {
          newErrors.birthDate = validation.message;
        }
      }
    }

    if (requiredFields.patientEmail) {
      const email = formData.email?.trim() || '';
      
      // Se é uma edição e a pessoa original não tinha email, tornar opcional
      const originalPersonHadEmail = person && person.email;
      const isEditingWithoutOriginalEmail = person && !originalPersonHadEmail;
      
      if (!email) {
        if (isEditingWithoutOriginalEmail) {
          // Não adicionar erro se a pessoa original não tinha email
        } else {
          newErrors.email = 'E-mail é obrigatório';
        }
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        newErrors.email = 'E-mail inválido';
      }
    }

    if (requiredFields.patientPhone) {
      const cleanPhone = formData.phone ? formData.phone.replace(/\D/g, '') : '';
      if (!cleanPhone || (cleanPhone.length !== 10 && cleanPhone.length !== 11)) {
        newErrors.phone = 'Telefone é obrigatório';
      }
    }

    if (requiredFields.patientCep) {
      const cleanPostalCode = formData.postalCode ? formData.postalCode.replace(/\D/g, '') : '';
      if (!cleanPostalCode || cleanPostalCode.length !== 8) {
        newErrors.postalCode = 'CEP é obrigatório';
      }
    }

    // Validação de associação com cliente
    if (requiredFields.patientAssociateClient) {
      const hasClientId = formData.clientId && formData.clientId.trim() !== '';
      const hasClientPersons = formData.clientPersons && formData.clientPersons.length > 0;
      
      if (!hasClientId && !hasClientPersons) {
        newErrors.clientId = 'Associação com cliente é obrigatória';
      }
    }

    setErrors(newErrors);

    // Mostrar toast com erros se houver
    if (Object.keys(newErrors).length > 0) {
      const errorMessages = Object.values(newErrors).join(', ');
      toast_error({
        title: 'Campos obrigatórios não preenchidos',
        message: errorMessages
      });
    }

    return Object.keys(newErrors).length === 0;
  };

  // Função para mostrar erros de validação em toast
  const showValidationErrors = (errors) => {
    if (Object.keys(errors).length > 0) {
      const errorMessages = Object.values(errors).join(', ');
      toast_error({
        title: 'Erro de validação',
        message: errorMessages
      });
    }
  };

  const handleSave = async (data) => {
    if (!validateForm()) {
      return false;
    }

    setIsLoading(true);

    try {
      const cleanPayload = {
        ...data,
        phone: data.phone ? data.phone.replace(/\D/g, '') : '',
        notes: data.notes ? String(data.notes) : '',
        relationship: data.relationship ? String(data.relationship) : '',
        postalCode: data.postalCode ? data.postalCode.replace(/\D/g, '') : '',
        cpf: data.cpf ? data.cpf.replace(/\D/g, '') : '',
      };



      let savedPerson;

      if (person) {
        // Update existing person
        savedPerson = await personsService.updatePerson(person.id, cleanPayload);
      } else {
        // Create new person
        savedPerson = await personsService.createPerson(cleanPayload);
      }

      // Set the ID so we can use it for documents and contacts tabs
      if (savedPerson && savedPerson.id) {
        setSavedPersonId(savedPerson.id);

        // Processar dados temporários após salvar a pessoa
        await processTemporaryData(savedPerson.id);
      }

      return true;
    } catch (error) {
      console.error("Erro ao salvar pessoa:", error);

      // Handle API validation errors
      if (error.response?.data?.errors) {
        const apiErrors = {};
        const errorMessages = [];
        
        error.response.data.errors.forEach(err => {
          // Verificar se é um erro do formato antigo (com param e msg) ou novo (array de strings)
          if (typeof err === 'string') {
            // Novo formato: array de strings
            errorMessages.push(err);
            // Mapear para um campo genérico
            apiErrors['general'] = err;
          } else if (err.param && err.msg) {
            // Formato antigo: objeto com param e msg
            apiErrors[err.param] = err.msg;
            errorMessages.push(err.msg);
          }
        });
        
        setErrors(apiErrors);
        
        // Mostrar toast com erros de validação
        if (errorMessages.length > 0) {
          toast_error({
            title: 'Campos obrigatórios não preenchidos',
            message: errorMessages.join(', ')
          });
        }
      } else {
        const errorMessage = error.response?.data?.message || "Erro ao salvar pessoa";
        setErrors({
          submit: errorMessage
        });
        
        // Mostrar toast de erro
        toast_error({
          title: 'Erro ao salvar pessoa',
          message: errorMessage
        });
      }

      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Método para processar os dados temporários após salvar a pessoa
  const processTemporaryData = async (personId) => {
    console.log('Processando dados temporários para a pessoa ID:', personId);

    try {
      // Processar foto de perfil
      if (tempProfileImage) {
        console.log('Processando foto de perfil temporária');
        await personsService.uploadProfileImage(personId, tempProfileImage);
        setTempProfileImage(null);
      }

      // Processar documentos temporários
      if (tempDocuments.length > 0) {
        console.log('Processando documentos temporários:', tempDocuments.length);
        for (const doc of tempDocuments) {
          await personsService.uploadDocument(personId, doc.file, doc.type);
        }
        setTempDocuments([]);
      }

      // Processar contatos temporários
      if (tempContacts.length > 0) {
        console.log('Processando contatos temporários:', tempContacts.length);
        for (const contact of tempContacts) {
          await personsService.createContact(personId, contact);
        }
        setTempContacts([]);
      }

      // Processar convênios temporários
      if (tempInsurances.length > 0) {
        console.log('Processando convênios temporários:', tempInsurances.length);
        for (const insurance of tempInsurances) {
          await personsService.addPersonInsurance(personId, insurance);
        }
        setTempInsurances([]);
      }

      console.log('Processamento de dados temporários concluído com sucesso');
    } catch (error) {
      console.error('Erro ao processar dados temporários:', error);
      // Exibir mensagem de erro
      toast_error({
        title: 'Erro ao processar dados',
        message: 'Erro ao processar alguns dados adicionais. Por favor, verifique e tente novamente.'
      });
    }
  };

  const handleTabChange = (tab) => {
    // Limpar mensagens de erro ao mudar de aba
    setErrors({});
    setShowSuccessMessage(false);

    // Verificar se a aba está disponível
    if (!availableTabs[tab]) {
      // Se a aba não estiver disponível, mostrar mensagem
      toast_warning({
        title: 'Aba não disponível',
        message: 'Complete a etapa atual antes de avançar para a próxima'
      });
      return;
    }

    // Permitir a navegação entre abas se estiverem disponíveis
    setActiveTab(tab);

    // Exibir uma mensagem informativa se a pessoa ainda não foi salva
    if ((tab === "documents" || tab === "contacts" || tab === "insurances") && !savedPersonId && !person) {
      console.log('Mudando para a aba', tab, 'sem ID de pessoa');
    }
  };

  // Função para avançar para a próxima aba
  const handleNextTab = async () => {
    if (activeTab === "documents") {
      // Avançar para a próxima aba - Contatos
      setAvailableTabs(prev => ({
        ...prev,
        contacts: true
      }));

      setActiveTab("contacts");

      // Toast de sucesso será mostrado pelo componente de toast
    } else if (activeTab === "contacts") {
      // Avançar para a próxima aba - Convênios
      setAvailableTabs(prev => ({
        ...prev,
        insurances: true
      }));

      setActiveTab("insurances");

      // Toast de sucesso será mostrado pelo componente de toast
    }
  };

  const handlePersonInfoSubmit = async () => {
    try {
      // Verificar se há uma imagem para fazer upload
      if (profileImageUploadRef.current && profileImageUploadRef.current.hasSelectedFile && profileImageUploadRef.current.hasSelectedFile()) {
        // Obter o ID da pessoa (existente ou recém-criada)
        const personId = person ? person.id : savedPersonId;

        if (personId) {
          try {
            // Fazer o upload da imagem
            const imageResponse = await profileImageUploadRef.current.uploadSelectedImage();

            if (imageResponse && imageResponse.profileImageUrl) {
              // Atualizar o formData com a nova URL da imagem
              setFormData(prev => ({
                ...prev,
                profileImageUrl: imageResponse.profileImageUrl,
                profileImageFullUrl: imageResponse.fullImageUrl
              }));
            }
          } catch (error) {
            console.error('Erro ao fazer upload da imagem:', error);
          }
        }
      }

      // Se estiver editando uma pessoa existente
      if (person || savedPersonId) {
        // Salvar os dados da pessoa
        const success = await handleSave(formData);

        if (success) {
          // Mostrar mensagem de sucesso
          setShowSuccessMessage(true);

          // Habilitar todas as abas após salvar com sucesso
          setAvailableTabs({
            info: true,
            documents: true,
            contacts: true,
            insurances: true
          });

          // Toast de sucesso será mostrado pelo componente de toast

          // Fechar o modal após salvar (apenas na edição)
          onSuccess();
        }
      } else {
        // Criando uma nova pessoa - fluxo de etapas
        const valid = validateForm();
        if (!valid) {
          // O toast já foi mostrado na função validateForm
          return;
        }

        // Validar informações básicas e avançar para a próxima etapa
        // Atualizar as abas disponíveis
        setAvailableTabs(prev => ({
          ...prev,
          documents: true
        }));

        // Avançar para a próxima aba - Documentos
        setActiveTab("documents");

        // Toast de sucesso será mostrado pelo componente de toast
      }
    } catch (error) {
      console.error('[DEBUG][handlePersonInfoSubmit] Erro durante o processo de salvamento:', error);
      toast_error({
        title: 'Erro ao salvar informações',
        message: error.response?.data?.message || 'Erro ao salvar informações'
      });
    }
  };

  // Função para finalizar e criar a pessoa
  const handleFinish = async () => {
    console.log('Finalizando e criando a pessoa');

    try {
      setIsLoading(true);

      // Salvar os dados da pessoa
      const success = await handleSave(formData);
      console.log('Resultado do salvamento:', success ? 'Sucesso' : 'Falha');

      if (success) {
        // Toast de sucesso será mostrado pelo componente de toast

        // Fechar o modal após criar a pessoa com sucesso
        onSuccess();
      }
    } catch (error) {
      console.error('Erro ao finalizar criação da pessoa:', error);
      toast_error({
        title: 'Erro ao criar paciente',
        message: error.response?.data?.message || 'Erro ao criar paciente'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    console.log(`PersonFormModal.handleChange: Atualizando campo ${name} para ${value}`);

    // Log do estado atual antes da atualização
    console.log('Estado atual do formulário antes da atualização:', formData);

    setFormData((prev) => {
      const newState = { ...prev, [name]: value };
      console.log('Novo estado do formulário após atualização:', newState);
      return newState;
    });

    // Validar data de nascimento em tempo real
    if (name === "birthDate") {
      if (value) {
        const validation = validateBirthDate(value);
        if (!validation.isValid) {
          setErrors(prev => ({ ...prev, birthDate: validation.message }));
        } else {
          setErrors(prev => ({ ...prev, birthDate: undefined }));
        }
      } else {
        setErrors(prev => ({ ...prev, birthDate: undefined }));
      }
    }

    // Clear error when user starts typing (exceto para birthDate que tem validação específica)
    if (errors[name] && name !== "birthDate") {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };



  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="fixed left-[50%] top-[50%] z-[12050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-orange-300 dark:border-orange-600 bg-background shadow-lg duration-200 rounded-xl max-w-5xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="pb-4 border-b-2 border-orange-400 dark:border-orange-500 flex-shrink-0 px-6 pt-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg text-white">
              <User className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-orange-800 dark:text-white border-l-4 border-orange-400 dark:border-orange-500 pl-3">
                {person ? 'Editar Paciente' : 'Novo Paciente'}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                {person ? 'Modifique as informações do paciente' : 'Preencha as informações para criar um novo paciente'}
              </p>
            </div>
            {person && (
              <div className="ml-auto">
                <ShareButton
                  itemType="person"
                  itemId={person.id}
                  itemTitle={person.fullName}
                  size="sm"
                  variant="ghost"
                />
              </div>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b-2 border-orange-400 dark:border-orange-500 flex-shrink-0 px-6">
          <button
            onClick={() => handleTabChange("info")}
            className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-t-lg ${
              activeTab === "info"
                ? "border-b-2 border-orange-500 text-orange-700 dark:text-orange-300 bg-orange-100 dark:bg-orange-800 font-semibold"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
            }`}
            disabled={isLoading}
          >
            <User size={16} />
            <span>Informações</span>
          </button>
          <button
            onClick={() => handleTabChange("documents")}
            className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-t-lg ${
              activeTab === "documents"
                ? "border-b-2 border-orange-500 text-orange-700 dark:text-orange-300 bg-orange-100 dark:bg-orange-800 font-semibold"
                : availableTabs.documents
                  ? "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                  : "text-gray-400 dark:text-gray-500 cursor-not-allowed"
            }`}
            disabled={isLoading || !availableTabs.documents}
          >
            <FileText size={16} />
            <span>Documentos</span>
          </button>
          <button
            onClick={() => handleTabChange("contacts")}
            className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-t-lg ${
              activeTab === "contacts"
                ? "border-b-2 border-orange-500 text-orange-700 dark:text-orange-300 bg-orange-100 dark:bg-orange-800 font-semibold"
                : availableTabs.contacts
                  ? "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                  : "text-gray-400 dark:text-gray-500 cursor-not-allowed"
            }`}
            disabled={isLoading || !availableTabs.contacts}
          >
            <Users size={16} />
            <span>Contatos</span>
          </button>
          <button
            onClick={() => handleTabChange("insurances")}
            className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 rounded-t-lg ${
              activeTab === "insurances"
                ? "border-b-2 border-orange-500 text-orange-700 dark:text-orange-300 bg-orange-100 dark:bg-orange-800 font-semibold"
                : availableTabs.insurances
                  ? "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
                  : "text-gray-400 dark:text-gray-500 cursor-not-allowed"
            }`}
            disabled={isLoading || !availableTabs.insurances}
          >
            <CreditCard size={16} />
            <span>Convênios</span>
          </button>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(90vh-200px)]">
          <div className="flex-1 overflow-y-auto p-6">
            {errors.submit && (
              <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2 mb-6">
                <AlertCircle size={16} />
                <span>{errors.submit}</span>
              </div>
            )}

            {activeTab === "info" && (
              <PersonInfoTab
                formData={formData}
                setFormData={setFormData}
                errors={errors}
                isLoading={isLoading}
                handleChange={handleChange}
                onSubmit={handlePersonInfoSubmit}
                personId={savedPersonId || person?.id}
                profileImageUploadRef={profileImageUploadRef}
                isCreating={!savedPersonId && !person}
                onSetTempProfileImage={(file) => {
                  console.log('Definindo foto de perfil temporária:', file?.name);
                  setTempProfileImage(file);
                }}
                tempProfileImage={tempProfileImage}
              />
            )}

            {activeTab === "documents" && (
              <DocumentsTab
                personId={savedPersonId || person?.id}
                onClose={() => handleTabChange("info")}
                isCreating={!savedPersonId && !person}
                onAddTempDocument={(doc) => {
                  console.log('Adicionando documento temporário:', doc);
                  setTempDocuments(prev => [...prev, doc]);
                }}
                tempDocuments={tempDocuments}
              />
            )}

            {activeTab === "contacts" && (
              <ContactsTab
                personId={savedPersonId || person?.id}
                onClose={() => handleTabChange("info")}
                isCreating={!savedPersonId && !person}
                onAddTempContact={(contact) => {
                  console.log('Adicionando contato temporário:', contact);
                  setTempContacts(prev => [...prev, contact]);
                }}
                tempContacts={tempContacts}
              />
            )}

            {activeTab === "insurances" && (
              <PersonInsurancesTab
                personId={savedPersonId || person?.id}
                onClose={() => handleTabChange("info")}
                isCreating={!savedPersonId && !person}
                onAddTempInsurance={(insurance) => {
                  console.log('Adicionando convênio temporário:', insurance);
                  setTempInsurances(prev => [...prev, insurance]);
                }}
                tempInsurances={tempInsurances}
              />
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-between items-center border-t-2 border-orange-400 dark:border-orange-500 pt-4 flex-shrink-0 px-6 pb-6">
            <div>
              {activeTab !== "info" && (
                <button
                  type="button"
                  onClick={() => {
                    const prevTab = {
                      documents: "info",
                      contacts: "documents",
                      insurances: "contacts"
                    }[activeTab];
                    setActiveTab(prevTab);
                  }}
                  className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
                  disabled={isLoading}
                >
                  Voltar
                </button>
              )}
            </div>
            <div className="flex gap-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
                disabled={isLoading}
              >
                Cancelar
              </button>
              <button
                type="button"
                onClick={person || savedPersonId ? handlePersonInfoSubmit : activeTab === "info" ? handlePersonInfoSubmit : activeTab === "insurances" ? handleFinish : handleNextTab}
                className="px-4 py-2 bg-orange-500 dark:bg-orange-600 text-white rounded-lg hover:bg-orange-600 dark:hover:bg-orange-700 transition-colors flex items-center gap-2"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    <span>Salvando...</span>
                  </>
                ) : (
                  <>
                    <User size={16} />
                    <span>
                      {person || savedPersonId ? "Salvar" : 
                        activeTab === "info" ? "Continuar" :
                        activeTab === "insurances" ? "Criar Paciente" : "Continuar"
                      }
                    </span>
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PersonFormModal;