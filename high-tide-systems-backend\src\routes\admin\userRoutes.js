const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const checkPermission = require('../../middlewares/permissionCheck');
const systemAdminMiddleware = require('../../middlewares/systemAdmin');
const userProfileImageUpload = require('../../middlewares/userProfileImageUpload');
const AdvancedCacheMiddleware = require('../../middlewares/advancedCache');
const { dynamicValidation } = require('../../middlewares/dynamicValidation');
const { applyUserPrivacyMasks } = require('../../middlewares/privacyMiddleware');
const { auditUserDataAccess } = require('../../middlewares/auditMiddleware');
const {
  UserController,
  createUserValidation,
  updateModulesValidation,
  updatePermissionsValidation,
  updateRoleValidation,
  updateModulePreferencesValidation
} = require('../../controllers/userController');


// Rotas protegidas
// Rota especial para clientes acessarem usuários com módulo SCHEDULING
router.get('/', authenticate,
  AdvancedCacheMiddleware.smartCache({
    prefix: 'users:list',
    ttl: 600, // 10 minutos
    strategy: 'search',
    tags: ['users']
  }),
  (req, res, next) => {
    // Se for um cliente, permitir acesso direto (lógica de filtro está no controller)
    if (req.user.isClient || req.user.role === 'CLIENT') {
      return UserController.list(req, res);
    }

    // Caso contrário, verificar permissão normal
    checkPermission('admin.users.view')(req, res, next);
  },
  applyUserPrivacyMasks,
  auditUserDataAccess,
  UserController.list
);

// Rota especial para system_admin buscar todos os usuários (para o chat)
// Removendo temporariamente a rota /all que estava causando erro
router.get('/:id', authenticate,
  AdvancedCacheMiddleware.smartCache({
    prefix: 'users:profile',
    ttl: 900, // 15 minutos
    strategy: 'standard',
    tags: ['users']
  }),
  checkPermission('admin.users.view'),
  applyUserPrivacyMasks,
  auditUserDataAccess,
  UserController.getProfile
);

router.post('/', authenticate,
  checkPermission('admin.users.create'),
  createUserValidation,
  dynamicValidation('user', false), // false = criação
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['users:*'],
    tags: ['users']
  }),
  UserController.create
);

router.put('/:id', authenticate,
  checkPermission('admin.users.edit'),
  dynamicValidation('user', true), // true = atualização
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['users:*'],
    tags: ['users']
  }),
  UserController.update
);

router.patch('/:id/modules', authenticate,
  checkPermission('admin.permissions.manage'),
  updateModulesValidation,
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['users:*'],
    tags: ['users']
  }),
  UserController.updateModules
);

router.patch('/:id/permissions', authenticate,
  checkPermission('admin.permissions.manage'),
  updatePermissionsValidation,
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['users:*'],
    tags: ['users']
  }),
  UserController.updatePermissions
);

router.patch('/:id/status', authenticate,
  checkPermission('admin.users.edit'),
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['users:*'],
    tags: ['users']
  }),
  UserController.toggleStatus
);

router.patch('/:id/role', authenticate,
  checkPermission('admin.users.edit'),
  updateRoleValidation,
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['users:*'],
    tags: ['users']
  }),
  UserController.updateRole
);

router.delete('/:id', authenticate,
  checkPermission('admin.users.delete'),
  AdvancedCacheMiddleware.smartInvalidation({
    patterns: ['users:*'],
    tags: ['users']
  }),
  UserController.delete
);

// Rotas para gerenciamento de imagem de perfil
router.post('/:id/profile-image', authenticate, checkPermission('admin.users.edit'), userProfileImageUpload.single('profileImage'), UserController.uploadProfileImage);
router.get('/:id/profile-image', UserController.getProfileImage);

// Rotas para gerenciamento de preferências de módulos
// Importante: rotas mais específicas devem vir antes das rotas com parâmetros dinâmicos
console.log('[userRoutes] Registrando rota GET /module-preferences');
router.get('/module-preferences', authenticate, (req, res, next) => {
  console.log('[userRoutes] Rota GET /module-preferences chamada');
  UserController.getModulePreferences(req, res, next);
});

console.log('[userRoutes] Registrando rota PUT /module-preferences');
router.put('/module-preferences', authenticate, updateModulePreferencesValidation, (req, res, next) => {
  console.log('[userRoutes] Rota PUT /module-preferences chamada');
  UserController.updateModulePreferences(req, res, next);
});

// Rotas com parâmetros dinâmicos devem vir depois
console.log('[userRoutes] Registrando rota GET /:id/module-preferences');
router.get('/:id/module-preferences', authenticate, (req, res, next) => {
  console.log('[userRoutes] Rota GET /:id/module-preferences chamada com id:', req.params.id);
  UserController.getModulePreferences(req, res, next);
});

console.log('[userRoutes] Registrando rota PUT /:id/module-preferences');
router.put('/:id/module-preferences', authenticate, checkPermission('admin.users.edit'), updateModulePreferencesValidation, (req, res, next) => {
  console.log('[userRoutes] Rota PUT /:id/module-preferences chamada com id:', req.params.id);
  UserController.updateModulePreferences(req, res, next);
});

module.exports = router;