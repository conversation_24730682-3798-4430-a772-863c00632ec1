import React, { useState } from 'react';
import EventTooltip from './EventTooltip';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Grid, Clock, Eye, Download } from 'lucide-react';
import { getEventBackgroundColor, getEventTextColor, getEventStatusLabel } from '../utils/eventColors';
import '../styles/vibrant-calendar.css';
import MultipleEventsModal from './MultipleEventsModal';

const diasSemana = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
const meses = [
  'Janeiro', 'Fevereiro', 'Março', 'Abril', '<PERSON><PERSON>', '<PERSON><PERSON>',
  'Jul<PERSON>', 'Agosto', 'Setem<PERSON>', 'Outubro', 'Novembro', 'Dezembro'
];

function addDays(date, days) {
  const d = new Date(date);
  d.setDate(d.getDate() + days);
  return d;
}

function getDaysInMonth(year, month) {
  return new Date(year, month + 1, 0).getDate();
}

function getFirstDayOfMonth(year, month) {
  return new Date(year, month, 1).getDay();
}

function formatWeekRange(startDate) {
  const endDate = addDays(startDate, 6);
  const options = { day: '2-digit', month: 'long', year: 'numeric' };
  const startStr = startDate.toLocaleDateString('pt-BR', { day: '2-digit' });
  const endStr = endDate.toLocaleDateString('pt-BR', options);
  return `${startStr} - ${endStr}`;
}

export default function CustomMonthGrid({ events, monthStartDate, onNavigate, onViewChange, onEventClick, isDarkMode = false, onCreateAppointment, preferences, allowedHours, onExport, isExporting, isClient = false }) {
  monthStartDate = new Date(monthStartDate);
  if (isNaN(monthStartDate)) monthStartDate = new Date();
  const [modalEvents, setModalEvents] = useState(null);
  
  // Calcular datas do mês
  const year = monthStartDate.getFullYear();
  const month = monthStartDate.getMonth();
  const daysInMonth = getDaysInMonth(year, month);
  const firstDayOfMonth = getFirstDayOfMonth(year, month);
  

  
  // Calcular datas para exibir (incluindo dias do mês anterior e próximo)
  const daysToShow = [];
  
  // Filtrar dias baseado nas preferências
  const selectedWeekDays = preferences?.selectedWeekDays || [1, 2, 3, 4, 5];
  
  // Adicionar dias do mês anterior para completar a primeira semana
  const prevMonth = month === 0 ? 11 : month - 1;
  const prevYear = month === 0 ? year - 1 : year;
  const daysInPrevMonth = getDaysInMonth(prevYear, prevMonth);
  
  for (let i = firstDayOfMonth - 1; i >= 0; i--) {
    const day = daysInPrevMonth - i;
    const date = new Date(prevYear, prevMonth, day);
    daysToShow.push({
      date,
      isCurrentMonth: false,
      isToday: false
    });
  }
  
  // Adicionar dias do mês atual
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    const today = new Date();
    daysToShow.push({
      date,
      isCurrentMonth: true,
      isToday: date.toDateString() === today.toDateString()
    });
  }
  
  // Adicionar dias do próximo mês para completar a última semana
  const remainingDays = 42 - daysToShow.length; // 6 semanas * 7 dias
  const nextMonth = month === 11 ? 0 : month + 1;
  const nextYear = month === 11 ? year + 1 : year;
  
  for (let day = 1; day <= remainingDays; day++) {
    const date = new Date(nextYear, nextMonth, day);
    daysToShow.push({
      date,
      isCurrentMonth: false,
      isToday: false
    });
  }
  
  // Agrupar eventos por dia
  const eventsByDay = {};
  events.forEach(ev => {
    const start = new Date(ev.startDate || ev.start);
    const dayKey = start.toISOString().slice(0, 10); // yyyy-mm-dd
    if (!eventsByDay[dayKey]) eventsByDay[dayKey] = [];
    eventsByDay[dayKey].push(ev);
  });

  return (
    <div className="modern-calendar-container p-4">
      {/* Toolbar customizada */}
      <div className="modern-calendar-toolbar mb-4">
        <div className="toolbar-left">
          <button onClick={() => onNavigate && onNavigate('TODAY')} className="today-btn">
            <CalendarIcon size={16} />
            <span>Hoje</span>
          </button>
          <div className="navigation-buttons">
            <button onClick={() => onNavigate && onNavigate('PREV')} className="nav-btn">
              <ChevronLeft size={18} />
            </button>
            <button onClick={() => onNavigate && onNavigate('NEXT')} className="nav-btn">
              <ChevronRight size={18} />
            </button>
          </div>
          <div className="current-period">
            <h2 className="period-title">Mês</h2>
            <p className="period-date">{meses[month]} {year}</p>
          </div>
        </div>

        <div className="toolbar-right">
          <div className="view-buttons">
            <button
              className="view-btn active"
              onClick={() => onViewChange && onViewChange('month')}
            >
              <Grid size={16} />
              <span>Mês</span>
            </button>
            <button
              className="view-btn"
              onClick={() => onViewChange && onViewChange('week')}
            >
              <Clock size={16} />
              <span>Semana</span>
            </button>
            <button
              className="view-btn"
              onClick={() => onViewChange && onViewChange('day')}
            >
              <Eye size={16} />
              <span>Dia</span>
            </button>
          </div>
          
          {/* Botão de exportar */}
          {onExport && (
            <div className="export-section ml-4">
              <button
                onClick={() => onExport('image')}
                disabled={isExporting || events.length === 0}
                className="export-btn"
                title="Exportar calendário como imagem"
              >
                <Download size={16} />
                <span>Exportar</span>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Grid do mês */}
      <div className="grid grid-cols-7 gap-0 border border-purple-200 dark:border-[#374151] rounded-lg overflow-hidden">
        {/* Header dos dias da semana */}
        {diasSemana.map((dia, i) => (
          <div key={i} className="bg-purple-100 dark:bg-[#1a1d2a] text-purple-800 dark:text-[#a78bfa] font-bold p-3 border-b border-r border-purple-200 dark:border-[#374151] text-center">
            {dia}
          </div>
        ))}
        
        {/* Dias do mês */}
        {daysToShow.map((dayInfo, i) => {
          const dayKey = dayInfo.date.toISOString().slice(0, 10);
          const dayEvents = eventsByDay[dayKey] || [];
          const showEvents = dayEvents.slice(0, 3);
          const extraCount = dayEvents.length - 3;
          
          return (
            <div 
              key={i} 
              className={`min-h-[120px] max-h-[200px] border-b border-r border-purple-200 dark:border-[#374151] p-2 ${
                !selectedWeekDays.includes(dayInfo.date.getDay())
                  ? 'bg-gray-100 dark:bg-[#1a1d2a] opacity-50 cursor-not-allowed'
                  : dayInfo.isCurrentMonth 
                    ? 'bg-white dark:bg-[#23273a] cursor-pointer hover:bg-purple-50 dark:hover:bg-gray-800' 
                    : 'bg-gray-50 dark:bg-[#1a1d2a] text-gray-500 dark:text-[#6b7280] cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700'
              } ${
                dayInfo.isToday 
                  ? 'ring-2 ring-purple-400 dark:ring-[#a78bfa] ring-inset' 
                  : ''
              }`}
              style={{ cursor: !selectedWeekDays.includes(dayInfo.date.getDay()) ? 'not-allowed' : 'pointer' }}
              onClick={() => {
                // Verificar se o dia da semana está permitido pelas preferências
                const dayOfWeek = dayInfo.date.getDay();
                if (!selectedWeekDays.includes(dayOfWeek)) {
                  console.log('❌ [CustomMonthGrid] Dia da semana não permitido:', dayOfWeek);
                  return;
                }
                
                if (isClient) return;
                
                // Encontrar o primeiro horário permitido
                let firstAllowedHour = 8;
                if (allowedHours) {
                  for (let hour = 0; hour < 24; hour++) {
                    if (allowedHours[hour]) {
                      firstAllowedHour = hour;
                      break;
                    }
                  }
                }
                
                const start = new Date(dayInfo.date);
                start.setHours(firstAllowedHour, 0, 0, 0);
                const end = new Date(start);
                end.setHours(firstAllowedHour + 1, 0, 0, 0);
                console.log('🟢 [CustomMonthGrid] Slot (célula) do mês clicado:', { start, end, dia: dayKey });
                if (typeof onCreateAppointment === 'function') {
                  onCreateAppointment({ start, end, dia: dayKey });
                }
              }}
            >
              {/* Número do dia */}
              <div className={`text-sm font-bold mb-1 ${
                dayInfo.isToday 
                  ? 'text-[#a78bfa]' 
                  : dayInfo.isCurrentMonth 
                    ? 'text-[#e5e7eb]' 
                    : 'text-[#6b7280]'
              }`}>
                {dayInfo.date.getDate()}
              </div>
              
              {/* Eventos do dia */}
              <div className="space-y-1">
                {showEvents.map((ev, idx) => {
                  const eventStatus = ev.extendedProps?.status || ev.status || 'PENDING';
                  return (
                    <EventTooltip key={ev.id} content={ev.title || ev.personfullName}>
                      <div
                        className="calendar-event-container w-full rounded px-2 py-1 font-bold text-xs shadow-md cursor-pointer hover:opacity-80 transition"
                        style={{
                          background: getEventBackgroundColor(eventStatus, isDarkMode),
                          color: getEventTextColor(eventStatus, isDarkMode)
                        }}
                        onClick={e => {
                          e.stopPropagation();
                          if (isClient) return;
                          console.log('[CustomMonthGrid] Evento clicado:', ev);
                          onEventClick && onEventClick({ event: ev });
                        }}
                      >
                        <span className="calendar-event-text" style={{ fontWeight: 'normal' }}>
                          <span style={{ fontWeight: 'bold', fontSize: '1em' }}>{ev.extendedProps?.personfullName || 'N/I'}</span><br />
                          <span style={{ fontWeight: 'normal', fontSize: '0.85em' }}>{ev.extendedProps?.providerfullName || 'N/I'}</span><br />
                          <span style={{ fontWeight: 'normal', fontSize: '0.85em' }}>{(ev.extendedProps?.serviceTypefullName || ev.extendedProps?.serviceType?.name || 'N/I')}</span><br />
                          <span style={{ fontWeight: 'normal', fontSize: '0.85em' }}>{(new Date(ev.startDate || ev.start)).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}</span><br />
                          <span style={{ fontWeight: 'normal', fontSize: '0.85em' }}>{(new Date(ev.endDate || ev.end)).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}</span>
                        </span>
                      </div>
                    </EventTooltip>
                  );
                })}
                {extraCount > 0 && (
                  <button
                    className="calendar-event-container w-full rounded bg-[#7c3aed] text-white font-bold text-xs py-1 hover:bg-[#a78bfa] transition"
                    style={{}}
                    onClick={e => { e.stopPropagation(); setModalEvents(dayEvents); }}
                  >
                    <span className="calendar-event-text">
                      +{extraCount} mais
                    </span>
                  </button>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Modal de eventos extras */}
      <MultipleEventsModal
        isOpen={!!modalEvents}
        onClose={() => setModalEvents(null)}
        events={modalEvents || []}
        title="Agendamentos do dia"
        onEventClick={onEventClick}
        isDarkMode={isDarkMode}
      />
    </div>
  );
} 