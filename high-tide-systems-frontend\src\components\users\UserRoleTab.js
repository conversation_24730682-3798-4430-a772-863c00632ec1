import React from 'react';
import { Shield, UserCog, AlertCircle, Info } from 'lucide-react';
import { ModuleRadio } from '@/components/ui';

const UserRoleTab = ({
  user,
  savedUserId,
  selectedRole,
  setSelectedRole,
  currentUser,
  isSystemAdmin,
  isLoading
}) => {
  return (
    <div>
      <div className="mb-6">
        <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-slate-500 pl-3">
          {user?.fullName || (savedUserId ? "Novo Usuário" : "")}
        </h4>
        <p className="text-sm text-neutral-600 dark:text-gray-300">
          Selecione a função deste usuário no sistema:
        </p>
      </div>

      <div className="space-y-4">
        {/* Função SYSTEM_ADMIN */}
        {isSystemAdmin && (
          <div
            className={`p-4 rounded-lg border ${
              selectedRole === "SYSTEM_ADMIN" 
                ? "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border-red-200 dark:border-red-800/50" 
                : "border-neutral-200 dark:border-gray-700"
            } ${
              !isSystemAdmin 
                ? "opacity-70 cursor-not-allowed" 
                : "cursor-pointer hover:border-slate-300 dark:hover:border-slate-700"
            }`}
            onClick={() => isSystemAdmin && setSelectedRole("SYSTEM_ADMIN")}
          >
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 mt-0.5">
                <ModuleRadio
                  name="selectedRole"
                  value="SYSTEM_ADMIN"
                  selectedValue={selectedRole}
                  onChange={() => isSystemAdmin && setSelectedRole("SYSTEM_ADMIN")}
                  disabled={isLoading || !isSystemAdmin}
                  moduleColor="admin"
                  size="md"
                  showLabel={false}
                />
              </div>

              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <UserCog className="h-5 w-5 text-red-600 dark:text-red-400" />
                  <h5 className="font-medium text-neutral-800 dark:text-white">
                    Administrador do Sistema
                    <span className="ml-2 text-xs font-normal text-amber-600 dark:text-amber-500 bg-amber-50 dark:bg-amber-900/30 px-2 py-1 rounded">
                      Acesso Total
                    </span>
                  </h5>
                </div>
                <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                  Acesso completo a todas as funcionalidades e empresas
                </p>
                {!isSystemAdmin && (
                  <div className="mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1">
                    <AlertCircle size={12} />
                    <span>Apenas administradores do sistema podem conceder este acesso</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Função COMPANY_ADMIN */}
        <div
          className={`p-4 rounded-lg border ${
            selectedRole === "COMPANY_ADMIN" 
              ? "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800/50" 
              : "border-neutral-200 dark:border-gray-700"
          } ${
            currentUser?.role === "EMPLOYEE" 
              ? "opacity-70 cursor-not-allowed" 
              : "cursor-pointer hover:border-slate-300 dark:hover:border-slate-700"
          }`}
          onClick={() => currentUser?.role !== "EMPLOYEE" && setSelectedRole("COMPANY_ADMIN")}
        >
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <ModuleRadio
                name="selectedRole"
                value="COMPANY_ADMIN"
                selectedValue={selectedRole}
                onChange={() => currentUser?.role !== "EMPLOYEE" && setSelectedRole("COMPANY_ADMIN")}
                disabled={isLoading || currentUser?.role === "EMPLOYEE"}
                moduleColor="admin"
                size="md"
                showLabel={false}
              />
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <h5 className="font-medium text-neutral-800 dark:text-white">Administrador da Empresa</h5>
              </div>
              <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                Acesso completo às funcionalidades dentro da empresa
              </p>
              {currentUser?.role === "EMPLOYEE" && (
                <div className="mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1">
                  <AlertCircle size={12} />
                  <span>Como funcionário, você só pode criar outros funcionários</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Função EMPLOYEE */}
        <div
          className={`p-4 rounded-lg border ${
            selectedRole === "EMPLOYEE" 
              ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50" 
              : "border-neutral-200 dark:border-gray-700"
          } cursor-pointer hover:border-slate-300 dark:hover:border-slate-700`}
          onClick={() => setSelectedRole("EMPLOYEE")}
        >
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <ModuleRadio
                name="selectedRole"
                value="EMPLOYEE"
                selectedValue={selectedRole}
                onChange={() => setSelectedRole("EMPLOYEE")}
                disabled={isLoading}
                moduleColor="admin"
                size="md"
                showLabel={false}
              />
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-2">
                <UserCog className="h-5 w-5 text-green-600 dark:text-green-400" />
                <h5 className="font-medium text-neutral-800 dark:text-white">Funcionário</h5>
              </div>
              <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                Acesso limitado às funcionalidades atribuídas
              </p>
            </div>
          </div>
        </div>

        {/* Alerta de restrições para não administradores do sistema */}
        {!isSystemAdmin && (
          <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500 dark:text-amber-400 flex-shrink-0 mt-0.5" />
              <div>
                <h5 className="font-medium text-amber-800 dark:text-amber-300">Restrições de acesso</h5>
                <p className="text-sm text-amber-700 dark:text-amber-400">
                  {currentUser?.role === "EMPLOYEE" ?
                    "Como funcionário, você só pode criar outros funcionários. A opção de Administrador de Empresa está desabilitada." :
                    "Você tem permissões limitadas para alterar funções de usuários. Algumas opções podem não estar disponíveis."}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserRoleTab;