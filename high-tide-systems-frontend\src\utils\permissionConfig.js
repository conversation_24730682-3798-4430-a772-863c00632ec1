// <PERSON><PERSON>, vamos definir a estrutura de permissões
// src/app/modules/admin/permissions/permissionsConfig.js

/**
 * Configuração das permissões do sistema
 * Estrutura: {
 *   [moduleId]: {
 *     name: 'Nome exibido do módulo',
 *     permissions: [
 *       {
 *         id: 'identificador_unico',
 *         name: 'Nome da permissão',
 *         description: 'Descrição detalhada'
 *       }
 *     ]
 *   }
 * }
 *
 * O formato de cada permissão será: 'module.action'
 * Exemplos: 'rh.view', 'financial.payroll.edit'
 */

export const PERMISSIONS_CONFIG = {
    // Módulo de Administração
    ADMIN: {
      name: 'Administra<PERSON>',
      icon: 'Settings',
      permissions: [
        {
          id: 'admin.users.view',
          name: 'Visualizar usuários',
          description: 'Permite visualizar a lista de usuários do sistema'
        },
        {
          id: 'admin.users.create',
          name: '<PERSON>ria<PERSON> usuá<PERSON>',
          description: 'Permite criar novos usuários no sistema'
        },
        {
          id: 'admin.users.edit',
          name: 'Editar usuários',
          description: 'Permite editar informações de usuários existentes'
        },
        {
          id: 'admin.users.delete',
          name: 'Remover usuários',
          description: 'Permite remover usuários do sistema'
        },
        {
          id: 'admin.permissions.manage',
          name: 'Gerenciar permissões',
          description: 'Permite gerenciar permissões de outros usuários'
        },
        {
          id: 'admin.logs.view',
          name: 'Visualizar logs',
          description: 'Permite visualizar logs de atividades do sistema'
        },
        {
          id: 'admin.config.edit',
          name: 'Editar configurações',
          description: 'Permite editar configurações gerais do sistema'
        },
        {
          id: 'admin.professions.view',
          name: 'Visualizar profissões',
          description: 'Permite visualizar a lista de profissões'
        },
        {
          id: 'admin.professions.create',
          name: 'Criar profissões',
          description: 'Permite criar novas profissões'
        },
        {
          id: 'admin.professions.edit',
          name: 'Editar profissões',
          description: 'Permite editar profissões existentes'
        },
        {
          id: 'admin.professions.delete',
          name: 'Excluir profissões',
          description: 'Permite excluir profissões'
        },
        {
          id: 'admin.profession-groups.view',
          name: 'Visualizar grupos de profissões',
          description: 'Permite visualizar a lista de grupos de profissões'
        },
        {
          id: 'admin.profession-groups.create',
          name: 'Criar grupos de profissões',
          description: 'Permite criar novos grupos de profissões'
        },
        {
          id: 'admin.profession-groups.edit',
          name: 'Editar grupos de profissões',
          description: 'Permite editar grupos de profissões existentes'
        },
        {
          id: 'admin.profession-groups.delete',
          name: 'Excluir grupos de profissões',
          description: 'Permite excluir grupos de profissões'
        },
        {
          id: 'admin.plans.view',
          name: 'Visualizar planos',
          description: 'Permite visualizar a lista de planos disponíveis'
        },
        {
          id: 'admin.plans.create',
          name: 'Criar planos',
          description: 'Permite criar novos planos'
        },
        {
          id: 'admin.plans.edit',
          name: 'Editar planos',
          description: 'Permite editar planos existentes'
        },
        {
          id: 'admin.plans.delete',
          name: 'Excluir planos',
          description: 'Permite excluir planos'
        },
        {
          id: 'admin.plans.features.manage',
          name: 'Gerenciar recursos dos planos',
          description: 'Permite gerenciar os recursos e funcionalidades disponíveis em cada plano'
        },
        {
          id: 'admin.plans.pricing.manage',
          name: 'Gerenciar preços dos planos',
          description: 'Permite gerenciar os preços e condições de pagamento dos planos'
        },
        {
          id: 'admin.notifications.view',
          name: 'Visualizar notificações',
          description: 'Permite visualizar configurações de notificações'
        },
        {
          id: 'admin.notifications.manage',
          name: 'Gerenciar notificações',
          description: 'Permite gerenciar configurações de notificações da empresa'
        },
        {
          id: 'admin.notifications.permissions.manage',
          name: 'Gerenciar permissões de notificação',
          description: 'Permite gerenciar permissões de notificação dos usuários'
        },
        {
          id: 'admin.settings.view',
          name: 'Visualizar configurações',
          description: 'Permite visualizar configurações do sistema'
        },
        {
          id: 'admin.settings.manage',
          name: 'Gerenciar configurações',
          description: 'Permite gerenciar configurações do sistema'
        },
        {
          id: 'admin.introductions.view',
          name: 'Visualizar introduções',
          description: 'Permite visualizar telas de introduções'
        },
        {
          id: 'admin.introductions.manage',
          name: 'Gerenciar introduções',
          description: 'Permite gerenciar telas de introduções'
        }
      ]
    },

    // // Módulo de RH - OCULTO TEMPORARIAMENTE
    // RH: {
    //   name: 'Recursos Humanos',
    //   icon: 'Users',
    //   permissions: []
    // },

    // // Módulo Financeiro - OCULTO TEMPORARIAMENTE
    // FINANCIAL: {
    //   name: 'Financeiro',
    //   icon: 'DollarSign',
    //   permissions: []
    // },

    // Módulo de Agendamento
    SCHEDULING: {
      name: 'Agendamento',
      icon: 'Calendar',
      permissions: [
        {
          id: 'scheduling.calendar.view',
          name: 'Visualizar calendário',
          description: 'Permite visualizar o calendário de agendamentos'
        },
        {
          id: 'scheduling.calendar.create',
          name: 'Criar agendamentos',
          description: 'Permite criar novos agendamentos no calendário'
        },
        {
          id: 'scheduling.calendar.edit',
          name: 'Editar agendamentos',
          description: 'Permite editar agendamentos existentes'
        },
        {
          id: 'scheduling.calendar.delete',
          name: 'Excluir agendamentos',
          description: 'Permite excluir agendamentos do calendário'
        },
        {
          id: 'scheduling.working-hours.view',
          name: 'Visualizar horários de trabalho',
          description: 'Permite visualizar horários de trabalho'
        },
        {
          id: 'scheduling.working-hours.manage',
          name: 'Gerenciar horários de trabalho',
          description: 'Permite gerenciar horários de trabalho'
        },
        {
          id: 'scheduling.service-types.view',
          name: 'Visualizar tipos de serviço',
          description: 'Permite visualizar tipos de serviço'
        },
        {
          id: 'scheduling.service-types.create',
          name: 'Criar tipos de serviço',
          description: 'Permite criar novos tipos de serviço'
        },
        {
          id: 'scheduling.service-types.edit',
          name: 'Editar tipos de serviço',
          description: 'Permite editar tipos de serviço existentes'
        },
        {
          id: 'scheduling.service-types.delete',
          name: 'Excluir tipos de serviço',
          description: 'Permite excluir tipos de serviço'
        },
        {
          id: 'scheduling.locations.view',
          name: 'Visualizar localizações',
          description: 'Permite visualizar localizações'
        },
        {
          id: 'scheduling.locations.create',
          name: 'Criar localizações',
          description: 'Permite criar novas localizações'
        },
        {
          id: 'scheduling.locations.edit',
          name: 'Editar localizações',
          description: 'Permite editar localizações existentes'
        },
        {
          id: 'scheduling.locations.delete',
          name: 'Excluir localizações',
          description: 'Permite excluir localizações'
        },
        {
          id: 'scheduling.occupancy.view',
          name: 'Visualizar ocupação',
          description: 'Permite visualizar a ocupação dos profissionais'
        },
        {
          id: 'scheduling.appointments-report.view',
          name: 'Visualizar relatório de agendamentos',
          description: 'Permite visualizar o relatório de agendamentos'
        },
        {
          id: 'scheduling.appointments-report.export',
          name: 'Exportar relatório de agendamentos',
          description: 'Permite exportar o relatório de agendamentos'
        },
        {
          id: 'scheduling.appointments-report.edit',
          name: 'Editar agendamentos no relatório',
          description: 'Permite editar agendamentos através do relatório'
        },
        {
          id: 'scheduling.appointments-report.cancel',
          name: 'Cancelar agendamentos no relatório',
          description: 'Permite cancelar agendamentos através do relatório'
        },
        {
          id: 'scheduling.appointments-report.delete',
          name: 'Excluir agendamentos no relatório',
          description: 'Permite excluir agendamentos através do relatório'
        },
        {
          id: 'scheduling.appointments-dashboard.view',
          name: 'Visualizar dashboard de agendamentos',
          description: 'Permite visualizar o dashboard de agendamentos'
        },
        {
          id: 'scheduling.rooms.manage',
          name: 'Gerenciar salas',
          description: 'Permite gerenciar salas para agendamentos'
        },
        {
          id: 'scheduling.resources.manage',
          name: 'Gerenciar recursos',
          description: 'Permite gerenciar recursos para agendamentos'
        },
        {
          id: 'scheduling.introductions.view',
          name: 'Visualizar introduções do Agendamento',
          description: 'Permite visualizar telas de introduções do módulo Agendamento'
        },
        {
          id: 'scheduling.introductions.manage',
          name: 'Gerenciar introduções do Agendamento',
          description: 'Permite gerenciar telas de introduções do módulo Agendamento'
        }
      ]
    },

    // Módulo de Pessoas
    PEOPLE: {
      name: 'Pessoas',
      icon: 'UserCheck',
      permissions: [
        {
          id: 'people.clients.view',
          name: 'Visualizar clientes',
          description: 'Permite visualizar a lista de clientes'
        },
        {
          id: 'people.clients.create',
          name: 'Criar clientes',
          description: 'Permite criar novos clientes'
        },
        {
          id: 'people.clients.edit',
          name: 'Editar clientes',
          description: 'Permite editar informações de clientes existentes'
        },
        {
          id: 'people.clients.delete',
          name: 'Excluir clientes',
          description: 'Permite excluir clientes'
        },
        {
          id: 'people.persons.view',
          name: 'Visualizar pacientes',
          description: 'Permite visualizar a lista de pacientes'
        },
        {
          id: 'people.persons.create',
          name: 'Criar pacientes',
          description: 'Permite criar novos pacientes'
        },
        {
          id: 'people.persons.edit',
          name: 'Editar pacientes',
          description: 'Permite editar informações de pacientes existentes'
        },
        {
          id: 'people.persons.delete',
          name: 'Excluir pacientes',
          description: 'Permite excluir pacientes'
        },
        {
          id: 'people.insurances.view',
          name: 'Visualizar convênios',
          description: 'Permite visualizar a lista de convênios'
        },
        {
          id: 'people.insurances.create',
          name: 'Criar convênios',
          description: 'Permite criar novos convênios'
        },
        {
          id: 'people.insurances.edit',
          name: 'Editar convênios',
          description: 'Permite editar informações de convênios existentes'
        },
        {
          id: 'people.insurances.delete',
          name: 'Excluir convênios',
          description: 'Permite excluir convênios'
        },
        {
          id: 'people.insurance-limits.view',
          name: 'Visualizar limites de convênio',
          description: 'Permite visualizar limites de convênio'
        },
        {
          id: 'people.insurance-limits.create',
          name: 'Criar limites de convênio',
          description: 'Permite criar novos limites de convênio'
        },
        {
          id: 'people.insurance-limits.edit',
          name: 'Editar limites de convênio',
          description: 'Permite editar limites de convênio existentes'
        },
        {
          id: 'people.insurance-limits.delete',
          name: 'Excluir limites de convênio',
          description: 'Permite excluir limites de convênio'
        },
        {
          id: 'people.documents.view',
          name: 'Visualizar documentos',
          description: 'Permite visualizar documentos de pessoas'
        },
        {
          id: 'people.documents.create',
          name: 'Adicionar documentos',
          description: 'Permite adicionar novos documentos de pessoas'
        },
        {
          id: 'people.documents.edit',
          name: 'Editar documentos',
          description: 'Permite editar documentos existentes'
        },
        {
          id: 'people.documents.delete',
          name: 'Excluir documentos',
          description: 'Permite excluir documentos de pessoas'
        },
        {
          id: 'people.documents.download',
          name: 'Baixar documentos',
          description: 'Permite fazer download de documentos'
        },
        {
          id: 'people.documents.categories.view',
          name: 'Visualizar categorias de documentos',
          description: 'Permite visualizar categorias de documentos'
        },
        {
          id: 'people.documents.categories.create',
          name: 'Criar categorias de documentos',
          description: 'Permite criar novas categorias de documentos'
        },
        {
          id: 'people.documents.categories.edit',
          name: 'Editar categorias de documentos',
          description: 'Permite editar categorias de documentos existentes'
        },
        {
          id: 'people.documents.categories.delete',
          name: 'Excluir categorias de documentos',
          description: 'Permite excluir categorias de documentos'
        },
        {
          id: 'people.introductions.view',
          name: 'Visualizar introduções do Pessoas',
          description: 'Permite visualizar telas de introduções do módulo Pessoas'
        },
        {
          id: 'people.introductions.manage',
          name: 'Gerenciar introduções do Pessoas',
          description: 'Permite gerenciar telas de introduções do módulo Pessoas'
        }
      ]
    },

    // // Módulo ABA+ - OCULTO TEMPORARIAMENTE
    // ABAPLUS: {
    //   name: 'ABA+',
    //   icon: 'Brain',
    //   permissions: [
    //     {
    //       id: 'abaplus.dashboard.view',
    //       name: 'Visualizar dashboard',
    //       description: 'Permite visualizar o dashboard do ABA+'
    //     }
    //   ]
    // },

    // Módulo Básico
    BASIC: {
      name: 'Básico',
      icon: 'CheckSquare',
      permissions: [
        {
          id: 'basic.profile.view',
          name: 'Visualizar perfil',
          description: 'Permite visualizar seu próprio perfil'
        },
        {
          id: 'basic.profile.edit',
          name: 'Editar perfil',
          description: 'Permite editar seu próprio perfil'
        },
        {
          id: 'notifications.new_registration.receive',
          name: 'Receber notificações de novos cadastros',
          description: 'Permite receber notificações sobre novos cadastros no sistema'
        },
        {
          id: 'notifications.appointment_coming.receive',
          name: 'Receber notificações de consultas chegando',
          description: 'Permite receber avisos de consultas agendadas (1 hora antes)'
        },
        {
          id: 'notifications.new_access.receive',
          name: 'Receber notificações de novos acessos',
          description: 'Permite receber notificações sobre novos acessos ao sistema'
        },
        {
          id: 'notifications.new_backup.receive',
          name: 'Receber notificações de backup',
          description: 'Permite receber confirmação de backups realizados'
        },
        {
          id: 'notifications.new_export.receive',
          name: 'Receber notificações de exportação',
          description: 'Permite receber notificações sobre exportações concluídas'
        },
        {
          id: 'notifications.system_alert.receive',
          name: 'Receber alertas do sistema',
          description: 'Permite receber alertas importantes do sistema'
        },
        {
          id: 'admin.settings.view',
          name: 'Visualizar configurações',
          description: 'Permite visualizar configurações do sistema'
        },
        {
          id: 'basic.introductions.view',
          name: 'Visualizar introduções do Básico',
          description: 'Permite visualizar telas de introduções do módulo Básico'
        }
      ]
    }
  };

  // Obter todas as permissões disponíveis
  export const getAllPermissions = () => {
    const allPermissions = [];

    Object.keys(PERMISSIONS_CONFIG).forEach(moduleId => {
      const module = PERMISSIONS_CONFIG[moduleId];
      module.permissions.forEach(permission => {
        allPermissions.push({
          ...permission,
          moduleId
        });
      });
    });

    return allPermissions;
  };

  // Verificar se um usuário tem uma permissão específica
  export const hasPermission = (user, permissionId) => {
    if (!user || !user.permissions) return false;

    // System Admin tem todas as permissões
    if (user.role === 'SYSTEM_ADMIN') return true;

    // Verifica se o usuário tem a permissão específica
    return user.permissions.includes(permissionId);
  };

  // Função para verificar se o usuário tem acesso a um módulo
  export const hasModuleAccess = (user, moduleId) => {
    if (!user || !user.modules) return false;

    // System Admin tem acesso a todos os módulos
    if (user.role === 'SYSTEM_ADMIN') return true;

    return user.modules.includes(moduleId);
  };

  // Função para obter todas as permissões de um módulo
  export const getModulePermissions = (moduleId) => {
    if (!PERMISSIONS_CONFIG[moduleId]) return [];
    return PERMISSIONS_CONFIG[moduleId].permissions || [];
  };

  // Função para agrupar permissões por módulo
  export const groupPermissionsByModule = (permissions) => {
    const grouped = {};

    permissions.forEach(permissionId => {
      const [modulePrefix] = permissionId.split('.');
      const moduleId = Object.keys(PERMISSIONS_CONFIG).find(
        m => modulePrefix === m.toLowerCase()
      );

      if (moduleId) {
        if (!grouped[moduleId]) {
          grouped[moduleId] = [];
        }
        grouped[moduleId].push(permissionId);
      }
    });

    return grouped;
  };