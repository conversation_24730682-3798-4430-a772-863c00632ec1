import { api } from "@/utils/api";

export const preferencesService = {
  async save(preferences) {
    const response = await api.put("/admin/preferences", { preferences });
    return response.data;
  },
  async get() {
    const response = await api.get("/admin/preferences");
    return response.data;
  },
  async saveForCompany(companyId, preferences) {
    const response = await api.put(`/admin/preferences/company/${companyId}`, { preferences });
    return response.data;
  },
  async getForCompany(companyId) {
    const response = await api.get(`/admin/preferences/company/${companyId}`);
    return response.data;
  }
}; 