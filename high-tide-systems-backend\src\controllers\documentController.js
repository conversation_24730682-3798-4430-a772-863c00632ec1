// src/controllers/documentController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const path = require("path");
const fs = require("fs").promises;
const prisma = require("../utils/prisma");
const { hasDocumentPermission, hasCategoryDocumentPermission } = require("../utils/permissionHelper");

// Caminho de uploads adaptado para funcionar em produção
const UPLOAD_PATH =
  process.env.NODE_ENV === "production"
    ? path.resolve("/data/uploads")
    : path.resolve("/usr/src/app/uploads");

// Verifica e cria o diretório de uploads se não existir
const ensureDirectoryExists = async () => {
  try {
    await fs.access(UPLOAD_PATH);
  } catch (error) {
    await fs.mkdir(UPLOAD_PATH, { recursive: true });
  }
};

// Executa a verificação do diretório
ensureDirectoryExists().catch((err) => {
  console.error(`Erro ao criar diretório de upload: ${err}`);
});

const uploadDocumentValidation = [
  body("types").custom((value, { req }) => {
    try {
      // Se for string, tenta fazer o parse para array
      const types = typeof value === "string" ? JSON.parse(value) : value;

      // Verifica se é um array
      if (!Array.isArray(types)) {
        throw new Error("Types deve ser um array");
      }

      // Verifica se o número de tipos corresponde ao número de arquivos
      if (!req.files || types.length !== req.files.length) {
        throw new Error(
          "Número de tipos deve corresponder ao número de arquivos"
        );
      }

      // Verifica se os tipos são válidos
      const validTypes = ["RG", "CPF", "CNH", "COMP_RESIDENCIA", "OUTROS"];
      const allValid = types.every((type) => validTypes.includes(type));

      if (!allValid) {
        throw new Error("Um ou mais tipos de documento são inválidos");
      }

      // Adiciona o array parseado ao request para uso posterior
      req.body.parsedTypes = types;

      return true;
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new Error("Formato inválido para types");
      }
      throw error;
    }
  }),
];

const uploadSharedDocumentValidation = [
  body("name").optional().isString().withMessage("Nome deve ser uma string"),
  body("categoryId").optional().isString().withMessage("Categoria deve ser uma string"),
  body("sharedWithUsers").optional().custom((value, { req }) => {
    try {
      const users = typeof value === "string" ? JSON.parse(value) : value;
      if (!Array.isArray(users)) {
        throw new Error("sharedWithUsers deve ser um array");
      }
      req.body.parsedSharedUsers = users;
      return true;
    } catch (error) {
      throw new Error("Formato inválido para sharedWithUsers");
    }
  }),
  body("sharedWithProfessions").optional().custom((value, { req }) => {
    try {
      const professions = typeof value === "string" ? JSON.parse(value) : value;
      if (!Array.isArray(professions)) {
        throw new Error("sharedWithProfessions deve ser um array");
      }
      req.body.parsedSharedProfessions = professions;
      return true;
    } catch (error) {
      throw new Error("Formato inválido para sharedWithProfessions");
    }
  }),
  body("sharedWithClients").optional().custom((value, { req }) => {
    try {
      const clients = typeof value === "string" ? JSON.parse(value) : value;
      if (!Array.isArray(clients)) {
        throw new Error("sharedWithClients deve ser um array");
      }
      req.body.parsedSharedClients = clients;
      return true;
    } catch (error) {
      throw new Error("Formato inválido para sharedWithClients");
    }
  }),
];

class DocumentController {
  static async upload(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ message: "Nenhum arquivo enviado" });
      }

      const types = req.body.parsedTypes;
      const userId = req.user.id;
      const targetId = req.query.targetId;
      const targetType = req.query.targetType;

      // Prepara os dados base para a criação do documento
      let createData;

      // Se não houver targetId, o documento é para o próprio usuário
      if (!targetId) {
        createData = {
          user: {
            connect: {
              id: userId,
            },
          },
          ownerType: "USER",
        };
      }
      // Se for documento para outro usuário
      else if (targetType === "user") {
        // Verificar se o usuário tem permissão para gerenciar usuários
        const canManageUsers = req.user.role === "SYSTEM_ADMIN" ||
                              req.user.role === "COMPANY_ADMIN" ||
                              req.user.permissions.includes("admin.users.create") ||
                              req.user.permissions.includes("admin.users.edit");

        if (!canManageUsers) {
          await Promise.all(req.files.map((file) => fs.unlink(file.path)));
          return res.status(403).json({
            message:
              "Você não tem permissão para adicionar documentos para usuários",
          });
        }

        const targetUser = await prisma.user.findUnique({
          where: { id: targetId },
        });

        if (!targetUser) {
          await Promise.all(req.files.map((file) => fs.unlink(file.path)));
          return res.status(404).json({ message: "Usuário não encontrado" });
        }

        createData = {
          user: {
            connect: {
              id: targetId,
            },
          },
          ownerType: "USER",
        };
      }
      // Se for documento para cliente
      else if (targetType === "person") {
        const person = await prisma.person.findFirst({
          where: {
            id: targetId,
          },
        });

        if (!person) {
          await Promise.all(req.files.map((file) => fs.unlink(file.path)));
          return res
            .status(404)
            .json({ message: "Pessoa não encontrada ou sem permissão" });
        }

        createData = {
          person: {
            connect: {
              id: targetId,
            },
          },
          ownerType: "PERSON",
        };
      }

      // Se for documento para empresa
      else if (targetType === "company") {
        const company = await prisma.company.findUnique({
          where: { id: targetId },
        });

        if (!company) {
          await Promise.all(req.files.map((file) => fs.unlink(file.path)));
          return res.status(404).json({ message: "Empresa não encontrada" });
        }

        createData = {
          company: {
            connect: {
              id: targetId,
            },
          },
          ownerType: "COMPANY",
        };
      }

      // Na criação dos documentos
      const documents = await prisma.$transaction(
        req.files.map((file, index) => {
          // Determinar o mimeType baseado na extensão ou usar o mime type do arquivo
          const mimeType = file.mimetype || "application/octet-stream";

          // Obter o tamanho do arquivo
          const fileSize = file.size || 0;

          return prisma.document.create({
            data: {
              ...createData,
              filename: file.originalname,
              path: path.relative(UPLOAD_PATH, file.path),
              type: types[index],
              mimeType: mimeType,
              size: fileSize,
              createdBy: {
                // Correção aqui - usar sintaxe de relação
                connect: {
                  id: req.user.id,
                },
              },
            },
            include: {
              person: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
              user: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
              company: {
                select: {
                  id: true,
                  name: true,
                },
              },
              createdBy: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          });
        })
      );

      res.status(201).json(documents);
    } catch (error) {
      // Em caso de erro, remove todos os arquivos enviados
      if (req.files) {
        await Promise.all(
          req.files.map((file) =>
            fs
              .unlink(file.path)
              .catch((err) =>
                console.error(`Erro ao remover arquivo ${file.path}:`, err)
              )
          )
        );
      }

      console.error("Erro ao fazer upload dos documentos:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async uploadShared(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ message: "Nenhum arquivo enviado" });
      }

      const userId = req.user.id;
      const { name, categoryId } = req.body;
      const sharedUsers = req.body.parsedSharedUsers || [];
      const sharedProfessions = req.body.parsedSharedProfessions || [];
      const sharedClients = req.body.parsedSharedClients || [];

      // Criar o documento principal
      const documents = await prisma.$transaction(
        req.files.map((file) => {
          const mimeType = file.mimetype || "application/octet-stream";
          const fileSize = file.size || 0;

          return prisma.document.create({
            data: {
              filename: name || file.originalname,
              path: path.relative(UPLOAD_PATH, file.path),
              type: "OUTROS", // Tipo padrão para documentos compartilhados
              mimeType: mimeType,
              size: fileSize,
              ownerType: "USER",
              user: {
                connect: { id: userId },
              },
              categoryDocument: categoryId ? {
                connect: { id: categoryId },
              } : undefined,
              createdBy: {
                connect: { id: userId },
              },
            },
            include: {
              user: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
              categoryDocument: {
                select: {
                  id: true,
                  name: true,
                },
              },
              createdBy: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          });
        })
      );

      // Criar permissões para usuários
      if (sharedUsers.length > 0) {
        await Promise.all(
          documents.map((document) =>
            Promise.all(
              sharedUsers.map((userId) =>
                prisma.documentPermission.create({
                  data: {
                    documentId: document.id,
                    userId: userId,
                    canView: true,
                    canEdit: false,
                  },
                })
              )
            )
          )
        );
      }

      // Criar permissões para profissões (todos os usuários da profissão)
      if (sharedProfessions.length > 0) {
        const professionUsers = await prisma.user.findMany({
          where: {
            professionId: {
              in: sharedProfessions,
            },
          },
          select: { id: true },
        });

        if (professionUsers.length > 0) {
          await Promise.all(
            documents.map((document) =>
              Promise.all(
                professionUsers.map((user) =>
                  prisma.documentPermission.create({
                    data: {
                      documentId: document.id,
                      userId: user.id,
                      canView: true,
                      canEdit: false,
                    },
                  })
                )
              )
            )
          );
        }
      }

      // Criar permissões para clientes
      if (sharedClients.length > 0) {
        await Promise.all(
          documents.map((document) =>
            Promise.all(
              sharedClients.map((clientId) =>
                prisma.documentPermission.create({
                  data: {
                    documentId: document.id,
                    clientId: clientId,
                    canView: true,
                    canEdit: false,
                  },
                })
              )
            )
          )
        );
      }

      res.status(201).json(documents);
    } catch (error) {
      // Em caso de erro, remove todos os arquivos enviados
      if (req.files) {
        await Promise.all(
          req.files.map((file) =>
            fs
              .unlink(file.path)
              .catch((err) =>
                console.error(`Erro ao remover arquivo ${file.path}:`, err)
              )
          )
        );
      }

      console.error("Erro ao fazer upload dos documentos compartilhados:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async getDocument(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const userRole = req.user.role;
      const clientId = req.user.clientId;
      const companyId = req.user.companyId;

      const document = await prisma.document.findUnique({ where: { id } });
      if (!document) {
        return res.status(404).json({ message: "Documento não encontrado" });
      }

      // Permissão total para COMPANY_ADMIN da empresa
      if (userRole === "COMPANY_ADMIN" && document.companyId === companyId || userRole === "SYSTEM_ADMIN") {
        // ok
      } else if (
        document.userId === userId ||
        document.createdById === userId ||
        (clientId && document.clientId === clientId)
      ) {
        // ok
      } else {
        // Checar permissão explícita
        const hasDocPerm = await hasDocumentPermission({ prisma, documentId: id, userId, clientId });
        let hasCatPerm = false;
        if (document.categoryDocumentId) {
          hasCatPerm = await hasCategoryDocumentPermission({ prisma, categoryDocumentId: document.categoryDocumentId, userId, clientId });
        }
        if (!hasDocPerm && !hasCatPerm) {
          return res.status(403).json({ message: "Sem permissão para acessar este documento" });
        }
      }

      const absolutePath = path.join(UPLOAD_PATH, document.path);
      try {
        await fs.access(absolutePath);
      } catch (error) {
        return res.status(404).json({ message: "Arquivo físico não encontrado" });
      }
      res.setHeader("Content-Disposition", `inline; filename=\"${document.filename}\"`);
      res.sendFile(absolutePath);
    } catch (error) {
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async update(req, res) {
    try {
      const { id } = req.params;
      const { name, categoryId } = req.body;
      const userId = req.user.id;
      const userRole = req.user.role;
      const clientId = req.user.clientId;
      const companyId = req.user.companyId;

      const document = await prisma.document.findUnique({ where: { id } });
      if (!document) {
        return res.status(404).json({ message: "Documento não encontrado" });
      }

      // Verificar permissões
      let canEdit = false;
      if (userRole === "COMPANY_ADMIN" && document.companyId === companyId) {
        canEdit = true;
      } else if (document.userId === userId || document.createdById === userId || (clientId && document.clientId === clientId)) {
        canEdit = true;
      } else {
        // Checar permissão explícita de edição
        const hasDocPerm = await hasDocumentPermission({ prisma, documentId: id, userId, clientId, checkEdit: true });
        let hasCatPerm = false;
        if (document.categoryDocumentId) {
          hasCatPerm = await hasCategoryDocumentPermission({ prisma, categoryDocumentId: document.categoryDocumentId, userId, clientId, checkEdit: true });
        }
        if (hasDocPerm || hasCatPerm) canEdit = true;
      }

      if (!canEdit) {
        return res.status(403).json({ message: "Sem permissão para editar este documento" });
      }

      // Preparar dados para atualização
      const updateData = {};
      
      if (name && name.trim() !== document.filename) {
        updateData.filename = name.trim();
      }
      
      if (categoryId !== document.categoryDocumentId) {
        updateData.categoryDocumentId = categoryId || null;
      }

      // Atualizar documento se houver mudanças
      if (Object.keys(updateData).length > 0) {
        await prisma.document.update({
          where: { id },
          data: updateData
        });
      }

      res.json({ 
        success: true, 
        message: "Documento atualizado com sucesso",
        document: {
          ...document,
          ...updateData
        }
      });
    } catch (error) {
      console.error("Erro ao atualizar documento:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async delete(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const userRole = req.user.role;
      const clientId = req.user.clientId;
      const companyId = req.user.companyId;

      const document = await prisma.document.findUnique({ where: { id } });
      if (!document) {
        return res.status(404).json({ message: "Documento não encontrado" });
      }

      let canDelete = false;
      if (userRole === "COMPANY_ADMIN" && document.companyId === companyId) {
        canDelete = true;
      } else if (document.userId === userId || document.createdById === userId || (clientId && document.clientId === clientId)) {
        canDelete = true;
      } else {
        // Checar permissão explícita de edição
        const hasDocPerm = await hasDocumentPermission({ prisma, documentId: id, userId, clientId, checkEdit: true });
        let hasCatPerm = false;
        if (document.categoryDocumentId) {
          hasCatPerm = await hasCategoryDocumentPermission({ prisma, categoryDocumentId: document.categoryDocumentId, userId, clientId, checkEdit: true });
        }
        if (hasDocPerm || hasCatPerm) canDelete = true;
      }
      if (!canDelete) {
        return res.status(403).json({ message: "Sem permissão para deletar este documento" });
      }
      const absolutePath = path.join(UPLOAD_PATH, document.path);
      try { await fs.unlink(absolutePath); } catch {}
      await prisma.document.delete({ where: { id } });
      res.status(204).send();
    } catch (error) {
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async list(req, res) {
    try {
      const { type, targetId, targetType } = req.query;
      const userId = req.user.id;
      const userRole = req.user.role;
      const clientId = req.user.clientId;
      const companyId = req.user.companyId;
      
      console.log('[DocumentController.list] User info:', { userId, userRole, clientId, companyId });
      
      let whereClause = {};
      if (type) whereClause.type = type;
      
      // Adicionar filtros de busca
      const { search, category } = req.query;
      
      if (search) {
        whereClause.filename = {
          contains: search,
          mode: 'insensitive'
        };
      }
      
      if (category) {
        whereClause.categoryDocumentId = category;
      }
      
      // Aplicar filtros por targetId e targetType
      if (targetId && targetType) {
        if (targetType === 'person') {
          whereClause.personId = targetId;
        } else if (targetType === 'user') {
          whereClause.userId = targetId;
        } else if (targetType === 'company') {
          whereClause.companyId = targetId;
        } else if (targetType === 'client') {
          whereClause.clientId = targetId;
        }
      }
      // Aplicar filtros de permissão baseados no papel do usuário
      if (userRole === "EMPLOYEE") {
        // EMPLOYEE: só documentos próprios ou compartilhados
        const baseConditions = [
          { userId },
          { createdById: userId },
        ];
        
        // Buscar documentos compartilhados
        const docPerms = await prisma.documentPermission.findMany({ where: { userId, canView: true } });
        const docIds = docPerms.map(p => p.documentId);
        if (docIds.length > 0) {
          baseConditions.push({ id: { in: docIds } });
        }
        
        // Buscar por categoria compartilhada
        const catPerms = await prisma.categoryDocumentPermission.findMany({ where: { userId, canView: true } });
        const catIds = catPerms.map(p => p.categoryDocumentId);
        if (catIds.length > 0) {
          baseConditions.push({ categoryDocumentId: { in: catIds } });
        }
        
        // Combinar com filtros existentes usando AND
        whereClause.AND = [
          { OR: baseConditions }
        ];
        
      } else if (userRole === "COMPANY_ADMIN") {
        // COMPANY_ADMIN pode ver documentos da sua empresa OU documentos sem companyId (sistema)
        whereClause.AND = [
          { OR: [
            { companyId: companyId },
            { companyId: null }
          ]}
        ];
        
      } else if (clientId) {
        // Cliente: só documentos próprios ou compartilhados
        const baseConditions = [
          { clientId },
        ];
        
        // Documentos compartilhados com o cliente
        const docPerms = await prisma.documentPermission.findMany({ where: { clientId, canView: true } });
        const docIds = docPerms.map(p => p.documentId);
        if (docIds.length > 0) {
          baseConditions.push({ id: { in: docIds } });
        }
        
        // Por categoria
        const catPerms = await prisma.categoryDocumentPermission.findMany({ where: { clientId, canView: true } });
        const catIds = catPerms.map(p => p.categoryDocumentId);
        if (catIds.length > 0) {
          baseConditions.push({ categoryDocumentId: { in: catIds } });
        }
        
        // Combinar com filtros existentes usando AND
        whereClause.AND = [
          { OR: baseConditions }
        ];
        
      } // SYSTEM_ADMIN vê tudo
      
      console.log('[DocumentController.list] Final whereClause:', JSON.stringify(whereClause, null, 2));
      
      const documents = await prisma.document.findMany({
        where: whereClause,
        include: {
          person: { select: { id: true, fullName: true } },
          user: { select: { id: true, fullName: true } },
          company: { select: { id: true, name: true } },
          createdBy: { select: { id: true, fullName: true } },
          categoryDocument: { select: { id: true, name: true } },
          DocumentPermission: {
            include: {
              user: { select: { id: true, fullName: true } },
              client: { select: { id: true, login: true } }
            }
          }
        },
        orderBy: { createdAt: "desc" },
      });
      
      console.log('[DocumentController.list] Documents found:', documents.length);
      
      // Verificar se há parâmetros de paginação
      const { page, limit, sortField, sortDirection } = req.query;
      
      if (page && limit) {
        // Implementar paginação se necessário
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        const skip = (pageNumber - 1) * limitNumber;
        
        // Contar total de documentos
        const total = await prisma.document.count({ where: whereClause });
        
        // Buscar documentos com paginação
        const paginatedDocuments = await prisma.document.findMany({
          where: whereClause,
          include: {
            person: { select: { id: true, fullName: true } },
            user: { select: { id: true, fullName: true } },
            company: { select: { id: true, name: true } },
            createdBy: { select: { id: true, fullName: true } },
            categoryDocument: { select: { id: true, name: true } },
            DocumentPermission: {
              include: {
                user: { select: { id: true, fullName: true } },
                client: { select: { id: true, login: true } }
              }
            }
          },
          orderBy: sortField && sortDirection ? { [sortField]: sortDirection } : { createdAt: "desc" },
          skip,
          take: limitNumber,
        });
        
        console.log('[DocumentController.list] Paginated documents found:', paginatedDocuments.length);
        
        res.json({
          documents: paginatedDocuments,
          total,
          totalPages: Math.ceil(total / limitNumber),
          currentPage: pageNumber,
          limit: limitNumber
        });
      } else {
        res.json(documents);
      }
    } catch (error) {
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  // Permissões de Documento
  static async addDocumentPermission(req, res) {
    try {
      const { documentId, userId, clientId, canView, canEdit } = req.body;
      if (!documentId || (!userId && !clientId)) {
        return res.status(400).json({ message: "Informe documentId e userId ou clientId" });
      }
      const perm = await prisma.documentPermission.upsert({
        where: {
          documentId_userId_clientId: {
            documentId,
            userId: userId || null,
            clientId: clientId || null,
          },
        },
        update: { canView, canEdit },
        create: { documentId, userId, clientId, canView, canEdit },
      });
      res.json(perm);
    } catch (error) {
      res.status(500).json({ message: "Erro ao adicionar permissão" });
    }
  }

  static async removeDocumentPermission(req, res) {
    try {
      const { documentId, userId, clientId } = req.body;
      if (!documentId || (!userId && !clientId)) {
        return res.status(400).json({ message: "Informe documentId e userId ou clientId" });
      }
      await prisma.documentPermission.deleteMany({
        where: { documentId, userId: userId || null, clientId: clientId || null },
      });
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Erro ao remover permissão" });
    }
  }

  // Permissões de Categoria de Documento
  static async addCategoryDocumentPermission(req, res) {
    try {
      const { categoryDocumentId, userId, clientId, canView, canEdit } = req.body;
      if (!categoryDocumentId || (!userId && !clientId)) {
        return res.status(400).json({ message: "Informe categoryDocumentId e userId ou clientId" });
      }
      const perm = await prisma.categoryDocumentPermission.upsert({
        where: {
          categoryDocumentId_userId_clientId: {
            categoryDocumentId,
            userId: userId || null,
            clientId: clientId || null,
          },
        },
        update: { canView, canEdit },
        create: { categoryDocumentId, userId, clientId, canView, canEdit },
      });
      res.json(perm);
    } catch (error) {
      res.status(500).json({ message: "Erro ao adicionar permissão de categoria" });
    }
  }

  static async removeCategoryDocumentPermission(req, res) {
    try {
      const { categoryDocumentId, userId, clientId } = req.body;
      if (!categoryDocumentId || (!userId && !clientId)) {
        return res.status(400).json({ message: "Informe categoryDocumentId e userId ou clientId" });
      }
      await prisma.categoryDocumentPermission.deleteMany({
        where: { categoryDocumentId, userId: userId || null, clientId: clientId || null },
      });
      res.json({ success: true });
    } catch (error) {
      res.status(500).json({ message: "Erro ao remover permissão de categoria" });
    }
  }

  // Buscar permissões de um documento
  static async getDocumentPermissions(req, res) {
    try {
      const { id } = req.params;
      
      // Verificar se o documento existe
      const document = await prisma.document.findUnique({
        where: { id },
        include: { DocumentPermission: true }
      });
      
      if (!document) {
        return res.status(404).json({ message: "Documento não encontrado" });
      }

      // Verificar permissões do usuário
      const canView = req.user.role === "SYSTEM_ADMIN" || 
                     req.user.role === "COMPANY_ADMIN" ||
                     document.createdById === req.user.id ||
                     document.userId === req.user.id;

      if (!canView) {
        return res.status(403).json({ message: "Sem permissão para visualizar este documento" });
      }

      // Buscar permissões organizadas por tipo
      const permissions = await prisma.documentPermission.findMany({
        where: { documentId: id },
        include: {
          user: { select: { id: true, fullName: true, email: true } },
          client: { select: { id: true, login: true, email: true } }
        }
      });

      // Organizar permissões
      const users = permissions
        .filter(p => p.userId)
        .map(p => p.userId);

      const clients = permissions
        .filter(p => p.clientId)
        .map(p => p.clientId);

      // Para profissões, precisamos buscar os usuários e agrupar por profissão
      const userIds = permissions
        .filter(p => p.userId)
        .map(p => p.userId);

      const usersWithProfessions = await prisma.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, professionId: true }
      });

      // Agrupar usuários por profissão e identificar profissões que têm todos os usuários
      const professionUserCounts = {};
      const totalUsersByProfession = {};
      
      // Contar usuários por profissão no sistema
      const allUsers = await prisma.user.findMany({
        select: { id: true, professionId: true }
      });
      
      allUsers.forEach(user => {
        if (user.professionId) {
          totalUsersByProfession[user.professionId] = (totalUsersByProfession[user.professionId] || 0) + 1;
        }
      });
      
      // Contar usuários selecionados por profissão
      usersWithProfessions.forEach(user => {
        if (user.professionId) {
          professionUserCounts[user.professionId] = (professionUserCounts[user.professionId] || 0) + 1;
        }
      });
      
      // Identificar profissões onde todos os usuários estão selecionados
      const professions = Object.keys(professionUserCounts).filter(professionId => {
        const selectedCount = professionUserCounts[professionId];
        const totalCount = totalUsersByProfession[professionId];
        return selectedCount === totalCount && totalCount > 0;
      });

      res.json({
        users,
        professions,
        clients,
        totalUsers: users.length,
        totalProfessions: professions.length,
        totalClients: clients.length
      });
    } catch (error) {
      console.error("Erro ao buscar permissões:", error);
      res.status(500).json({ message: "Erro ao buscar permissões do documento" });
    }
  }

  // Gerenciar permissões em lote para um documento
  static async updateDocumentPermissions(req, res) {
    try {
      const { id } = req.params;
      const { users, professions, clients } = req.body;
      
      // Verificar se o documento existe
      const document = await prisma.document.findUnique({
        where: { id },
        include: { DocumentPermission: true }
      });
      
      if (!document) {
        return res.status(404).json({ message: "Documento não encontrado" });
      }

      // Verificar permissões do usuário
      const canManage = req.user.role === "SYSTEM_ADMIN" || 
                       req.user.role === "COMPANY_ADMIN" ||
                       document.createdById === req.user.id;

      if (!canManage) {
        return res.status(403).json({ message: "Sem permissão para gerenciar este documento" });
      }

      // Remover todas as permissões existentes
      await prisma.documentPermission.deleteMany({
        where: { documentId: id }
      });

      // Adicionar permissões para usuários individuais
      if (users && users.length > 0) {
        const userPermissions = users.map(userId => ({
          documentId: id,
          userId,
          canView: true,
          canEdit: false
        }));
        await prisma.documentPermission.createMany({
          data: userPermissions
        });
      }

      // Adicionar permissões para usuários das profissões
      if (professions && professions.length > 0) {
        const professionUsers = await prisma.user.findMany({
          where: { professionId: { in: professions } }
        });
        
        if (professionUsers.length > 0) {
          // Filtrar usuários que já não foram adicionados individualmente
          const userIdsToAdd = professionUsers
            .map(user => user.id)
            .filter(userId => !users.includes(userId));
          
          if (userIdsToAdd.length > 0) {
            const professionPermissions = userIdsToAdd.map(userId => ({
              documentId: id,
              userId,
              canView: true,
              canEdit: false
            }));
            await prisma.documentPermission.createMany({
              data: professionPermissions,
              skipDuplicates: true
            });
          }
        }
      }

      // Adicionar permissões para clientes
      if (clients && clients.length > 0) {
        const clientPermissions = clients.map(clientId => ({
          documentId: id,
          clientId,
          canView: true,
          canEdit: false
        }));
        await prisma.documentPermission.createMany({
          data: clientPermissions
        });
      }

      res.json({ 
        success: true, 
        message: "Permissões atualizadas com sucesso",
        totalUsers: users?.length || 0,
        totalProfessions: professions?.length || 0,
        totalClients: clients?.length || 0
      });
    } catch (error) {
      console.error("Erro ao atualizar permissões:", error);
      res.status(500).json({ message: "Erro ao atualizar permissões do documento" });
    }
  }

  // CRUD de Categoria de Documento
  static async createCategoryDocument(req, res) {
    try {
      const { name, description, companyId } = req.body;
      const userRole = req.user.role;
      const userCompanyId = req.user.companyId;

      if (!name) {
        return res.status(400).json({ message: "Nome da categoria é obrigatório" });
      }

      // Determinar qual empresa usar
      let finalCompanyId = null;
      
      if (userRole === "SYSTEM_ADMIN") {
        // System admin pode escolher qualquer empresa ou criar sem empresa (global)
        finalCompanyId = companyId || null;
      } else {
        // Outros usuários só podem criar para sua própria empresa
        finalCompanyId = userCompanyId;
      }

      const category = await prisma.categoryDocument.create({
        data: { 
          name, 
          description,
          companyId: finalCompanyId
        },
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              documents: true,
              CategoryDocumentPermission: true
            }
          }
        }
      });
      
      res.status(201).json(category);
    } catch (error) {
      console.error("Erro ao criar categoria de documento:", error);
      res.status(500).json({ message: "Erro ao criar categoria de documento" });
    }
  }

  // Função auxiliar para inicializar categorias padrão
  static async initializeDefaultCategories(companyId = null) {
    try {
      const defaultCategories = [
        { name: "Documentos Pessoais", description: "RG, CPF, CNH e outros documentos pessoais" },
        { name: "Comprovantes", description: "Comprovantes de residência, renda e outros" },
        { name: "Contratos", description: "Contratos e documentos legais" },
        { name: "Outros", description: "Outros documentos" }
      ];

      for (const category of defaultCategories) {
        // Verificar se a categoria já existe
        const existing = await prisma.categoryDocument.findFirst({
          where: {
            name: category.name,
            companyId: companyId
          }
        });
        
        if (!existing) {
          await prisma.categoryDocument.create({
            data: {
              name: category.name,
              description: category.description,
              companyId: companyId
            }
          });
          console.log(`[initializeDefaultCategories] Categoria criada: ${category.name}`);
        }
      }
      
      console.log('[initializeDefaultCategories] Categorias padrão inicializadas');
    } catch (error) {
      console.error('[initializeDefaultCategories] Erro ao inicializar categorias padrão:', error);
    }
  }

  static async listCategoryDocuments(req, res) {
    try {
      console.log('[listCategoryDocuments] Iniciando busca de categorias');
      console.log('[listCategoryDocuments] User:', { role: req.user.role, companyId: req.user.companyId });
      
      const userRole = req.user.role;
      const userCompanyId = req.user.companyId;

      let whereClause = {};
      
      if (userRole === "SYSTEM_ADMIN") {
        // System admin vê todas as categorias
        whereClause = {};
      } else if (userRole === "COMPANY_ADMIN" || userRole === "EMPLOYEE") {
        if (userCompanyId) {
          // Usuários com empresa veem categorias da sua empresa + categorias globais
          whereClause = {
            OR: [
              { companyId: userCompanyId },
              { companyId: null }
            ]
          };
        } else {
          // Usuários sem empresa só veem categorias globais
          whereClause.companyId = null;
        }
      } else {
        // Outros tipos de usuário só veem categorias globais
        whereClause.companyId = null;
      }

      console.log('[listCategoryDocuments] WhereClause:', JSON.stringify(whereClause, null, 2));

      let categories = [];
      
      try {
        // Tentar buscar categorias
        categories = await prisma.categoryDocument.findMany({
          where: whereClause,
          orderBy: { name: "asc" },
          include: {
            company: {
              select: {
                id: true,
                name: true
              }
            },
            _count: {
              select: {
                documents: true,
                CategoryDocumentPermission: true
              }
            }
          }
        });
        
        console.log('[listCategoryDocuments] Categorias encontradas:', categories.length);
        
        // Se não houver categorias, tentar inicializar as padrão
        if (categories.length === 0) {
          console.log('[listCategoryDocuments] Nenhuma categoria encontrada, inicializando padrões');
          await DocumentController.initializeDefaultCategories(userCompanyId);
          
          // Tentar buscar novamente
          categories = await prisma.categoryDocument.findMany({
            where: whereClause,
            orderBy: { name: "asc" },
            include: {
              company: {
                select: {
                  id: true,
                  name: true
                }
              },
              _count: {
                select: {
                  documents: true,
                  CategoryDocumentPermission: true
                }
              }
            }
          });
          
          console.log('[listCategoryDocuments] Categorias após inicialização:', categories.length);
        }
        
      } catch (dbError) {
        console.error('[listCategoryDocuments] Erro de banco de dados:', dbError);
        
        // Se for erro de tabela não existente, retornar array vazio
        if (dbError.code === 'P2021' || dbError.message.includes('does not exist') || dbError.message.includes('relation')) {
          console.log('[listCategoryDocuments] Tabela não existe, retornando array vazio');
          return res.json([]);
        }
        
        throw dbError;
      }
      
      res.json(categories);
    } catch (error) {
      console.error("[listCategoryDocuments] Erro ao listar categorias de documento:", error);
      console.error("[listCategoryDocuments] Stack trace:", error.stack);
      console.error("[listCategoryDocuments] Error code:", error.code);
      console.error("[listCategoryDocuments] Error meta:", error.meta);
      
      // Em caso de erro crítico, retornar array vazio para não quebrar o frontend
      if (error.code === 'P2021' || error.message.includes('does not exist') || error.message.includes('relation') || error.message.includes('table')) {
        console.log('[listCategoryDocuments] Retornando array vazio devido a problema de schema/tabela');
        return res.json([]);
      }
      
      res.status(500).json({ 
        message: "Erro ao listar categorias de documento",
        error: process.env.NODE_ENV === 'development' ? error.message : undefined,
        code: error.code || 'UNKNOWN_ERROR'
      });
    }
  }

  static async updateCategoryDocument(req, res) {
    try {
      const { id } = req.params;
      const { name, description, companyId } = req.body;
      const userRole = req.user.role;
      const userCompanyId = req.user.companyId;

      if (!name) {
        return res.status(400).json({ message: "Nome da categoria é obrigatório" });
      }

      // Verificar se o usuário pode editar esta categoria
      const existingCategory = await prisma.categoryDocument.findUnique({
        where: { id },
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      if (!existingCategory) {
        return res.status(404).json({ message: "Categoria não encontrada" });
      }

      // Verificar permissões
      if (userRole !== "SYSTEM_ADMIN" && existingCategory.companyId !== userCompanyId) {
        return res.status(403).json({ message: "Sem permissão para editar esta categoria" });
      }

      // Determinar qual empresa usar
      let finalCompanyId = existingCategory.companyId;
      
      if (userRole === "SYSTEM_ADMIN") {
        // System admin pode alterar a empresa ou tornar global
        finalCompanyId = companyId !== undefined ? companyId : existingCategory.companyId;
      } else {
        // Usuários normais não podem tornar categorias globais
        if (companyId === null) {
          return res.status(403).json({ message: "Usuários normais não podem criar categorias globais" });
        }
        // Se tentar alterar para outra empresa, manter a empresa atual
        if (companyId && companyId !== userCompanyId) {
          return res.status(403).json({ message: "Sem permissão para alterar categoria para outra empresa" });
        }
      }

      const category = await prisma.categoryDocument.update({
        where: { id },
        data: { 
          name, 
          description,
          companyId: finalCompanyId
        },
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              documents: true,
              CategoryDocumentPermission: true
            }
          }
        }
      });
      
      res.json(category);
    } catch (error) {
      console.error("Erro ao atualizar categoria de documento:", error);
      res.status(500).json({ message: "Erro ao atualizar categoria de documento" });
    }
  }

  static async deleteCategoryDocument(req, res) {
    try {
      const { id } = req.params;
      const userRole = req.user.role;
      const userCompanyId = req.user.companyId;

      // Verificar se a categoria existe e se o usuário tem permissão
      const existingCategory = await prisma.categoryDocument.findUnique({
        where: { id }
      });

      if (!existingCategory) {
        return res.status(404).json({ message: "Categoria não encontrada" });
      }

      // Verificar permissões
      if (userRole !== "SYSTEM_ADMIN" && existingCategory.companyId !== userCompanyId) {
        return res.status(403).json({ message: "Sem permissão para excluir esta categoria" });
      }

      await prisma.categoryDocument.delete({ where: { id } });
      res.json({ success: true });
    } catch (error) {
      console.error("Erro ao excluir categoria de documento:", error);
      res.status(500).json({ message: "Erro ao excluir categoria de documento" });
    }
  }

  // Novo método para servir arquivos
  static async downloadDocument(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      const clientId = req.user?.clientId;
      const userRole = req.user?.role;
      const userCompanyId = req.user?.companyId;

      // Buscar o documento
      const document = await prisma.document.findUnique({
        where: { id },
        include: {
          user: true,
          person: true,
          Client: true,
          company: true,
          categoryDocument: true,
          createdBy: true,
          DocumentPermission: true
        }
      });

      if (!document) {
        return res.status(404).json({ message: "Documento não encontrado" });
      }

      // Verificar permissões
      let hasPermission = false;

      if (userRole === "SYSTEM_ADMIN") {
        hasPermission = true;
      } else if (userRole === "COMPANY_ADMIN") {
        // COMPANY_ADMIN pode acessar documentos da sua empresa ou sem companyId
        hasPermission = document.companyId === userCompanyId || document.companyId === null;
      } else if (clientId) {
        // Cliente: só documentos próprios ou compartilhados
        if (document.clientId === clientId) {
          hasPermission = true;
        } else {
          // Verificar permissões explícitas
          const permission = await prisma.documentPermission.findFirst({
            where: { 
              documentId: id, 
              clientId: clientId, 
              canView: true 
            }
          });
          if (permission) hasPermission = true;

          // Verificar permissões por categoria
          if (!hasPermission && document.categoryDocumentId) {
            const catPermission = await prisma.categoryDocumentPermission.findFirst({
              where: { 
                categoryDocumentId: document.categoryDocumentId, 
                clientId: clientId, 
                canView: true 
              }
            });
            if (catPermission) hasPermission = true;
          }
        }
      } else if (userId) {
        // Usuário: documentos próprios, da empresa ou compartilhados
        if (document.userId === userId || 
            (document.companyId === userCompanyId) ||
            document.companyId === null) {
          hasPermission = true;
        } else {
          // Verificar permissões explícitas
          const permission = await prisma.documentPermission.findFirst({
            where: { 
              documentId: id, 
              userId: userId, 
              canView: true 
            }
          });
          if (permission) hasPermission = true;

          // Verificar permissões por categoria
          if (!hasPermission && document.categoryDocumentId) {
            const catPermission = await prisma.categoryDocumentPermission.findFirst({
              where: { 
                categoryDocumentId: document.categoryDocumentId, 
                userId: userId, 
                canView: true 
              }
            });
            if (catPermission) hasPermission = true;
          }
        }
      }

      if (!hasPermission) {
        return res.status(403).json({ message: "Sem permissão para acessar este documento" });
      }

      // Construir o caminho completo do arquivo
      const fullPath = path.join(UPLOAD_PATH, document.path);

      // Verificar se o arquivo existe
      try {
        await fs.access(fullPath);
      } catch (error) {
        console.error("Arquivo não encontrado:", fullPath);
        return res.status(404).json({ message: "Arquivo não encontrado no sistema" });
      }

      // Definir headers apropriados
      res.set({
        'Content-Type': document.mimeType || 'application/octet-stream',
        'Content-Disposition': `inline; filename="${document.filename}"`,
        'Cache-Control': 'private, max-age=3600' // Cache por 1 hora
      });

      // Servir o arquivo
      res.sendFile(fullPath);

    } catch (error) {
      console.error("Erro ao servir documento:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  DocumentController,
  uploadDocumentValidation,
  uploadSharedDocumentValidation,
};
