// Script para verificar e corrigir clientes com fullName vazio
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixClientFullNames() {
  try {
    console.log('Verificando clientes com fullName vazio...');
    
    // Buscar clientes com fullName vazio ou null
    const clientsWithEmptyFullName = await prisma.client.findMany({
      where: {
        OR: [
          { fullName: null },
          { fullName: '' },
          { fullName: { equals: '' } }
        ]
      },
      select: {
        id: true,
        login: true,
        fullName: true,
        email: true
      }
    });
    
    console.log(`Encontrados ${clientsWithEmptyFullName.length} clientes com fullName vazio`);
    
    if (clientsWithEmptyFullName.length > 0) {
      console.log('Clientes encontrados:');
      clientsWithEmptyFullName.forEach(client => {
        console.log(`- ID: ${client.id}, Login: ${client.login}, FullName: "${client.fullName}", Email: ${client.email}`);
      });
      
      // Corrigir clientes com fullName vazio usando o login
      const updatePromises = clientsWithEmptyFullName.map(client => 
        prisma.client.update({
          where: { id: client.id },
          data: { fullName: client.login }
        })
      );
      
      await Promise.all(updatePromises);
      console.log(`${clientsWithEmptyFullName.length} clientes corrigidos com sucesso!`);
    } else {
      console.log('Nenhum cliente com fullName vazio encontrado.');
    }
    
    // Verificar se a correção funcionou
    const allClients = await prisma.client.findMany({
      select: {
        id: true,
        login: true,
        fullName: true,
        email: true
      }
    });
    
    console.log('\nTodos os clientes após correção:');
    allClients.forEach(client => {
      console.log(`- ID: ${client.id}, Login: ${client.login}, FullName: "${client.fullName}", Email: ${client.email}`);
    });
    
  } catch (error) {
    console.error('Erro ao corrigir clientes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar apenas se chamado diretamente
if (require.main === module) {
  fixClientFullNames();
}

module.exports = { fixClientFullNames };