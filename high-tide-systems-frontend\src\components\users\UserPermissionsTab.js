import React from 'react';
import { Info, Search, ChevronDown, ChevronRight, Settings, Users, DollarSign, Calendar, CheckSquare, Bell, UserCheck } from 'lucide-react';
import { PERMISSIONS_CONFIG } from '@/utils/permissionConfig';

const UserPermissionsTab = ({
  user,
  savedUserId,
  selectedModules,
  selectedPermissions,
  setSelectedPermissions,
  filteredPermissions,
  expandedModules,
  searchTerm,
  setSearchTerm,
  toggleModuleExpansion,
  togglePermission,
  toggleModulePermissions
}) => {
  return (
    <div>
      <div className="mb-6">
        <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-slate-500 pl-3">
          {user?.fullName || (savedUserId ? "Novo Usuário" : "")}
        </h4>
        <p className="text-sm text-neutral-600 dark:text-gray-400 mb-4">
          Configure as permissões específicas que este usuário terá acesso
          dentro de cada módulo:
        </p>

        <div className="bg-amber-50 border border-amber-200 p-2 rounded-lg flex items-start gap-2 mb-6 dark:bg-amber-900/20 dark:border-amber-800/50">
          <div className="flex-shrink-0 mt-0.5">
            <Info className="h-4 w-4 text-amber-500 dark:text-amber-400" />
          </div>
          <div>
            <h5 className="text-sm font-medium text-amber-800 dark:text-amber-300">Importante</h5>
            <p className="text-xs text-amber-700 dark:text-amber-400">
              As permissões só serão aplicadas se o usuário também tiver
              acesso ao módulo correspondente. Certifique-se de que o
              usuário tenha os módulos necessários atribuídos.
            </p>
          </div>
        </div>
      </div>

      {/* Barra de pesquisa */}
      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400 dark:text-gray-500" />
          </div>
          <input
            type="text"
            placeholder="Buscar permissões..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-slate-500 dark:focus:ring-slate-400 focus:border-slate-500 dark:focus:border-slate-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* Módulos Atribuídos */}
      <div className="bg-transparent border border-gray-300 dark:border-gray-600 p-3 rounded-lg mb-6">
        <h4 className="font-medium text-neutral-700 dark:text-gray-300 mb-2">
          Módulos Atribuídos
        </h4>
        <div className="flex flex-wrap gap-2">
          {selectedModules.filter(moduleId => !["RH", "FINANCIAL"].includes(moduleId)).map((moduleId) => (
            <div
              key={moduleId}
              className={`px-3 py-1.5 border rounded-full flex items-center gap-2 ${
                moduleId === "ADMIN" ? "bg-gray-200 dark:bg-gray-800/60 border-gray-300 dark:border-gray-700/60" :
                moduleId === "SCHEDULING" ? "bg-purple-100 dark:bg-purple-900/30 border-purple-300 dark:border-purple-700" :
                moduleId === "PEOPLE" ? "bg-orange-300 dark:bg-orange-700/60 border-orange-400 dark:border-orange-600/70" :
                moduleId === "BASIC" ? "bg-white dark:bg-white/20 border-gray-300 dark:border-gray-600" :
                "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
              }`}
            >
              {moduleId === "ADMIN" && <Settings className="h-4 w-4" />}
              {moduleId === "SCHEDULING" && <Calendar className="h-4 w-4" />}
              {moduleId === "PEOPLE" && <UserCheck className="h-4 w-4" />}
              {moduleId === "BASIC" && <CheckSquare className="h-4 w-4" />}
              <span className="text-sm dark:text-gray-300">
                {moduleId === "ADMIN" && "Administração"}
                {moduleId === "SCHEDULING" && "Agendamento"}
                {moduleId === "PEOPLE" && "Pessoas"}
                {moduleId === "BASIC" && "Básico"}
              </span>
            </div>
          ))}
          {(!selectedModules || selectedModules.filter(m => !["RH", "FINANCIAL"].includes(m)).length === 0) && (
            <p className="text-sm text-neutral-500 dark:text-gray-400">
              Nenhum módulo atribuído
            </p>
          )}
        </div>
      </div>

      {/* Lista de permissões */}
      <div className="space-y-6">


        {/* Agrupar permissões por módulo */}
        {["ADMIN", "SCHEDULING", "BASIC", "PEOPLE"]
          .filter(moduleId => PERMISSIONS_CONFIG[moduleId])
          .map(moduleId => {
            const moduleConfig = PERMISSIONS_CONFIG[moduleId];
          // Só mostrar módulos que o usuário tem acesso
          if (!selectedModules.includes(moduleId)) return null;

          // Filtrar permissões deste módulo (excluindo permissões de notificação)
          const modulePermissions = filteredPermissions.filter(
            (p) => p.moduleId === moduleId && !p.id.startsWith('notifications.')
          );

          if (modulePermissions.length === 0) return null;

          return (
            <div key={moduleId} className="mb-6 border rounded-lg overflow-hidden dark:border-gray-700">
              {/* Cabeçalho do módulo */}
              <div
                className={`p-4 flex items-center justify-between border-b dark:border-gray-700 cursor-pointer ${
                  moduleId === "ADMIN" ? "bg-gray-200 dark:bg-gray-800/60" :
                  moduleId === "PEOPLE" ? "bg-orange-300 dark:bg-orange-700/60" :
                  moduleId === "SCHEDULING" ? "bg-purple-100 dark:bg-purple-900/30" :
                  moduleId === "FINANCIAL" ? "bg-green-100 dark:bg-green-900/30" :
                  moduleId === "RH" ? "bg-blue-100 dark:bg-blue-900/30" :
                  moduleId === "BASIC" ? "bg-white dark:bg-white/20" :
                  "bg-neutral-50 dark:bg-gray-800"
                }`}
                onClick={() => toggleModuleExpansion(moduleId)}
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-full bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400">
                    {moduleId === "ADMIN" && <Settings className="h-5 w-5" />}
                    {moduleId === "RH" && <Users className="h-5 w-5" />}
                    {moduleId === "FINANCIAL" && <DollarSign className="h-5 w-5" />}
                    {moduleId === "SCHEDULING" && <Calendar className="h-5 w-5" />}
                    {moduleId === "PEOPLE" && <UserCheck className="h-5 w-5" />}
                    {moduleId === "BASIC" && <CheckSquare className="h-5 w-5" />}
                  </div>
                  <div>
                    <h3 className="font-medium text-neutral-800 dark:text-gray-200">{moduleConfig.name}</h3>
                    <p className="text-sm text-neutral-500 dark:text-gray-400">
                      {moduleId === "ADMIN" && "Permissões para gerenciamento administrativo"}
                      {moduleId === "RH" && "Permissões para recursos humanos"}
                      {moduleId === "FINANCIAL" && "Permissões para gestão financeira"}
                      {moduleId === "SCHEDULING" && "Permissões para agendamento"}
                      {moduleId === "PEOPLE" && "Permissões para gerenciamento de pessoas"}
                      {moduleId === "BASIC" && "Permissões básicas do sistema"}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleModulePermissions(moduleId);
                    }}
                    className="px-3 py-1 rounded text-sm font-medium bg-slate-500 text-white hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700"
                  >
                    Selecionar todas
                  </button>
                  {expandedModules[moduleId] ? (
                    <ChevronDown className="h-5 w-5 text-neutral-500 dark:text-gray-400" />
                  ) : (
                    <ChevronRight className="h-5 w-5 text-neutral-500 dark:text-gray-400" />
                  )}
                </div>
              </div>

              {/* Lista de permissões do módulo */}
              {expandedModules[moduleId] && (
                <div className="p-4 divide-y dark:divide-gray-700 dark:bg-gray-850">
                  {modulePermissions.map((permission) => (
                    <div key={permission.id} className="py-3 first:pt-0 last:pb-0">
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          <input
                            type="checkbox"
                            id={permission.id}
                            checked={selectedPermissions.includes(permission.id)}
                            onChange={() => togglePermission(permission.id)}
                            className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 focus:ring-slate-500 dark:bg-gray-700 dark:checked:bg-slate-500"
                          />
                        </div>

                        <div className="flex-1">
                          <label
                            htmlFor={permission.id}
                            className="block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer"
                          >
                            {permission.name}
                          </label>
                          <p className="mt-1 text-sm text-neutral-600 dark:text-gray-400">
                            {permission.description}
                          </p>
                          <div className="mt-1 text-xs text-neutral-500 dark:text-gray-500">
                            ID: {permission.id}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          );
        })}

        {/* Seção especial para permissões de notificação */}
        {(() => {
          const notificationPermissions = filteredPermissions.filter(
            (p) => p.id.startsWith('notifications.')
          );
          
          if (notificationPermissions.length > 0) {
            return (
              <div className="mb-6 border rounded-lg overflow-hidden dark:border-gray-700">
                <div
                  className="relative bg-neutral-50 dark:bg-gray-800 p-4 flex items-center justify-between border-b dark:border-gray-700 cursor-pointer overflow-hidden"
                  onClick={() => toggleModuleExpansion('NOTIFICATIONS')}
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 via-blue-500/20 to-purple-500/20"></div>
                  <div className="flex items-center gap-3 relative z-10">
                    <div className="p-2 rounded-full bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400">
                      <Bell className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="font-medium text-neutral-800 dark:text-gray-200">Notificações</h3>
                      <p className="text-sm text-neutral-500 dark:text-gray-400">
                        Controle quais notificações este usuário pode receber
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 relative z-10">
                    <button
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        const notificationPermissionIds = notificationPermissions.map(p => p.id);
                        const allSelected = notificationPermissionIds.every(id => selectedPermissions.includes(id));
                        
                        if (allSelected) {
                          setSelectedPermissions(prev => 
                            prev.filter(id => !notificationPermissionIds.includes(id))
                          );
                        } else {
                          setSelectedPermissions(prev => 
                            [...new Set([...prev, ...notificationPermissionIds])]
                          );
                        }
                      }}
                      className="px-3 py-1 rounded text-sm font-medium bg-slate-500 text-white hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700"
                    >
                      Selecionar todas
                    </button>
                    {expandedModules['NOTIFICATIONS'] ? (
                      <ChevronDown className="h-5 w-5 text-neutral-500 dark:text-gray-400" />
                    ) : (
                      <ChevronRight className="h-5 w-5 text-neutral-500 dark:text-gray-400" />
                    )}
                  </div>
                </div>
                {expandedModules['NOTIFICATIONS'] && (
                  <div className="p-4 divide-y divide-gray-200 dark:divide-gray-700 dark:bg-gray-850">
                    {notificationPermissions.map((permission) => (
                      <div key={permission.id} className="py-3 first:pt-0 last:pb-0">
                        <div className="flex items-start gap-3">
                          <div className="flex-shrink-0 mt-0.5">
                            <input
                              type="checkbox"
                              id={permission.id}
                              checked={selectedPermissions.includes(permission.id)}
                              onChange={() => togglePermission(permission.id)}
                              className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:checked:bg-blue-600"
                            />
                          </div>
                          <div className="flex-1">
                            <label
                              htmlFor={permission.id}
                              className="block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer"
                            >
                              {permission.name}
                            </label>
                            <p className="mt-1 text-sm text-neutral-600 dark:text-gray-400">
                              {permission.description}
                            </p>
                            <div className="mt-1 text-xs text-neutral-500 dark:text-gray-500">
                              ID: {permission.id}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          }
          return null;
        })()}

        {/* Mensagem quando não há permissões */}
        {filteredPermissions.length === 0 && (
          <div className="text-center py-8">
            <p className="text-neutral-500 dark:text-gray-400">
              Nenhuma permissão encontrada.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default UserPermissionsTab;