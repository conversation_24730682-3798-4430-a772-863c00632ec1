const multer = require("multer");
const path = require("path");
const crypto = require("crypto");
const fs = require("fs");

// Caminho base para uploads
const UPLOAD_PATH = process.env.NODE_ENV === 'production'
  ? path.resolve("/tmp/uploads")
  : path.resolve("/usr/src/app/uploads");


// Garante que o diretório de uploads existe
const ensureUploadDirectoryExists = () => {
  const directories = [
    UPLOAD_PATH,
    path.join(UPLOAD_PATH, "documents")
  ];

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
};

// Cria os diretórios necessários
ensureUploadDirectoryExists();

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const userId = req.user.id;
    const userUploadPath = path.join(UPLOAD_PATH, "documents", userId);

    // Cria diretório específico para o usuário se não existir
    if (!fs.existsSync(userUploadPath)) {
      fs.mkdirSync(userUploadPath, { recursive: true });
    }

    cb(null, userUploadPath);
  },
  filename: (req, file, cb) => {
    // Gera um nome único para o arquivo
    const uniqueSuffix = crypto.randomBytes(16).toString("hex");
    // Mantém o nome original do arquivo, mas adiciona um sufixo único
    const filename = `${path.parse(file.originalname).name}-${uniqueSuffix}${path.extname(file.originalname)}`;

    cb(null, filename);
  },
});

const fileFilter = (req, file, cb) => {
  // Lista de tipos MIME permitidos
  const allowedMimeTypes = [
    // Imagens
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/bmp',
    'image/webp',
    'image/svg+xml',
    
    // PDFs
    'application/pdf',
    
    // Documentos do Microsoft Office
    'application/msword', // .doc
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/vnd.ms-excel', // .xls
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-powerpoint', // .ppt
    'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
    
    // Documentos de texto
    'text/plain', // .txt
    'text/csv', // .csv
    'application/rtf', // .rtf
    
    // Outros formatos comuns
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    'application/json',
    'application/xml',
    'text/xml'
  ];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`Tipo de arquivo não permitido: ${file.mimetype}. Tipos aceitos: imagens, PDFs, documentos do Office, arquivos de texto, ZIP, RAR, 7z, JSON, XML`));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // Limite de 50MB
  },
});

module.exports = upload;