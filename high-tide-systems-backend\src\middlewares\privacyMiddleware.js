const DataPrivacyUtils = require('../utils/dataPrivacy');
const prisma = require('../utils/prisma');

/**
 * Cache para configurações de privacidade
 * Evita consultas desnecessárias ao banco
 */
const privacyCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutos

/**
 * Busca as configurações de privacidade da empresa
 * @param {string} companyId - ID da empresa
 * @returns {object|null} - Configurações de privacidade
 */
async function getPrivacySettings(companyId) {
  if (!companyId) return null;

  // Verificar cache
  const cacheKey = `privacy_${companyId}`;
  const cached = privacyCache.get(cacheKey);
  
  if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
    return cached.settings;
  }

  try {
    const preferences = await prisma.companyPreference.findUnique({
      where: { companyId },
      select: { preferences: true }
    });

    const privacySettings = preferences?.preferences || null;
    
    // Atualizar cache
    privacyCache.set(cacheKey, {
      settings: privacySettings,
      timestamp: Date.now()
    });

    return privacySettings;
  } catch (error) {
    console.error('Erro ao buscar configurações de privacidade:', error);
    return null;
  }
}

/**
 * Middleware para aplicar máscaras de privacidade automaticamente
 * @param {string} entityType - Tipo da entidade (user, client, patient)
 * @returns {function} - Middleware function
 */
function applyPrivacyMasks(entityType) {
  return async (req, res, next) => {
    // Interceptar o método res.json para aplicar máscaras
    const originalJson = res.json.bind(res);
    
    res.json = async function(data) {
      try {
        // Só aplicar máscaras se há dados e o usuário tem empresa
        if (!data || !req.user?.companyId) {
          return originalJson(data);
        }

        // Buscar configurações de privacidade
        const privacySettings = await getPrivacySettings(req.user.companyId);
        
        if (!privacySettings || !DataPrivacyUtils.hasActivePrivacySettings(entityType, privacySettings)) {
          return originalJson(data);
        }

        let maskedData = data;

        // Aplicar máscaras baseado na estrutura dos dados
        if (Array.isArray(data)) {
          // Lista de itens
          maskedData = DataPrivacyUtils.applyPrivacyMasksToList(entityType, data, privacySettings);
        } else if (data.data && Array.isArray(data.data)) {
          // Resposta paginada com array em data
          maskedData = {
            ...data,
            data: DataPrivacyUtils.applyPrivacyMasksToList(entityType, data.data, privacySettings)
          };
        } else if (data.users && Array.isArray(data.users)) {
          // Resposta específica de usuários
          maskedData = {
            ...data,
            users: DataPrivacyUtils.applyPrivacyMasksToList(entityType, data.users, privacySettings)
          };
        } else if (data.clients && Array.isArray(data.clients)) {
          // Resposta específica de clientes
          maskedData = {
            ...data,
            clients: DataPrivacyUtils.applyPrivacyMasksToList(entityType, data.clients, privacySettings)
          };
        } else if (data.persons && Array.isArray(data.persons)) {
          // Resposta específica de pessoas/pacientes
          maskedData = {
            ...data,
            persons: DataPrivacyUtils.applyPrivacyMasksToList(entityType, data.persons, privacySettings)
          };
        } else if (typeof data === 'object' && !Array.isArray(data) && data.id) {
          // Objeto único com ID (provavelmente uma entidade)
          maskedData = DataPrivacyUtils.applyPrivacyMasks(entityType, data, privacySettings);
        }

        return originalJson(maskedData);
      } catch (error) {
        console.error('Erro ao aplicar máscaras de privacidade:', error);
        // Em caso de erro, retornar dados originais
        return originalJson(data);
      }
    };

    next();
  };
}

/**
 * Middleware específico para usuários
 */
const applyUserPrivacyMasks = applyPrivacyMasks('user');

/**
 * Middleware específico para clientes
 */
const applyClientPrivacyMasks = applyPrivacyMasks('client');

/**
 * Middleware específico para pacientes/pessoas
 */
const applyPatientPrivacyMasks = applyPrivacyMasks('patient');

/**
 * Limpar cache de privacidade (útil quando as configurações mudam)
 * @param {string} companyId - ID da empresa (opcional, limpa tudo se não especificado)
 */
function clearPrivacyCache(companyId = null) {
  if (companyId) {
    privacyCache.delete(`privacy_${companyId}`);
  } else {
    privacyCache.clear();
  }
}

/**
 * Middleware para limpar cache quando as preferências são atualizadas
 */
function clearCacheOnPreferencesUpdate(req, res, next) {
  const originalJson = res.json.bind(res);
  
  res.json = function(data) {
    // Limpar cache após atualização bem-sucedida
    if (res.statusCode >= 200 && res.statusCode < 300) {
      const companyId = req.body?.companyId || req.user?.companyId;
      if (companyId) {
        clearPrivacyCache(companyId);
      }
    }
    
    return originalJson(data);
  };

  next();
}

module.exports = {
  applyPrivacyMasks,
  applyUserPrivacyMasks,
  applyClientPrivacyMasks,
  applyPatientPrivacyMasks,
  clearPrivacyCache,
  clearCacheOnPreferencesUpdate,
  getPrivacySettings
};