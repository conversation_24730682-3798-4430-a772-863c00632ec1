import { useState, useEffect } from 'react';
import { preferencesService } from '@/services/preferencesService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { useCompanySelection } from '@/contexts/CompanySelectionContext';

/**
 * Hook para gerenciar preferências por empresa
 * Para SYSTEM_ADMIN: permite selecionar qualquer empresa
 * Para outros roles: usa a empresa do usuário atual
 */
export function useCompanyPreferences() {
  const { user, isSystemAdmin } = useAuth();
  const { toast_error, toast_success } = useToast();
  const { 
    selectedCompanyId, 
    companies, 
    isLoading: isLoadingCompanies,
    selectedCompany,
    getEffectiveCompanyId,
    canSelectCompany
  } = useCompanySelection();
  
  const [preferences, setPreferences] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Carregar preferências da empresa selecionada
  useEffect(() => {
    async function loadPreferences() {
      const companyId = getEffectiveCompanyId();
      
      try {
        setIsLoading(true);
        let data;
        
        if (isSystemAdmin() && companyId) {
          // SYSTEM_ADMIN pode acessar preferências de qualquer empresa
          data = await preferencesService.getForCompany(companyId);
        } else if (!isSystemAdmin()) {
          // Outros roles usam suas próprias preferências
          data = await preferencesService.get();
        } else {
          // SYSTEM_ADMIN operando sem empresa - usar preferências padrão
          data = getDefaultPreferences();
        }
        
        setPreferences(data);
      } catch (error) {
        console.error('Erro ao carregar preferências:', error);
        toast_error('Erro ao carregar preferências da empresa');
        // Definir preferências padrão em caso de erro
        setPreferences(getDefaultPreferences());
      } finally {
        setIsLoading(false);
      }
    }

    loadPreferences();
  }, [selectedCompanyId, isSystemAdmin, toast_error, getEffectiveCompanyId]);

  // Salvar preferências da empresa selecionada
  const savePreferences = async (newPreferences) => {
    const companyId = getEffectiveCompanyId();
    
    if (isSystemAdmin() && !companyId) {
      toast_error('Selecione uma empresa para salvar as preferências ou opere no modo global');
      return false;
    }

    try {
      let data;
      
      if (isSystemAdmin() && companyId) {
        // SYSTEM_ADMIN pode salvar preferências de qualquer empresa
        data = await preferencesService.saveForCompany(companyId, newPreferences);
      } else if (!isSystemAdmin()) {
        // Outros roles salvam suas próprias preferências
        data = await preferencesService.save(newPreferences);
      } else {
        // SYSTEM_ADMIN operando sem empresa - não permitir salvamento
        toast_error('Selecione uma empresa para salvar as preferências');
        return false;
      }
      
      setPreferences(data);
      toast_success('Preferências salvas com sucesso!');
      return true;
    } catch (error) {
      console.error('Erro ao salvar preferências:', error);
      toast_error('Erro ao salvar preferências da empresa');
      return false;
    }
  };

  // Preferências padrão
  const getDefaultPreferences = () => ({
    user: {
      userEmail: true,
      userPassword: true,
      userName: true,
      userRole: true,
      userProfilePhoto: false,
      require2FA: false
    },
    client: {
      clientName: true,
      clientBirthDate: true,
      clientCpfCnpj: true,
      clientPhone: false,
      clientEmail: true,
      clientAddress: false,
      clientGender: false,
      clientMaritalStatus: false,
      clientProfession: false,
      clientProfilePhoto: false,
      requireEmailConfirmation: false,
      requirePhoneConfirmation: false,
      allowMultipleDocuments: false,
      requireResidentialAddress: false,
      requireCommercialAddress: false
    },
    userPrivacy: {
      hideUserCpf: false,
      hideUserCnpj: false,
      hideUserEmail: false,
      hideUserPhone: false,
      hideUserAddress: false,
      hideUserBirthDate: false,
      hideUserLastLogin: false
    },
    clientPrivacy: {
      hideClientEmail: false,
      hideClientFullName: false
    },
    patientPrivacy: {
      hidePatientCpf: false,
      hidePatientEmail: false,
      hidePatientPhone: false,
      hidePatientAddress: false,
      hidePatientBirthDate: false,
      hidePatientNotes: false,
      hidePatientProfileImage: false
    }
  });

  return {
    // Estados
    selectedCompanyId,
    companies,
    preferences,
    isLoading,
    isLoadingCompanies,
    selectedCompany,
    
    // Funções
    savePreferences,
    getEffectiveCompanyId,
    
    // Flags
    canSelectCompany,
    hasMultipleCompanies: companies.length > 1
  };
}