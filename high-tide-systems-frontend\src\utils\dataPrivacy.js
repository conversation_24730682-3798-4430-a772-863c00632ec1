/**
 * Utilitários para mascarar e ocultar dados sensíveis
 * Baseado nas configurações de privacidade da empresa
 */

export const DataPrivacyUtils = {
  /**
   * Mascara CPF/CNPJ
   * @param {string} document - CPF ou CNPJ
   * @returns {string} - Documento mascarado
   */
  maskDocument(document) {
    if (!document) return '';
    
    const cleanDoc = document.replace(/\D/g, '');
    
    if (cleanDoc.length === 11) {
      // CPF: 123.456.789-10 -> ***.***.***-10
      return cleanDoc.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '***.***.$3-$4');
    } else if (cleanDoc.length === 14) {
      // CNPJ: 12.345.678/0001-90 -> **.***.***/****-**
      return cleanDoc.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '**.***.***/****-**');
    }
    
    return '***';
  },

  /**
   * Mascara e-mail
   * @param {string} email - E-mail
   * @returns {string} - E-mail mascarado
   */
  maskEmail(email) {
    if (!email || !email.includes('@')) return '***@***.***';
    
    const [localPart, domain] = email.split('@');
    const [domainName, extension] = domain.split('.');
    
    const maskedLocal = localPart.length > 2 
      ? localPart.substring(0, 2) + '*'.repeat(localPart.length - 2)
      : '***';
      
    const maskedDomain = domainName.length > 2
      ? domainName.substring(0, 1) + '*'.repeat(domainName.length - 2) + domainName.slice(-1)
      : '***';
    
    return `${maskedLocal}@${maskedDomain}.${extension || '***'}`;
  },

  /**
   * Mascara telefone
   * @param {string} phone - Telefone
   * @returns {string} - Telefone mascarado
   */
  maskPhone(phone) {
    if (!phone) return '';
    
    const cleanPhone = phone.replace(/\D/g, '');
    
    if (cleanPhone.length === 11) {
      // Celular: (11) 99999-9999 -> (11) ****-9999
      return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) ****-$3');
    } else if (cleanPhone.length === 10) {
      // Fixo: (11) 9999-9999 -> (11) ****-9999
      return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) ****-$3');
    }
    
    return '(***) ****-****';
  },

  /**
   * Mascara endereço
   * @param {string} address - Endereço
   * @returns {string} - Endereço mascarado
   */
  maskAddress(address) {
    if (!address) return '';
    
    // Mantém apenas os primeiros 10 caracteres e mascara o resto
    if (address.length <= 10) {
      return '*'.repeat(address.length);
    }
    
    return address.substring(0, 10) + '*'.repeat(Math.max(0, address.length - 10));
  },

  /**
   * Mascara data de nascimento
   * @param {string|Date} birthDate - Data de nascimento
   * @returns {string} - Data mascarada
   */
  maskBirthDate(birthDate) {
    if (!birthDate) return '';
    
    const date = new Date(birthDate);
    if (isNaN(date.getTime())) return '***';
    
    // Mantém apenas o ano, mascara dia e mês
    const year = date.getFullYear();
    return `**/**/****`;
  },

  /**
   * Mascara IP
   * @param {string} ip - Endereço IP
   * @returns {string} - IP mascarado
   */
  maskIp(ip) {
    if (!ip) return '';
    
    const parts = ip.split('.');
    if (parts.length === 4) {
      // Mantém apenas o primeiro octeto
      return `${parts[0]}.***.***.***`;
    }
    
    return '***.***.***.***';
  },

  /**
   * Mascara nome completo
   * @param {string} fullName - Nome completo
   * @returns {string} - Nome mascarado
   */
  maskFullName(fullName) {
    if (!fullName) return '';
    
    const names = fullName.trim().split(' ');
    
    if (names.length === 1) {
      // Apenas um nome - mantém primeira e última letra
      if (names[0].length <= 2) return '*'.repeat(names[0].length);
      return names[0][0] + '*'.repeat(names[0].length - 2) + names[0].slice(-1);
    }
    
    // Múltiplos nomes - mantém primeiro nome e última inicial
    const firstName = names[0];
    const lastInitial = names[names.length - 1][0];
    
    return `${firstName} ${'*'.repeat(names.length - 2)} ${lastInitial}.`;
  },

  /**
   * Mascara observações/notas
   * @param {string} notes - Observações
   * @returns {string} - Observações mascaradas
   */
  maskNotes(notes) {
    if (!notes) return '';
    
    // Mantém apenas os primeiros 20 caracteres se for muito longo
    if (notes.length <= 20) {
      return '*'.repeat(notes.length);
    }
    
    return notes.substring(0, 20) + '... [DADOS SENSÍVEIS OCULTOS]';
  },

  /**
   * Retorna string vazia para ocultar completamente
   * @returns {string}
   */
  hideCompletely() {
    return '[OCULTO]';
  },

  /**
   * Mascara qualquer valor genérico
   * @param {any} value - Valor a ser mascarado
   * @param {string} type - Tipo de máscara (cpf, email, phone, etc.)
   * @returns {string} - Valor mascarado
   */
  maskValue(value, type) {
    if (!value) return '';
    
    switch (type.toLowerCase()) {
      case 'cpf':
      case 'cnpj':
      case 'document':
        return this.maskDocument(value);
      case 'email':
        return this.maskEmail(value);
      case 'phone':
      case 'telefone':
        return this.maskPhone(value);
      case 'address':
      case 'endereco':
        return this.maskAddress(value);
      case 'birthdate':
      case 'nascimento':
        return this.maskBirthDate(value);
      case 'ip':
        return this.maskIp(value);
      case 'fullname':
      case 'nome':
        return this.maskFullName(value);
      case 'notes':
      case 'observacoes':
        return this.maskNotes(value);
      default:
        return this.hideCompletely();
    }
  }
};

/**
 * Mapeamento de campos para tipos de máscara
 */
export const FIELD_MASK_MAPPING = {
  // Usuários
  hideUserCpf: 'cpf',
  hideUserCnpj: 'cnpj', 
  hideUserEmail: 'email',
  hideUserPhone: 'phone',
  hideUserAddress: 'address',
  hideUserBirthDate: 'birthdate',
  hideUserLastLoginIp: 'ip',
  
  // Clientes
  hideClientEmail: 'email',
  hideClientFullName: 'fullname',
  
  // Pacientes
  hidePatientCpf: 'cpf',
  hidePatientEmail: 'email',
  hidePatientPhone: 'phone',
  hidePatientAddress: 'address',
  hidePatientBirthDate: 'birthdate',
  hidePatientNotes: 'notes',
  hidePatientProfileImage: 'hide', // Para imagens, apenas ocultar
};

export default DataPrivacyUtils;