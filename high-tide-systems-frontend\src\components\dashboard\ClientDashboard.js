// src/components/dashboard/ClientDashboard.js
'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Calendar, Users, User, Phone, Mail } from 'lucide-react';
import ModuleCard from '@/components/dashboard/ModuleCard';
import { ModuleTable } from '@/components/ui';
import TableSettings from '@/components/dashboard/TableSettings';
import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';
import { personsService } from '@/app/modules/people/services/personsService';
import { formatDate } from '@/utils/dateUtils';
import { formatTime } from '@/utils/dateFormatters';
import WelcomeCard from '@/components/dashboard/WelcomeCard';

const ClientDashboard = () => {
  const { user } = useAuth();
  const router = useRouter();
  const [appointments, setAppointments] = useState([]);
  const [persons, setPersons] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [visibleColumns, setVisibleColumns] = useState(['date', 'time', 'patient', 'service', 'professional', 'status']);

  useEffect(() => {
    const loadClientData = async () => {
      setIsLoading(true);
      try {
        // Load client's persons
        const personsResponse = await personsService.getPersons({
          clientId: user.id,
          limit: 100 // Get all persons for this client
        });

        // Processar as pessoas para adicionar URLs completas de imagens
        const personsData = personsResponse.persons || personsResponse.people || [];
        const processedPersons = personsData.map(person => {
          // Se a pessoa tem uma imagem de perfil, adicionar a URL completa
          if (person.profileImageUrl && !person.profileImageFullUrl) {
            person.profileImageFullUrl = personsService.getProfileImageUrl(
              person.id,
              person.profileImageUrl
            );
            console.log(`URL completa gerada para ${person.fullName}:`, person.profileImageFullUrl);
          }
          return person;
        });

        setPersons(processedPersons);

        // Load client's appointments - get all appointments for this client
        // Use the correct parameter name based on the backend controller
        const appointmentsResponse = await appointmentService.getAppointments({
          clientId: user.id, // This will be handled by the backend controller
          limit: 1000, // Get all appointments
          status: ['PENDING', 'CONFIRMED', 'COMPLETED'], // Include all relevant statuses
          include_insurance_info: true // Include insurance limit information
        });
        const appointmentsData = appointmentsResponse.appointments || [];
        console.log('Appointments loaded for client:', appointmentsData);
        setAppointments(appointmentsData);
      } catch (error) {
        console.error('Error loading client data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user?.id) {
      loadClientData();
    }
  }, [user]);

  const handleModuleClick = (moduleId) => {
    if (moduleId === 'people') {
      router.push('/dashboard/people/persons');
    } else if (moduleId === 'scheduler') {
      router.push('/dashboard/scheduler/calendar');
    }
  };

  // Garantir que o usuário tenha o papel de cliente e o nome correto
  const userWithRole = useMemo(() => {
    // Verificar se o usuário tem pessoas relacionadas e usar o nome da primeira pessoa
    const mainPerson = user?.persons && user.persons.length > 0 ? user.persons[0] : null;

    return {
      ...user,
      role: 'CLIENT',
      // Prioridade: nome da pessoa principal > nome do usuário > login > 'Cliente'
      fullName: mainPerson?.fullName || user?.fullName || user?.login || 'Cliente'
    };
  }, [user]);

  // Log para debug
  console.log('Cliente autenticado:', userWithRole);

  return (
    <div className="space-y-8">
      {/* Cartão de boas-vindas adaptado para clientes */}
      <WelcomeCard user={userWithRole} />

      {/* Client's Modules */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <ModuleCard
          title="Pessoas"
          icon={Users}
          description="Gerencie seus dados pessoais e informações de pessoas relacionadas. Atualize perfis, contatos e documentos importantes."
          onClick={() => handleModuleClick('people')}
          isAccessible={true}
          moduleId="people"
        />
        <ModuleCard
          title="Agendamentos"
          icon={Calendar}
          description="Visualize seus agendamentos, consultas marcadas e histórico de atendimentos. Acompanhe datas, horários e profissionais."
          onClick={() => handleModuleClick('scheduler')}
          isAccessible={true}
          moduleId="scheduler"
        />
      </div>

      {/* Client's Persons */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Users className="mr-2 text-people-500" size={20} />
          Pessoas Relacionadas
        </h2>
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-module-people-border/30 dark:border-module-people-border-dark/30 overflow-hidden">
          <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-gray-700">
            <thead className="bg-module-people-bg/10 dark:bg-module-people-bg-dark/10">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-module-people-text/70 dark:text-module-people-text-dark/70" style={{width: '30%'}}>Nome</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-module-people-text/70 dark:text-module-people-text-dark/70" style={{width: '25%'}}>Email</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-module-people-text/70 dark:text-module-people-text-dark/70" style={{width: '20%'}}>Telefone</th>
                <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-module-people-text/70 dark:text-module-people-text-dark/70" style={{width: '25%'}}>Ações</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700">
              {isLoading ? (
                <tr>
                  <td colSpan={4} className="py-8 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <div className="h-8 w-8 animate-spin rounded-full border-2 border-people-500 border-t-transparent"></div>
                      <p className="mt-2 text-gray-500">Carregando...</p>
                    </div>
                  </td>
                </tr>
              ) : persons.length === 0 ? (
                <tr>
                  <td colSpan={4} className="py-12 text-center">
                    <div className="flex flex-col items-center justify-center">
                      <User size={24} className="text-gray-400 mb-3" />
                      <p className="text-lg font-medium text-gray-900 dark:text-white">Nenhuma pessoa encontrada</p>
                    </div>
                  </td>
                </tr>
              ) : (
                persons.map((person) => (
                  <tr key={person.id} className="hover:bg-module-people-bg/5 dark:hover:bg-module-people-bg-dark/5">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                          {person.profileImageUrl ? (
                            <img
                              src={person.profileImageFullUrl || personsService.getProfileImageUrl(person.id, person.profileImageUrl)}
                              alt={person.fullName}
                              className="h-10 w-10 rounded-full object-cover"
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.parentNode.innerHTML = '<div class="flex items-center justify-center w-full h-full"><svg class="h-5 w-5 text-gray-500" fill="currentColor" viewBox="0 0 24 24"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg></div>';
                              }}
                            />
                          ) : (
                            <User className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                          )}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {person.fullName}
                          </div>
                          {person.relationship && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {person.relationship}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white flex items-center">
                        {person.email ? (
                          <>
                            <Mail size={14} className="mr-2 text-gray-500" />
                            <span>{person.email}</span>
                          </>
                        ) : (
                          <span className="text-gray-400 dark:text-gray-500">-</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white flex items-center">
                        {person.phone ? (
                          <>
                            <Phone size={14} className="mr-2 text-gray-500" />
                            <span>{person.phone}</span>
                          </>
                        ) : (
                          <span className="text-gray-400 dark:text-gray-500">-</span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-right">
                      <button
                        onClick={() => router.push(`/dashboard/people/persons/${person.id}`)}
                        className="text-people-600 hover:text-people-900 dark:text-people-400 dark:hover:text-people-300"
                      >
                        Ver Detalhes
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
          </div>
        </div>
      </div>

      {/* Client's Appointments */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Calendar className="mr-2 text-scheduler-500" size={20} />
          Meus Agendamentos
        </h2>
        <ModuleTable
          moduleColor="scheduler"
          columns={[
            { header: 'Data', field: 'date', width: '15%', dataType: 'date' },
            { header: 'Horário', field: 'time', width: '12%', sortable: false },
            { header: 'Paciente', field: 'patient', width: '20%' },
            { header: 'Serviço', field: 'service', width: '20%' },
            { header: 'Profissional', field: 'professional', width: '18%' },
            { header: 'Status', field: 'status', width: '15%' }
          ]}
          data={appointments}
          isLoading={isLoading}
          emptyMessage="Nenhum agendamento encontrado"
          emptyIcon={<Calendar size={24} />}
          tableId="client-appointments-table"
          defaultSortField="date"
          defaultSortDirection="desc"
          currentPage={currentPage}
          totalPages={Math.ceil(appointments.length / itemsPerPage)}
          totalItems={appointments.length}
          onPageChange={setCurrentPage}
          showPagination={true}
          itemsPerPage={itemsPerPage}
          headerContent={
            <TableSettings
              moduleColor="scheduler"
              columns={[
                { header: 'Data', field: 'date' },
                { header: 'Horário', field: 'time' },
                { header: 'Paciente', field: 'patient' },
                { header: 'Serviço', field: 'service' },
                { header: 'Profissional', field: 'professional' },
                { header: 'Status', field: 'status' }
              ]}
              visibleColumns={visibleColumns}
              onToggleColumn={(columnId) => {
                setVisibleColumns(prev => 
                  prev.includes(columnId) 
                    ? prev.filter(id => id !== columnId)
                    : [...prev, columnId]
                );
              }}
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={(newItemsPerPage) => {
                setItemsPerPage(newItemsPerPage);
                setCurrentPage(1);
              }}
              itemsPerPageOptions={[5, 10, 25, 50, 100]}
            />
          }
          renderRow={(appointment, _index, moduleColors) => (
            <tr key={appointment.id} className={moduleColors.hoverBg}>
              {visibleColumns.includes('date') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {formatDate(appointment.startDate)}
                  </div>
                </td>
              )}
              {visibleColumns.includes('time') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {formatTime(appointment.startDate)} - {formatTime(appointment.endDate)}
                  </div>
                </td>
              )}
              {visibleColumns.includes('patient') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mr-3">
                      <User className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                    </div>
                    <div className="text-sm text-gray-900 dark:text-white">
                      {appointment.personfullName || appointment.Person?.[0]?.fullName || 'Não especificado'}
                    </div>
                  </div>
                </td>
              )}
              {visibleColumns.includes('service') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {appointment.serviceType?.name || appointment.serviceTypefullName || appointment.title}
                  </div>
                </td>
              )}
              {visibleColumns.includes('professional') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {appointment.provider?.fullName || appointment.providerfullName || 'Não especificado'}
                  </div>
                </td>
              )}
              {visibleColumns.includes('status') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                    ${appointment.status === 'CONFIRMED'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : appointment.status === 'PENDING'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        : appointment.status === 'COMPLETED'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`}>
                    {appointment.status === 'CONFIRMED' ? 'Confirmado' :
                     appointment.status === 'PENDING' ? 'Pendente' :
                     appointment.status === 'CANCELLED' ? 'Cancelado' :
                     appointment.status === 'COMPLETED' ? 'Concluído' :
                     appointment.status === 'NO_SHOW' ? 'Não Compareceu' :
                     appointment.status}
                  </span>
                </td>
              )}
            </tr>
          )}
        />
      </div>
    </div>
  );
};

export default ClientDashboard;
