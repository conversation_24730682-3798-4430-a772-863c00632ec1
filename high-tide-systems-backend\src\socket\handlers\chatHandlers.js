// src/socket/handlers/chatHandlers.js
const prisma = require('../../utils/prisma');
const chatService = require('../../services/chat/chatService');
const messageService = require('../../services/chat/messageService');

/**
 * Registra os handlers de eventos de chat para um socket
 * @param {Object} socket - Socket do cliente
 * @param {Object} io - Instância do servidor Socket.IO
 */
const register = (socket, io) => {
  // Entrar em salas de conversas do usuário
  joinUserConversations(socket);

  // Evento para enviar mensagem
  socket.on('message:send', async (data, callback) => {
    try {
      // Validar dados
      if (!data.conversationId) {
        return callback({ 
          success: false, 
          error: 'Dados inválidos. conversationId é obrigatório.' 
        });
      }

      // Para mensagens com anexos, o content pode estar vazio, mas pelo menos um deve estar presente
      const hasContent = data.content && data.content.trim() !== '';
      const hasAttachment = data.contentType === 'ATTACHMENT' && data.metadata && data.metadata.attachments;
      
      if (!hasContent && !hasAttachment) {
        return callback({ 
          success: false, 
          error: 'Dados inválidos. Conteúdo ou anexo é obrigatório.' 
        });
      }

      // Log para debug
      console.log('[Socket.IO] Message data received:', {
        conversationId: data.conversationId,
        contentType: data.contentType,
        hasContent: !!data.content,
        contentLength: data.content?.length || 0,
        hasMetadata: !!data.metadata
      });

      // Verificar se o usuário é participante da conversa
      const isParticipant = await chatService.isConversationParticipant(
        data.conversationId, 
        socket.user.id
      );

      if (!isParticipant) {
        return callback({ 
          success: false, 
          error: 'Você não é participante desta conversa.' 
        });
      }

      // Criar mensagem
      const message = await messageService.createMessage({
        conversationId: data.conversationId,
        senderId: socket.user.id,
        content: data.content,
        contentType: data.contentType || 'TEXT',
        referencedMessageId: data.referencedMessageId,
        metadata: data.metadata
      });

      // Atualizar última mensagem da conversa
      await chatService.updateLastMessage(data.conversationId, message.id);

      // Incluir o tempId na mensagem se existir para facilitar a substituição no frontend
      const messageWithTempId = data.tempId ? { ...message, tempId: data.tempId } : message;
      
      // Emitir evento para todos os participantes da conversa
      io.to(`conversation:${data.conversationId}`).emit('message:new', messageWithTempId);

      // Retornar sucesso
      callback({ success: true, message });
    } catch (error) {
      console.error('[Socket.IO] Error sending message:', error);
      console.error('[Socket.IO] Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        code: error.code
      });
      callback({ success: false, error: 'Erro ao enviar mensagem.' });
    }
  });

  // Evento para marcar mensagens como lidas
  socket.on('message:read', async (data, callback) => {
    try {
      // Validar dados
      if (!data.conversationId || !data.messageId) {
        return callback({ 
          success: false, 
          error: 'Dados inválidos. conversationId e messageId são obrigatórios.' 
        });
      }

      // Verificar se é cliente
      const isClient = socket.user.isClient || socket.user.role === 'CLIENT';
      
      // Verificar se o usuário é participante da conversa
      const participant = await prisma.conversationParticipant.findFirst({
        where: {
          conversationId: data.conversationId,
          OR: isClient ? [
            { clientId: socket.user.id, leftAt: null }
          ] : [
            { userId: socket.user.id, leftAt: null }
          ]
        }
      });

      if (!participant) {
        return callback({ 
          success: false, 
          error: 'Você não é participante desta conversa.' 
        });
      }

      // Atualizar última mensagem lida
      await chatService.updateLastReadMessage(
        participant.id, 
        data.messageId
      );

      // Emitir evento para todos os participantes da conversa
      io.to(`conversation:${data.conversationId}`).emit('message:read', {
        conversationId: data.conversationId,
        userId: socket.user.id,
        messageId: data.messageId
      });

      // Retornar sucesso
      callback({ success: true });
    } catch (error) {
      console.error('[Socket.IO] Error marking message as read:', error);
      callback({ success: false, error: 'Erro ao marcar mensagem como lida.' });
    }
  });

  // Evento para indicar que o usuário está digitando
  socket.on('user:typing', async (data, callback) => {
    try {
      // Validar dados
      if (!data.conversationId) {
        return callback({ 
          success: false, 
          error: 'Dados inválidos. conversationId é obrigatório.' 
        });
      }

      // Verificar se o usuário é participante da conversa
      const isParticipant = await chatService.isConversationParticipant(
        data.conversationId, 
        socket.user.id
      );

      if (!isParticipant) {
        return callback({ 
          success: false, 
          error: 'Você não é participante desta conversa.' 
        });
      }

      // Emitir evento para todos os participantes da conversa, exceto o remetente
      socket.to(`conversation:${data.conversationId}`).emit('user:typing', {
        conversationId: data.conversationId,
        userId: socket.user.id,
        isTyping: data.isTyping || true
      });

      // Retornar sucesso
      callback({ success: true });
    } catch (error) {
      console.error('[Socket.IO] Error sending typing indicator:', error);
      callback({ success: false, error: 'Erro ao enviar indicador de digitação.' });
    }
  });
};

/**
 * Adiciona o socket às salas de todas as conversas do usuário
 * @param {Object} socket - Socket do cliente
 */
const joinUserConversations = async (socket) => {
  try {
    // Verificar se é cliente
    const isClient = socket.user.isClient || socket.user.role === 'CLIENT';
    
    // Buscar todas as conversas do usuário
    const participations = await prisma.conversationParticipant.findMany({
      where: isClient ? {
        clientId: socket.user.id,
        leftAt: null
      } : {
        userId: socket.user.id,
        leftAt: null
      },
      select: {
        conversationId: true
      }
    });

    // Entrar em cada sala de conversa
    for (const participation of participations) {
      socket.join(`conversation:${participation.conversationId}`);
    }

  } catch (error) {
    console.error('[Socket.IO] Error joining conversation rooms:', error);
  }
};

module.exports = {
  register
};
