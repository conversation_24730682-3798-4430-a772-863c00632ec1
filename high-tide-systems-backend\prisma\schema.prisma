generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum SystemModule {
  ADMIN
  RH
  FINANCIAL
  SCHEDULING
  PEOPLE
  ABAPLUS
  BASIC
}

enum SchedulingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
  NO_SHOW
}

enum RecurrenceType {
  OCCURRENCES
  END_DATE
}

enum UserRole {
  SYSTEM_ADMIN // Administrador do sistema com acesso total
  COMPANY_ADMIN // Administrador de uma empresa específica
  EMPLOYEE // Funcionário regular de uma empresa
}

enum BillingCycle {
  MONTHLY
  YEARLY
}

enum SubscriptionStatus {
  ACTIVE
  TRIAL
  PAST_DUE
  CANCELED
  INCOMPLETE
}

enum InvoiceStatus {
  PAID
  PENDING
  FAILED
}

enum ConversationType {
  INDIVIDUAL
  GROUP
}

enum MessageContentType {
  TEXT
  IMAGE
  FILE
  ATTACHMENT
  LINK
  SYSTEM
  SHARED_APPOINTMENT
  SHARED_PERSON
  SHARED_CLIENT
  SHARED_USER
  SHARED_SERVICE_TYPE
  SHARED_LOCATION
  SHARED_WORKING_HOURS
  SHARED_INSURANCE
  SHARED_INSURANCE_LIMIT
}

enum MessageDeliveryStatus {
  SENT
  DELIVERED
  READ
}

enum EvaluationType {
  SKILL_ACQUISITION
  BEHAVIOR_REDUCTION
}

enum ScoreType {
  ALWAYS
  FREQUENTLY
  SOMETIMES
  RARELY
  NEVER
  NOT_APPLICABLE
}

enum TeachingType {
  DISCRETE_TRIAL_STRUCTURED
  TASK_ANALYSIS
  NATURALISTIC_TEACHING
  DISCRETE_TRIAL_INTERSPERSED
}

enum CriteriaDegree {
  OMISSION
  ERROR
  MORE_INTRUSIVE
  PARTIALLY_INTRUSIVE
  LESS_INTRUSIVE
  INDEPENDENT
}

enum ProgramType {
  PROGRAM_CATALOG
  LEARNING_PROGRAM
}

enum ProgramStatus {
  unallocated
  inTraining
  completed
}

// Enum para os campos Sim, Não, Às vezes
enum SimNaoAsVezes {
  SIM
  NAO
  AS_VEZES
}

// Enum para status da evolução diária
enum StatusEvolucaoDiaria {
  RASCUNHO
  FINALIZADA
}

// Modelo base para campos comuns a vários modelos
model BaseModel {
  id        String    @id @default(uuid())
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  active    Boolean   @default(true)
  deletedAt DateTime?

  @@ignore
}

model ProfessionGroup {
  id                 String         @id @default(uuid())
  name               String
  description        String?
  active             Boolean        @default(true)
  companyId          String?
  createdAt          DateTime       @default(now())
  updatedAt          DateTime       @updatedAt
  deletedAt          DateTime?
  defaultModules     SystemModule[] @default([BASIC])
  defaultPermissions String[]       @default([])
  professions        Profession[]
  company            Company?       @relation(fields: [companyId], references: [id])

  @@index([companyId])
  @@index([active])
  @@index([deletedAt])
  @@index([name])
}

model Profession {
  id          String           @id @default(uuid())
  name        String
  description String?
  active      Boolean          @default(true)
  groupId     String?
  companyId   String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  deletedAt   DateTime?
  company     Company?         @relation(fields: [companyId], references: [id])
  group       ProfessionGroup? @relation(fields: [groupId], references: [id])
  users       User[]

  @@index([companyId])
  @@index([groupId])
  @@index([active])
  @@index([deletedAt])
  @@index([name])
}

model User {
  id                              String                    @id @default(uuid())
  email                           String                    @unique
  password                        String
  active                          Boolean                   @default(true)
  createdAt                       DateTime                  @default(now())
  updatedAt                       DateTime                  @updatedAt
  address                         String?
  birthDate                       DateTime?
  cnpj                            String?                   @unique
  cpf                             String?                   @unique
  createdById                     String?
  fullName                        String
  login                           String                    @unique
  modules                         SystemModule[]
  permissions                     String[]
  phone                           String?
  companyId                       String?
  deletedAt                       DateTime?
  deletedById                     String?
  failedLoginAttempts             Int                       @default(0)
  lastLoginAt                     DateTime?
  lastLoginIp                     String?
  passwordChangedAt               DateTime?
  role                            UserRole                  @default(EMPLOYEE)
  professionId                    String?
  profileImageUrl                 String?
  city                            String?
  postalCode                      String?
  state                           String?
  branchId                        String?
  neighborhood                    String?
  modulePreferences               Json?
  AuditLog                        AuditLog[]
  createdClients                  Client[]                  @relation("CreatedClients")
  deletedClients                  Client[]                  @relation("DeletedClients")
  createdContacts                 Contact[]                 @relation("CreatedContacts")
  deletedContacts                 Contact[]                 @relation("DeletedContacts")
  createdConversations            Conversation[]            @relation("ConversationCreator")
  participatedConversations       ConversationParticipant[]
  createdCurriculumFolders        CurriculumFolder[]        @relation("CurriculumFolderCreator")
  createdCurriculumFolderPrograms CurriculumFolderProgram[] @relation("CurriculumFolderProgramCreator")
  createdDocuments                Document[]                @relation("DocumentCreatedBy")
  documents                       Document[]                @relation("UserDocuments")
  createdEvaluations              Evaluation[]              @relation("EvaluationCreator")
  sentMessages                    Message[]
  createdPersons                  Person[]                  @relation("CreatedPersons")
  deletedPersons                  Person[]                  @relation("DeletedPersons")
  createdPrograms                 Program[]                 @relation("ProgramCreator")
  createdAnamneses                Anamnese[]                @relation("AnamneseCreator")
  anamneseUltimaAtualizacao       Anamnese[]                @relation("AnamneseUltimaAtualizacao")

  // Relações para Evolução Diária
  evolucoesDiariasProfissional    EvolucaoDiaria[]        @relation("EvolucaoDiariaProfissional")
  createdEvolucoesDiarias         EvolucaoDiaria[]        @relation("EvolucaoDiariaCreator")
  evolucaoDiariaUltimaAtualizacao EvolucaoDiaria[]        @relation("EvolucaoDiariaUltimaAtualizacao")
  createdArquivosEvolucaoDiaria   ArquivoEvolucaoDiaria[]
  recurrencesAsCreator            Recurrence[]            @relation("CreatorRecurrence")
  recurrencesAsProvider           Recurrence[]            @relation("ProviderRecurrence")
  schedulingsAsCreator            Scheduling[]            @relation("CreatorScheduling")
  schedulingsAsProvider           Scheduling[]            @relation("ProviderScheduling")
  branch                          Branch?                 @relation(fields: [branchId], references: [id])
  company                         Company?                @relation(fields: [companyId], references: [id], onDelete: Restrict)
  createdBy                       User?                   @relation("UserCreatedBy", fields: [createdById], references: [id])
  createdUsers                    User[]                  @relation("UserCreatedBy")
  deletedBy                       User?                   @relation("UserDeletedBy", fields: [deletedById], references: [id])
  deletedUsers                    User[]                  @relation("UserDeletedBy")
  professionObj                   Profession?             @relation(fields: [professionId], references: [id])
  WorkingHours                    WorkingHours[]
  DocumentPermissions             DocumentPermission[]
  CategoryDocumentPermissions     CategoryDocumentPermission[]
  notifications                   Notification[]
  notificationPreferences         UserNotificationPreference[]

  @@index([companyId])
  @@index([branchId])
  @@index([email])
  @@index([login])
  @@index([role])
  // Índices compostos otimizados para queries de usuário
  @@index([companyId, active])
  @@index([companyId, role])
  @@index([active, lastLoginAt])
  @@index([companyId, active, role])
  @@index([fullName])
  @@index([professionId, companyId])
  
  // Constraints de validação
  @@map("User")
}

model Client {
  id              String            @id @default(uuid())
  login           String            @unique
  email           String            @unique
  password        String
  fullName        String            @default("") // Nome do titular da conta
  active          Boolean           @default(true)
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  createdById     String
  companyId       String?
  deletedAt       DateTime?
  deletedById     String?
  Company         Company?          @relation(fields: [companyId], references: [id])
  createdBy       User              @relation("CreatedClients", fields: [createdById], references: [id])
  deletedBy       User?             @relation("DeletedClients", fields: [deletedById], references: [id])
  ClientInsurance ClientInsurance[]
  Document        Document[]
  clientPersons ClientPerson[]
  Recurrence      Recurrence[]
  Scheduling      Scheduling[]
  DocumentPermissions DocumentPermission[]
  CategoryDocumentPermissions CategoryDocumentPermission[]
  
  // Relacionamentos para chat
  participatedConversations ConversationParticipant[]
  sentMessages              Message[]

  @@index([companyId])
  @@index([email])
  @@index([login])
  @@index([fullName])
}

model Person {
  id                     String                        @id @default(uuid())
  email                  String?
  phone                  String?
  address                String?
  city                   String?
  state                  String?
  birthDate              DateTime?
  notes                  String?
  active                 Boolean                       @default(true)
  createdAt              DateTime                      @default(now())
  updatedAt              DateTime                      @updatedAt
  deletedAt              DateTime?
  neighborhood           String?
  postalCode             String?
  cpf                    String?                       @unique
  createdById            String
  deletedById            String?
  fullName               String
  gender                 String?
  profileImageUrl        String?
  relationship           String?
  useClientEmail         Boolean                       @default(false)
  useClientPhone         Boolean                       @default(false)
  contacts               Contact[]                     @relation("PersonContacts")
  curriculumFolders      CurriculumFolder[]
  documents              Document[]                    @relation("PersonDocuments")
  clientPersons ClientPerson[]
  createdBy              User                          @relation("CreatedPersons", fields: [createdById], references: [id])
  deletedBy              User?                         @relation("DeletedPersons", fields: [deletedById], references: [id])
  personInsurances       PersonInsurance[]
  insuranceServiceLimits PersonInsuranceServiceLimit[]
  anamneses              Anamnese[]
  Recurrence             Recurrence[]
  schedulings            Scheduling[]                  @relation("PersonToScheduling")
  evolucoesDiarias       EvolucaoDiaria[]

  @@index([fullName])
  @@index([cpf])
  // Índices otimizados para busca de pessoas
  @@index([active, fullName])
  @@index([createdById])
  @@index([active, createdAt])
  @@index([email])
  @@index([phone])
  @@index([active, createdAt(sort: Desc), createdById])
  @@index([cpf, active])
}

model Document {
  id                  String             @id @default(uuid())
  filename            String
  path                String
  type                String
  createdAt           DateTime           @default(now())
  clientId            String?
  userId              String?
  companyId           String?
  createdById         String?
  externalUrl         String?
  mimeType            String
  ownerType           String
  size                Int
  personId            String?
  categoryDocumentId  String?
  Client              Client?            @relation(fields: [clientId], references: [id], onDelete: SetNull)
  company             Company?           @relation(fields: [companyId], references: [id], onDelete: Restrict)
  createdBy           User?              @relation("DocumentCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  person              Person?            @relation("PersonDocuments", fields: [personId], references: [id], onDelete: SetNull)
  user                User?              @relation("UserDocuments", fields: [userId], references: [id], onDelete: SetNull)
  categoryDocument    CategoryDocument?  @relation(fields: [categoryDocumentId], references: [id])
  DocumentPermission  DocumentPermission[]

  @@index([personId])
  @@index([type])
  @@index([clientId])
  @@index([companyId])
  @@index([ownerType, clientId])
  @@index([ownerType, companyId])
  @@index([ownerType, userId])
  @@index([userId])
  @@index([mimeType, companyId])
  @@index([createdAt(sort: Desc), companyId])
  @@index([ownerType, personId, type])
}

model Scheduling {
  id                 String           @id @default(uuid())
  userId             String
  clientId           String
  creatorId          String
  locationId         String
  title              String
  description        String?
  startDate          DateTime
  endDate            DateTime
  serviceTypeId      String
  insuranceId        String?
  status             SchedulingStatus @default(PENDING)
  createdAt          DateTime         @default(now())
  recurrenceId       String?
  companyId          String?
  confirmationSentAt DateTime?
  reminderSentAt     DateTime?
  branchId           String?
  branch             Branch?          @relation(fields: [branchId], references: [id])
  Client             Client           @relation(fields: [clientId], references: [id])
  company            Company?         @relation(fields: [companyId], references: [id])
  creator            User             @relation("CreatorScheduling", fields: [creatorId], references: [id])
  insurance          Insurance?       @relation(fields: [insuranceId], references: [id])
  location           Location         @relation(fields: [locationId], references: [id])
  recurrence         Recurrence?      @relation(fields: [recurrenceId], references: [id])
  serviceType        ServiceType      @relation(fields: [serviceTypeId], references: [id])
  provider           User             @relation("ProviderScheduling", fields: [userId], references: [id])
  Person             Person[]         @relation("PersonToScheduling")

  @@index([companyId])
  @@index([branchId])
  @@index([userId, startDate])
  @@index([status, startDate])
  @@index([locationId, startDate])
  @@index([serviceTypeId])
  @@index([clientId, startDate])
  // Índices compostos otimizados para queries de agendamento
  @@index([companyId, startDate])
  @@index([companyId, status, startDate])
  @@index([userId, status, startDate])
  @@index([serviceTypeId, startDate, status])
  @@index([locationId, status, startDate])
  @@index([insuranceId, startDate])
  @@index([startDate, endDate])
  @@index([companyId, userId, startDate])
}

model Recurrence {
  id                  String              @id @default(uuid())
  title               String
  description         String?
  clientId            String
  userId              String
  locationId          String
  serviceTypeId       String
  insuranceId         String?
  recurrenceType      RecurrenceType
  numberOfOccurrences Int?
  endDate             DateTime?
  active              Boolean             @default(true)
  createdAt           DateTime            @default(now())
  createdById         String
  companyId           String?
  branchId            String?
  personId            String
  branch              Branch?             @relation(fields: [branchId], references: [id])
  Client              Client              @relation(fields: [clientId], references: [id])
  company             Company?            @relation(fields: [companyId], references: [id])
  creator             User                @relation("CreatorRecurrence", fields: [createdById], references: [id])
  insurance           Insurance?          @relation(fields: [insuranceId], references: [id])
  location            Location            @relation(fields: [locationId], references: [id])
  person              Person              @relation(fields: [personId], references: [id])
  serviceType         ServiceType         @relation(fields: [serviceTypeId], references: [id])
  provider            User                @relation("ProviderRecurrence", fields: [userId], references: [id])
  patterns            RecurrencePattern[]
  schedulings         Scheduling[]

  @@index([companyId])
  @@index([branchId])
  @@index([personId])
  @@index([userId])
  @@index([active])
  @@index([clientId])
}

model RecurrencePattern {
  id               String     @id @default(uuid())
  recurrenceId     String
  dayOfWeek        Int
  endTimeMinutes   Int
  startTimeMinutes Int
  recurrence       Recurrence @relation(fields: [recurrenceId], references: [id])

  @@index([recurrenceId])
  @@index([dayOfWeek])
}

model Location {
  id          String       @id @default(uuid())
  name        String
  address     String
  phone       String?
  active      Boolean      @default(true)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  companyId   String?
  deletedAt   DateTime?
  branchId    String?
  branch      Branch?      @relation(fields: [branchId], references: [id])
  company     Company?     @relation(fields: [companyId], references: [id])
  recurrences Recurrence[]
  schedulings Scheduling[]
  availability LocationAvailability[]

  @@index([companyId])
  @@index([active])
  @@index([branchId])
}

model LocationAvailability {
  id                String   @id @default(uuid())
  locationId        String
  dayOfWeek         Int      // 0 = Domingo, 1 = Segunda, ..., 6 = Sábado
  startTimeMinutes  Int      // Horário de início em minutos desde 00:00
  endTimeMinutes    Int      // Horário de fim em minutos desde 00:00
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  location          Location @relation(fields: [locationId], references: [id], onDelete: Cascade)
  
  @@index([locationId])
  @@index([dayOfWeek])
  @@index([isActive])
  @@index([locationId, dayOfWeek, isActive])
}

model ServiceType {
  id                          String                        @id @default(uuid())
  name                        String
  value                       Decimal                       @db.Decimal(10, 2)
  companyId                   String?
  PersonInsuranceServiceLimit PersonInsuranceServiceLimit[]
  recurrences                 Recurrence[]
  schedulings                 Scheduling[]
  company                     Company?                      @relation(fields: [companyId], references: [id])

  @@unique([name, companyId])
  @@index([companyId])
}

model Insurance {
  id               String                        @id @default(uuid())
  name             String
  companyId        String?
  ClientInsurance  ClientInsurance[]
  company          Company?                      @relation(fields: [companyId], references: [id])
  personInsurances PersonInsurance[]
  serviceLimits    PersonInsuranceServiceLimit[]
  recurrences      Recurrence[]
  schedulings      Scheduling[]

  @@unique([name, companyId])
  @@index([companyId])
}

model WorkingHours {
  id                String   @id @default(uuid())
  userId            String
  dayOfWeek         Int
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  breakEndMinutes   Int?
  breakStartMinutes Int?
  endTimeMinutes    Int
  startTimeMinutes  Int
  user              User     @relation(fields: [userId], references: [id])

  @@index([userId, dayOfWeek])
  @@index([isActive])
}

model Company {
  id                String  @id @default(uuid())
  name              String // Name used within the system
  tradingName       String? // Trade name
  legalName         String? // Official legal name
  industry          String? // Industry sector
  contactEmail      String? // General contact email
  cnpj              String  @unique
  phone             String?
  privacyPolicyUrl  String? // Link to privacy policy
  termsOfServiceUrl String? // Link to terms of service
  phone2            String? // Secondary phone
  address           String?
  city              String?
  state             String?
  postalCode        String?
  website           String?
  primaryColor      String? // Primary color for branding
  secondaryColor    String? // Secondary color for branding
  description       String? // Short company description
  socialMedia       Json? // For storing social media links
  businessHours     Json? // For business hours

  // Additional fields
  defaultCurrency String @default("BRL")
  timeZone        String @default("America/Sao_Paulo")

  // Subscription info
  plan              String? // Contracted plan
  licenseValidUntil DateTime? // License expiration date
  active            Boolean   @default(true)
  deletedAt         DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Stripe integration
  stripeCustomerId String?

  // Campos de trial
  trialStart      DateTime?
  trialEnd        DateTime?
  isTrial         Boolean   @default(false)
  couponRedemptions CouponRedemption[]

  // Relations
  subscription     Subscription?
  invoices         Invoice[]
  affiliateSales   AffiliateSale[]
  branches                 Branch[]
  Client                   Client[]
  conversations            Conversation[]
  curriculumFolders        CurriculumFolder[]
  curriculumFolderPrograms CurriculumFolderProgram[]
  documents                Document[]
  emailConfigs             EmailConfig[]
  evaluations              Evaluation[]
  insurances               Insurance[]
  locations                Location[]
  professions              Profession[]
  professionGroups         ProfessionGroup[]
  programs                 Program[]
  anamneses                Anamnese[]
  evolucoesDiarias         EvolucaoDiaria[]
  arquivosEvolucaoDiaria   ArquivoEvolucaoDiaria[]
  recurrences              Recurrence[]
  schedulings              Scheduling[]
  serviceTypes             ServiceType[]
  skills                   Skill[]
  standardCriteria         StandardCriteria[]
  users                    User[]
  preferences              CompanyPreference?
  notifications            Notification[]
  userNotificationPreferences UserNotificationPreference[]
  categoryDocuments           CategoryDocument[]

  @@index([active])
}

model EmailConfig {
  id               String   @id @default(uuid())
  companyId        String
  smtpHost         String
  smtpPort         Int
  smtpSecure       Boolean  @default(false)
  smtpUser         String
  smtpPassword     String
  emailFromName    String
  emailFromAddress String
  active           Boolean  @default(true)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  company          Company  @relation(fields: [companyId], references: [id])

  @@index([companyId])
  @@index([active])
}

model Subscription {
  id                    String             @id @default(uuid())
  companyId             String             @unique
  billingCycle          BillingCycle       @default(MONTHLY)
  active                Boolean            @default(true)
  status                SubscriptionStatus @default(ACTIVE)
  startDate             DateTime           @default(now())
  endDate               DateTime?
  trialEndDate          DateTime?
  lastBillingDate       DateTime?
  nextBillingDate       DateTime?
  cancelAtPeriodEnd     Boolean            @default(false)

  // Stripe related fields
  stripeCustomerId      String?
  stripeSubscriptionId  String?
  stripeCurrentPeriodEnd DateTime?

  // Pricing and limits
  pricePerMonth         Decimal            @default(0) @db.Decimal(10, 2)
  userLimit             Int                @default(50) // Limite de usuários

  // Campos de cupom
  couponCode            String?
  couponDiscount         Float?

  // Relations
  company               Company            @relation(fields: [companyId], references: [id])
  modules               SubscriptionModule[]
  invoices              Invoice[]
  affiliateSales        AffiliateSale[]

  createdAt             DateTime           @default(now())
  updatedAt             DateTime           @updatedAt

  @@index([status])
  @@index([active])
  @@index([stripeCustomerId])
  @@index([stripeSubscriptionId])
}

model SubscriptionModule {
  id              String          @id @default(uuid())
  subscriptionId  String
  moduleType      SystemModule
  active          Boolean         @default(true)
  stripePriceId   String?
  pricePerMonth   Decimal         @default(0) @db.Decimal(10, 2)
  addedAt         DateTime        @default(now())
  
  // Relations
  subscription    Subscription    @relation(fields: [subscriptionId], references: [id])
  
  @@unique([subscriptionId, moduleType])
  @@index([moduleType])
}

model Invoice {
  id                String          @id @default(uuid())
  subscriptionId    String
  companyId         String
  amount            Decimal         @db.Decimal(10, 2)
  status            InvoiceStatus   @default(PENDING)
  dueDate           DateTime
  paidAt            DateTime?
  stripeInvoiceId   String?
  stripeInvoiceUrl  String?
  
  // Relations
  subscription      Subscription    @relation(fields: [subscriptionId], references: [id])
  company           Company         @relation(fields: [companyId], references: [id])
  
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  
  @@index([companyId])
  @@index([subscriptionId])
  @@index([status])
}

model AuditLog {
  id         String   @id @default(uuid())
  userId     String
  action     String
  entityType String
  entityId   String
  details    Json?
  ipAddress  String?
  userAgent  String?
  createdAt  DateTime @default(now())
  companyId  String?
  user       User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([entityType, entityId])
  @@index([companyId])
  @@index([createdAt])
  // Índices compostos otimizados para queries frequentes
  @@index([companyId, createdAt])
  @@index([action, createdAt])
  @@index([entityType, createdAt])
  @@index([companyId, action, createdAt])
  @@index([userId, createdAt])
}

model PasswordReset {
  id        String    @id @default(uuid())
  userId    String
  token     String    @unique
  expiresAt DateTime
  usedAt    DateTime?
  createdAt DateTime  @default(now())

  @@index([token])
  @@index([userId])
  @@index([expiresAt])
}

model Branch {
  id                  String         @id @default(uuid())
  name                String
  code                String?
  description         String?
  address             String
  city                String?
  state               String?
  postalCode          String?
  phone               String?
  email               String?
  active              Boolean        @default(true)
  isHeadquarters      Boolean        @default(false)
  deletedAt           DateTime?
  createdAt           DateTime       @default(now())
  updatedAt           DateTime       @updatedAt
  companyId           String
  neighborhood        String?
  defaultWorkingHours Json?
  company             Company        @relation(fields: [companyId], references: [id])
  conversations       Conversation[]
  locations           Location[]
  recurrences         Recurrence[]
  schedulings         Scheduling[]
  users               User[]

  @@unique([companyId, code])
  @@index([companyId])
  @@index([active])
}

model Contact {
  id           String   @id @default(uuid())
  name         String
  relationship String?
  email        String?
  phone        String?
  notes        String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  personId     String
  createdById  String
  deletedById  String?
  createdBy    User     @relation("CreatedContacts", fields: [createdById], references: [id])
  deletedBy    User?    @relation("DeletedContacts", fields: [deletedById], references: [id])
  person       Person   @relation("PersonContacts", fields: [personId], references: [id])

  @@index([personId])
}

model ClientInsurance {
  clientId     String
  insuranceId  String
  notes        String?
  policyNumber String?
  validUntil   DateTime?
  Client       Client    @relation(fields: [clientId], references: [id])
  Insurance    Insurance @relation(fields: [insuranceId], references: [id])

  @@id([clientId, insuranceId])
  @@index([insuranceId])
}

model PersonInsurance {
  personId     String
  insuranceId  String
  policyNumber String?
  validUntil   DateTime?
  notes        String?
  insurance    Insurance @relation(fields: [insuranceId], references: [id])
  person       Person    @relation(fields: [personId], references: [id])

  @@id([personId, insuranceId])
  @@index([insuranceId])
}

model PersonInsuranceServiceLimit {
  id            String      @id
  personId      String
  insuranceId   String
  serviceTypeId String
  monthlyLimit  Int
  yearlyLimit   Int         @default(0)
  notes         String?
  Insurance     Insurance   @relation(fields: [insuranceId], references: [id])
  Person        Person      @relation(fields: [personId], references: [id])
  ServiceType   ServiceType @relation(fields: [serviceTypeId], references: [id])

  @@unique([personId, insuranceId, serviceTypeId])
  @@index([personId, insuranceId])
  @@index([serviceTypeId])
  
  // Constraints de validação - dados já validados
  @@map("PersonInsuranceServiceLimit")
}

model Conversation {
  id            String                    @id @default(uuid())
  type          ConversationType
  title         String?
  createdAt     DateTime                  @default(now())
  updatedAt     DateTime                  @updatedAt
  companyId     String?
  branchId      String?
  lastMessageAt DateTime?
  isActive      Boolean                   @default(true)
  createdById   String?
  branch        Branch?                   @relation(fields: [branchId], references: [id])
  company       Company?                  @relation(fields: [companyId], references: [id])
  createdBy     User?                     @relation("ConversationCreator", fields: [createdById], references: [id])
  participants  ConversationParticipant[]
  messages      Message[]

  @@index([companyId])
  @@index([branchId])
  @@index([isActive])
  @@index([lastMessageAt])
}

model ConversationParticipant {
  id                String          @id @default(uuid())
  conversationId    String
  userId            String?
  clientId          String?
  joinedAt          DateTime        @default(now())
  leftAt            DateTime?
  isAdmin           Boolean         @default(false)
  lastReadMessageId String?
  conversation      Conversation    @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  lastReadMessage   Message?        @relation("LastReadMessage", fields: [lastReadMessageId], references: [id])
  user              User?           @relation(fields: [userId], references: [id])
  client            Client?         @relation(fields: [clientId], references: [id])
  messageStatuses   MessageStatus[]

  @@unique([conversationId, userId])
  @@unique([conversationId, clientId])
  @@index([userId])
  @@index([clientId])
  @@index([conversationId])
}

model Message {
  id                  String                    @id @default(uuid())
  conversationId      String
  senderId            String?
  senderClientId      String?
  content             String
  contentType         MessageContentType        @default(TEXT)
  createdAt           DateTime                  @default(now())
  updatedAt           DateTime                  @updatedAt
  isDeleted           Boolean                   @default(false)
  referencedMessageId String?
  metadata            Json?
  readByParticipants  ConversationParticipant[] @relation("LastReadMessage")
  conversation        Conversation              @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  referencedMessage   Message?                  @relation("MessageReference", fields: [referencedMessageId], references: [id])
  referencingMessages Message[]                 @relation("MessageReference")
  sender              User?                     @relation(fields: [senderId], references: [id])
  senderClient        Client?                   @relation(fields: [senderClientId], references: [id])
  statuses            MessageStatus[]

  @@index([conversationId])
  @@index([senderId])
  @@index([senderClientId])
  @@index([createdAt])
  @@index([conversationId, createdAt])
  @@index([conversationId, isDeleted, createdAt(sort: Desc)])
  @@index([senderId, createdAt(sort: Desc)])
  @@index([senderClientId, createdAt(sort: Desc)])
}

model MessageStatus {
  id            String                  @id @default(uuid())
  messageId     String
  participantId String
  status        MessageDeliveryStatus
  timestamp     DateTime                @default(now())
  message       Message                 @relation(fields: [messageId], references: [id], onDelete: Cascade)
  participant   ConversationParticipant @relation(fields: [participantId], references: [id], onDelete: Cascade)

  @@unique([messageId, participantId])
  @@index([messageId])
  @@index([participantId])
}

model Evaluation {
  id           String            @id @default(uuid())
  type         EvaluationType
  name         String
  observations String?
  active       Boolean           @default(true)
  createdAt    DateTime          @default(now())
  updatedAt    DateTime          @updatedAt
  deletedAt    DateTime?
  companyId    String
  createdById  String
  company      Company           @relation(fields: [companyId], references: [id])
  createdBy    User              @relation("EvaluationCreator", fields: [createdById], references: [id])
  skills       EvaluationSkill[]
  levels       Level[]
  scores       Score[]
  tasks        Task[]

  @@index([companyId])
  @@index([active])
  @@index([createdById])
  @@index([type])
}

model Level {
  id           String     @id @default(uuid())
  order        Int
  description  String
  ageRange     String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  evaluationId String
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id], onDelete: Cascade)
  tasks        Task[]

  @@index([evaluationId])
  @@index([order])
}

model Skill {
  id          String            @id @default(uuid())
  code        String?
  order       Int
  description String
  active      Boolean           @default(true)
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @default(now())
  deletedAt   DateTime?
  companyId   String
  evaluations EvaluationSkill[]
  company     Company           @relation(fields: [companyId], references: [id])
  tasks       Task[]

  @@index([companyId])
  @@index([active])
  @@index([order])
}

model EvaluationSkill {
  evaluationId String
  skillId      String
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id], onDelete: Cascade)
  skill        Skill      @relation(fields: [skillId], references: [id], onDelete: Cascade)

  @@id([evaluationId, skillId])
  @@index([evaluationId])
  @@index([skillId])
}

model Score {
  id           String     @id @default(uuid())
  type         ScoreType
  value        String?
  description  String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  evaluationId String
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id], onDelete: Cascade)

  @@index([evaluationId])
  @@index([type])
}

model Task {
  id           String     @id @default(uuid())
  order        Int
  name         String
  milestone    String?
  item         String?
  question     String?
  example      String?
  criteria     String?
  objective    String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  evaluationId String
  skillId      String?
  levelId      String?
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id], onDelete: Cascade)
  level        Level?     @relation(fields: [levelId], references: [id])
  skill        Skill?     @relation(fields: [skillId], references: [id])

  @@index([evaluationId])
  @@index([skillId])
  @@index([levelId])
  @@index([order])
}

model StandardCriteria {
  id           String         @id @default(uuid())
  teachingType TeachingType
  acronym      String
  degree       CriteriaDegree
  active       Boolean        @default(true)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @default(now())
  deletedAt    DateTime?
  companyId    String
  company      Company        @relation(fields: [companyId], references: [id])

  @@index([companyId])
  @@index([active])
  @@index([teachingType])
  @@index([degree])
}

model Program {
  id                  String          @id @default(uuid())
  type                ProgramType
  name                String
  protocol            String?
  skill               String?
  milestone           String?
  teachingType        String?
  targetsPerSession   Int?            @default(1)
  attemptsPerTarget   Int?            @default(1)
  teachingProcedure   String?         @default("")
  instruction         String?         @default("")
  objective           String?         @default("")
  correctionProcedure String?         @default("")
  learningCriteria    String?         @default("")
  materials           String?         @default("")
  notes               String?         @default("")
  active              Boolean         @default(true)
  createdAt           DateTime        @default(now())
  updatedAt           DateTime         @updatedAt
  deletedAt           DateTime?
  companyId           String
  createdById         String
  promptStep          String?         @default("")
  company             Company         @relation(fields: [companyId], references: [id])
  createdBy           User            @relation("ProgramCreator", fields: [createdById], references: [id])
  targets             ProgramTarget[]

  @@index([companyId])
  @@index([active])
  @@index([type])
  @@index([createdById])
}

model ProgramTarget {
  id               String    @id @default(uuid())
  target           String
  order            Int
  group            String?
  situation        String?   @default("ACTIVE")
  startDate        DateTime?
  acquisitionDate  DateTime?
  maintenanceCount Int?      @default(0)
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  programId        String
  program          Program   @relation(fields: [programId], references: [id], onDelete: Cascade)

  @@index([programId])
  @@index([order])
}

model CurriculumFolder {
  id               String                    @id @default(uuid())
  name             String
  personId         String
  shareWithParents Boolean                   @default(false)
  shareWithSchools Boolean                   @default(false)
  active           Boolean                   @default(true)
  createdAt        DateTime                  @default(now())
  updatedAt        DateTime                  @updatedAt
  deletedAt        DateTime?
  companyId        String
  createdById      String
  company          Company                   @relation(fields: [companyId], references: [id])
  createdBy        User                      @relation("CurriculumFolderCreator", fields: [createdById], references: [id])
  person           Person                    @relation(fields: [personId], references: [id])
  programs         CurriculumFolderProgram[]

  @@index([companyId])
  @@index([active])
  @@index([personId])
  @@index([createdById])
}

model CurriculumFolderProgram {
  id                  String           @id @default(uuid())
  name                String
  type                String
  protocol            String?
  skill               String?
  milestone           String?
  teachingType        String?
  targetsPerSession   Int?             @default(1)
  attemptsPerTarget   Int?             @default(1)
  teachingProcedure   String?          @default("")
  instruction         String?          @default("")
  objective           String?          @default("")
  promptStep          String?          @default("")
  correctionProcedure String?          @default("")
  learningCriteria    String?          @default("")
  materials           String?          @default("")
  notes               String?          @default("")
  status              ProgramStatus    @default(unallocated)
  originalProgramId   String?
  active              Boolean          @default(true)
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  deletedAt           DateTime?
  curriculumFolderId  String
  companyId           String
  createdById         String
  company             Company          @relation(fields: [companyId], references: [id])
  createdBy           User             @relation("CurriculumFolderProgramCreator", fields: [createdById], references: [id])
  curriculumFolder    CurriculumFolder @relation(fields: [curriculumFolderId], references: [id])

  @@index([curriculumFolderId])
  @@index([companyId])
  @@index([active])
  @@index([status])
  @@index([createdById])
  @@index([originalProgramId])
}

model Anamnese {
  id                                  String         @id @default(uuid())
  date                                DateTime
  personId                            String
  diagnostico                         String?        @db.Text
  cuidador                            String?        @db.Text
  profissaoCuidador                   String?
  telefone                            String?
  historicoPersonal                   String?        @db.Text
  patologiaAssociada                  String?        @db.Text
  convulsoes                          String?        @db.Text
  sentou                              String?
  engatinhou                          String?
  andou                               String?
  estereotipiasMotoras                String?        @db.Text
  alimentacaoSolidos                  Boolean        @default(false)
  alimentacaoLiquidos                 Boolean        @default(false)
  alimentacaoPastosos                 Boolean        @default(false)
  alergiasIntolerancias               String?        @db.Text
  // Campos adicionais para informações médicas
  historicoMedico                     String?        @db.Text
  medicacoes                          String?        @db.Text
  alergias                            String?        @db.Text
  historicoFamiliar                   String?        @db.Text
  // AVD
  avdAlimentacao                      String?        @db.Text
  avdBanho                            String?        @db.Text
  avdVestuario                        String?        @db.Text
  avdCuidadosPessoais                 String?        @db.Text
  avdSono                             String?        @db.Text
  avdEsfincter                        String?        @db.Text
  // Desenvolvimento da Linguagem - Não Verbal
  gestosElementares                   SimNaoAsVezes? @default(NAO)
  naoSimbolicosConvencionais          SimNaoAsVezes? @default(NAO)
  simbolicosRepresentacao             SimNaoAsVezes? @default(NAO)
  // Desenvolvimento da Linguagem - Verbal
  verbal                              SimNaoAsVezes? @default(NAO)
  balbucio                            SimNaoAsVezes? @default(NAO)
  palavrasIsoladas                    SimNaoAsVezes? @default(NAO)
  quaisPalavrasIsoladas               String?        @db.Text
  enunciadoDuasPalavras               SimNaoAsVezes? @default(NAO)
  frases                              SimNaoAsVezes? @default(NAO)
  estereotipiasVocais                 SimNaoAsVezes? @default(NAO)
  quaisEstereotipiasVocais            String?        @db.Text
  // Interação Social
  faltaExpressaoFacialAdequada        SimNaoAsVezes? @default(NAO)
  apresentaAtencaoDiminuida           SimNaoAsVezes? @default(NAO)
  apresentaPreferenciaIsolamento      SimNaoAsVezes? @default(NAO)
  ageComoSeFosseSurdo                 SimNaoAsVezes? @default(NAO)
  olhaParaAlguemQueLheFala            SimNaoAsVezes? @default(NAO)
  olhaQuandoChamadoPeloNome           SimNaoAsVezes? @default(NAO)
  fazPedidoItensInteresse             SimNaoAsVezes? @default(NAO)
  realizaImitacao                     SimNaoAsVezes? @default(NAO)
  brincaAdequadamenteBrinquedo        SimNaoAsVezes? @default(NAO)
  preferenciasObjetosEspecificos      String?        @db.Text
  apresentaAversoes                   String?        @db.Text
  autoEstimulacao                     Boolean?       @default(false)
  apresentaAutoAgressaoHeteroAgressao Boolean?       @default(false)
  apresentaBirrasIrritabilidade       Boolean?       @default(false)
  apresentaManiasRituais              Boolean?       @default(false)
  // Escola
  estuda                              Boolean?       @default(false)
  nomeEscola                          String?
  serie                               String?
  escolaRegular                       Boolean?       @default(false)
  professorApoio                      Boolean?       @default(false)
  // Outros
  outroCasoFamilia                    Boolean?       @default(false)
  outrosCasosDetalhamento             String?        @db.Text
  terapias                            String?        @db.Text
  expectativasFamilia                 String?        @db.Text
  observacoesGerais                   String?        @db.Text
  // Campos para controle de versão
  versao                              Int            @default(1)
  ultimaAtualizacaoPorId              String?
  // Campos padrão
  active                              Boolean        @default(true)
  createdAt                           DateTime       @default(now())
  updatedAt                           DateTime       @updatedAt
  deletedAt                           DateTime?
  companyId                           String
  createdById                         String
  company                             Company        @relation(fields: [companyId], references: [id])
  createdBy                           User           @relation("AnamneseCreator", fields: [createdById], references: [id])
  person                              Person         @relation(fields: [personId], references: [id])
  ultimaAtualizacaoPor                User?          @relation("AnamneseUltimaAtualizacao", fields: [ultimaAtualizacaoPorId], references: [id])

  @@index([personId])
  @@index([companyId])
  @@index([active])
  @@index([createdById])
  @@index([ultimaAtualizacaoPorId])
  @@index([versao])
}

// Modelo para Evolução Diária
model EvolucaoDiaria {
  id                   String               @id @default(uuid())
  personId             String // Aprendiz/Paciente
  profissionalId       String // Profissional responsável
  dataInicio           DateTime // Data e hora de início do atendimento
  dataFim              DateTime // Data e hora de fim do atendimento
  faltou               Boolean              @default(false) // Se o aprendiz faltou
  permitirVisualizacao Boolean              @default(false) // Permitir visualização pelos responsáveis
  atendimento          String?              @db.Text // Resumo do atendimento
  observacoes          String?              @db.Text // Observações adicionais
  status               StatusEvolucaoDiaria @default(RASCUNHO) // Status da evolução

  // Campos para controle de versão
  versao                 Int     @default(1)
  ultimaAtualizacaoPorId String?

  // Campos padrão
  active      Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
  companyId   String
  createdById String

  // Relacionamentos
  person               Person                  @relation(fields: [personId], references: [id])
  profissional         User                    @relation("EvolucaoDiariaProfissional", fields: [profissionalId], references: [id])
  company              Company                 @relation(fields: [companyId], references: [id])
  createdBy            User                    @relation("EvolucaoDiariaCreator", fields: [createdById], references: [id])
  ultimaAtualizacaoPor User?                   @relation("EvolucaoDiariaUltimaAtualizacao", fields: [ultimaAtualizacaoPorId], references: [id])
  arquivos             ArquivoEvolucaoDiaria[]

  @@index([personId])
  @@index([profissionalId])
  @@index([companyId])
  @@index([active])
  @@index([createdById])
  @@index([ultimaAtualizacaoPorId])
  @@index([status])
  @@index([dataInicio])
  @@index([faltou])
}

// Modelo para Arquivos da Evolução Diária
model ArquivoEvolucaoDiaria {
  id               String @id @default(uuid())
  evolucaoDiariaId String
  nome             String
  tipo             String // Tipo/extensão do arquivo
  tamanho          Int // Tamanho em bytes
  url              String // URL para acessar o arquivo

  // Campos padrão
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  deletedAt   DateTime?
  companyId   String
  createdById String

  // Relacionamentos
  evolucaoDiaria EvolucaoDiaria @relation(fields: [evolucaoDiariaId], references: [id])
  company        Company        @relation(fields: [companyId], references: [id])
  createdBy      User           @relation(fields: [createdById], references: [id])

  @@index([evolucaoDiariaId])
  @@index([companyId])
  @@index([createdById])
}

model ClientPerson {
  id           String   @id @default(uuid())
  clientId     String
  personId     String
  relationship String?  // "pai", "mae", "responsavel"
  isPrimary    Boolean  @default(false) // responsável principal
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  
  client       Client   @relation(fields: [clientId], references: [id])
  person       Person   @relation(fields: [personId], references: [id])
  
  @@unique([clientId, personId])
  @@index([clientId])
  @@index([personId])
  @@index([personId, isPrimary]) // Para buscar responsável principal rapidamente
}

model EmailVerification {
  id         String   @id @default(uuid())
  email      String   @unique
  code       String
  expiresAt  DateTime
  verified   Boolean  @default(false)
  createdAt  DateTime @default(now())
}

model Coupon {
  id          String   @id @default(uuid())
  code        String   @unique
  description String?
  type        String   // e.g. PERCENT, VALUE, TRIAL_EXTENSION
  value       Float?
  maxRedemptions Int?  // Quantidade máxima de usos
  expiresAt   DateTime?
  active      Boolean  @default(true)
  affiliateId String?  // Relacionamento com afiliado
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relacionamentos
  affiliate   Affiliate? @relation(fields: [affiliateId], references: [id])
  redemptions CouponRedemption[]
  
  @@index([affiliateId])
}

model CouponRedemption {
  id        String   @id @default(uuid())
  couponId  String
  userId    String?
  companyId String?
  redeemedAt DateTime @default(now())
  coupon    Coupon   @relation(fields: [couponId], references: [id])
  company   Company? @relation(fields: [companyId], references: [id])
}

model CompanyPreference {
  id                String   @id @default(uuid())
  companyId         String   @unique
  theme             String   @default("light")
  language          String   @default("pt-BR")
  timezone          String   @default("America/Sao_Paulo")
  dateFormat        String   @default("DD/MM/YYYY")
  timeFormat        String   @default("24h")
  currency          String   @default("BRL")
  notifications     Json?
  preferences       Json?    // Campo para configurações de campos obrigatórios
  locationSettings  Json?    // Configurações de disponibilidade de localizações
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  
  company           Company  @relation(fields: [companyId], references: [id])
}

// Sistema de Afiliados
model Affiliate {
  id            String   @id @default(uuid())
  name          String
  email         String   @unique
  phone         String?
  code          String   @unique // Código único do afiliado
  commission    Float    @default(10) // Percentual de comissão (padrão 10%)
  active        Boolean  @default(true)
  totalSales    Int      @default(0) // Total de vendas realizadas
  totalEarnings Float    @default(0) // Total de ganhos
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relacionamentos
  sales         AffiliateSale[] // Vendas realizadas pelo afiliado
  coupons       Coupon[]        // Cupons criados pelo afiliado
  
  @@index([code])
  @@index([active])
  @@index([createdAt])
}

// Modelo para comissões únicas por venda (não mensais)
model AffiliateSale {
  id            String   @id @default(uuid())
  affiliateId   String
  companyId     String
  subscriptionId String?
  amount        Float    // Valor total da venda
  commission    Float    // Valor da comissão (calculado automaticamente)
  commissionPercentage Float // Percentual de comissão usado
  monthsCount   Int      // Quantidade de meses da assinatura
  status        AffiliateSaleStatus @default(PENDING)
  paidAt        DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  
  // Relacionamentos
  affiliate     Affiliate @relation(fields: [affiliateId], references: [id])
  company       Company   @relation(fields: [companyId], references: [id])
  subscription  Subscription? @relation(fields: [subscriptionId], references: [id])
  
  @@index([affiliateId])
  @@index([companyId])
  @@index([subscriptionId])
  @@index([status])
  @@index([createdAt])
}

enum AffiliateSaleStatus {
  PENDING    // Pendente de pagamento
  PAID       // Pago
  CANCELLED  // Cancelado
}

enum NotificationType {
  NEW_REGISTRATION
  APPOINTMENT_COMING
  NEW_ACCESS
  NEW_BACKUP
  NEW_EXPORT
  SYSTEM_ALERT
}

enum NotificationPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

// Sistema de Notificações
model Notification {
  id                String                @id @default(uuid())
  title             String
  message           String
  type              NotificationType
  priority          NotificationPriority  @default(MEDIUM)
  read              Boolean               @default(false)
  userId            String
  companyId         String
  data              Json?                 // Dados extras da notificação
  expiresAt         DateTime?
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt
  
  // Relacionamentos
  user              User                  @relation(fields: [userId], references: [id])
  company           Company               @relation(fields: [companyId], references: [id])
  
  @@index([userId])
  @@index([companyId])
  @@index([type])
  @@index([read])
  @@index([createdAt])
}

// Preferências de Notificação por Usuário
model UserNotificationPreference {
  id                String                @id @default(uuid())
  userId            String
  companyId         String
  type              NotificationType
  enabled           Boolean               @default(true)
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt
  
  // Relacionamentos
  user              User                  @relation(fields: [userId], references: [id])
  company           Company               @relation(fields: [companyId], references: [id])
  
  @@unique([userId, companyId, type])
  @@index([userId])
  @@index([companyId])
}

model CategoryDocument {
  id          String     @id @default(uuid())
  name        String
  description String?
  companyId   String?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  company     Company?   @relation(fields: [companyId], references: [id])
  documents   Document[]
  CategoryDocumentPermission CategoryDocumentPermission[]
  
  @@index([companyId])
  @@index([createdAt])
}

model DocumentPermission {
  id          String    @id @default(uuid())
  documentId  String
  userId      String?
  clientId    String?
  canView     Boolean   @default(false)
  canEdit     Boolean   @default(false)
  document    Document  @relation(fields: [documentId], references: [id])
  user        User?     @relation(fields: [userId], references: [id])
  client      Client?   @relation(fields: [clientId], references: [id])

  @@index([documentId])
  @@index([userId])
  @@index([clientId])
}

model CategoryDocumentPermission {
  id                String           @id @default(uuid())
  categoryDocumentId String
  userId            String?
  clientId          String?
  canView           Boolean          @default(false)
  canEdit           Boolean          @default(false)
  categoryDocument  CategoryDocument @relation(fields: [categoryDocumentId], references: [id])
  user              User?            @relation(fields: [userId], references: [id])
  client            Client?          @relation(fields: [clientId], references: [id])

  @@index([categoryDocumentId])
  @@index([userId])
  @@index([clientId])
}
// Modelo para sessões de signup
model SignupSession {
  id          String   @id @default(uuid())
  sessionData Json     // Dados do formulário de signup
  expiresAt   DateTime
  used        Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  @@index([expiresAt])
  @@index([used])
}

