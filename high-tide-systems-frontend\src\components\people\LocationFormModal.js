"use client";

import React, { useState, useEffect } from "react";
import { MapPin, Phone, Building, Clock, Loader2, AlertCircle } from "lucide-react";
import { ModuleInput, ModuleSelect, ModuleFormGroup } from "@/components/ui";
import { locationService } from "@/app/modules/scheduler/services/locationService";
import { branchService } from "@/app/modules/admin/services/branchService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import MaskedInput from "@/components/common/MaskedInput";
import ShareButton from "@/components/common/ShareButton";

const LocationFormModal = ({ isOpen, onClose, location = null, onSuccess }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    name: "",
    address: "",
    phone: "",
    branchId: "",
    companyId: "",
    useAvailability: false,
    availability: {
      monday: [],
      tuesday: [],
      wednesday: [],
      thursday: [],
      friday: [],
      saturday: [],
      sunday: []
    }
  });

  const [branchOptions, setBranchOptions] = useState([]);
  const [companyOptions, setCompanyOptions] = useState([]);
  const [loading, setLoading] = useState({ branches: false, companies: false });
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isSystemAdmin = user?.role === "SYSTEM_ADMIN";

  // Carregar empresas e unidades ao abrir o modal
  useEffect(() => {
    if (isOpen) {
      // Definir a companyId para usuários não-admin
      if (!isSystemAdmin && user?.companyId) {
        setFormData((prev) => ({ ...prev, companyId: user.companyId }));
        loadBranches(user.companyId);
      } else if (isSystemAdmin) {
        // Apenas carrega empresas para administradores do sistema
        loadCompanies();
      }

      if (location) {
        setFormData({
          name: location.name || "",
          address: location.address || "",
          phone: location.phone || "",
          branchId: location.branchId || "",
          // Se não for admin, sempre usa a empresa do usuário
          companyId: !isSystemAdmin
            ? user?.companyId
            : location.companyId || "",
          useAvailability: false,
          availability: {
            monday: [], tuesday: [], wednesday: [], thursday: [],
            friday: [], saturday: [], sunday: []
          }
        });

        // Carregar disponibilidade da localização
        if (location.id) {
          loadLocationAvailability(location.id);
        }

        // Se tiver uma empresa definida, carrega as unidades correspondentes
        if (location.companyId) {
          loadBranches(location.companyId);
        }
      } else {
        // Reset do formulário para nova localização
        resetForm();
      }
    }
  }, [isOpen, location, user]);

  const resetForm = () => {
    setFormData({
      name: "",
      address: "",
      phone: "",
      branchId: "",
      // Se não for admin, sempre usa a empresa do usuário
      companyId: !isSystemAdmin ? user?.companyId : "",
      useAvailability: false,
      availability: {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: []
      }
    });
    setError(null);
  };

  const loadCompanies = async () => {
    // Apenas administradores do sistema podem carregar empresas
    if (!isSystemAdmin) return;

    setLoading((prev) => ({ ...prev, companies: true }));
    try {
      const response = await companyService.getCompanies({
        active: true,
        limit: 100,
      });

      setCompanyOptions(response.companies || []);
    } catch (err) {
      console.error("Erro ao carregar empresas:", err);
    } finally {
      setLoading((prev) => ({ ...prev, companies: false }));
    }
  };

  const loadBranches = async (companyId) => {
    if (!companyId) return;

    setLoading((prev) => ({ ...prev, branches: true }));
    try {
      const response = await branchService.getBranches({
        companyId,
        active: true,
        limit: 100,
      });

      setBranchOptions(response.branches || []);
    } catch (err) {
      console.error("Erro ao carregar unidades:", err);
    } finally {
      setLoading((prev) => ({ ...prev, branches: false }));
    }
  };

  const loadLocationAvailability = async (locationId) => {
    try {
      const response = await locationService.getLocationAvailability(locationId);
      const availabilityData = response.data || [];
      
      const availability = {
        monday: [], tuesday: [], wednesday: [], thursday: [],
        friday: [], saturday: [], sunday: []
      };
      
      const dayMap = {
        0: 'sunday', 1: 'monday', 2: 'tuesday', 3: 'wednesday',
        4: 'thursday', 5: 'friday', 6: 'saturday'
      };
      
      availabilityData.forEach(item => {
        const dayKey = dayMap[item.dayOfWeek];
        if (dayKey && item.isActive) {
          const startHour = Math.floor(item.startTimeMinutes / 60);
          const endHour = Math.floor(item.endTimeMinutes / 60);
          
          for (let hour = startHour; hour < endHour; hour++) {
            const timeStr = `${hour.toString().padStart(2, '0')}:00`;
            if (!availability[dayKey].includes(timeStr)) {
              availability[dayKey].push(timeStr);
            }
          }
        }
      });
      
      setFormData(prev => ({
        ...prev,
        useAvailability: availabilityData.length > 0,
        availability
      }));
    } catch (error) {
      console.error('Erro ao carregar disponibilidade:', error);
    }
  };

  // Gerar horários de 1 em 1 hora
  const generateTimeSlots = () => {
    const slots = [];
    for (let hour = 6; hour < 22; hour++) {
      const time = `${hour.toString().padStart(2, '0')}:00`;
      slots.push(time);
    }
    return slots;
  };

  // Estados para seleção por arrastar
  const [isDragging, setIsDragging] = useState(false);
  const [dragMode, setDragMode] = useState(null); // 'select' ou 'deselect'

  const timeSlots = generateTimeSlots();
  const daysOfWeek = [
    { key: 'monday', label: 'Segunda' },
    { key: 'tuesday', label: 'Terça' },
    { key: 'wednesday', label: 'Quarta' },
    { key: 'thursday', label: 'Quinta' },
    { key: 'friday', label: 'Sexta' },
    { key: 'saturday', label: 'Sábado' },
    { key: 'sunday', label: 'Domingo' }
  ];

  const handleAvailabilityToggle = (day, time, isMouseDown = false) => {
    setFormData(prev => {
      const dayAvailability = prev.availability[day] || [];
      const isSelected = dayAvailability.includes(time);
      
      // Se é o início do drag, definir o modo
      if (isMouseDown) {
        setIsDragging(true);
        setDragMode(isSelected ? 'deselect' : 'select');
      }
      
      // Se está arrastando, usar o modo definido
      let shouldSelect;
      if (isDragging && dragMode) {
        shouldSelect = dragMode === 'select';
      } else {
        shouldSelect = !isSelected;
      }
      
      return {
        ...prev,
        availability: {
          ...prev.availability,
          [day]: shouldSelect 
            ? [...dayAvailability.filter(t => t !== time), time].sort()
            : dayAvailability.filter(t => t !== time)
        }
      };
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setDragMode(null);
  };

  const handleMouseEnter = (day, time) => {
    if (isDragging) {
      handleAvailabilityToggle(day, time);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (type === 'checkbox' && name === 'useAvailability') {
      setFormData(prev => ({ ...prev, useAvailability: checked }));
      return;
    }

    // Se um branch for selecionado, encontrar e configurar o company correspondente
    if (name === "branchId" && value) {
      const selectedBranch = branchOptions.find(
        (branch) => branch.id === value
      );
      if (selectedBranch && isSystemAdmin) {
        // Apenas admin pode mudar a empresa
        setFormData((prev) => ({
          ...prev,
          [name]: value,
          companyId: selectedBranch.companyId,
        }));
        return;
      }
    }

    // Se uma empresa for selecionada, carregar as unidades dessa empresa
    if (name === "companyId" && value && isSystemAdmin) {
      // Apenas admin pode mudar a empresa
      loadBranches(value);
      // Resetar o branchId quando a empresa muda
      setFormData((prev) => ({
        ...prev,
        [name]: value,
        branchId: "",
      }));
      return;
    }

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateForm = () => {
    // Validação básica
    if (!formData.name) return "Nome da localização é obrigatório";
    if (!formData.address) return "Endereço é obrigatório";
    if (!formData.companyId) return "Empresa é obrigatória";

    return null; // Sem erros
  };

  const saveLocationAvailability = async (locationId) => {
    try {
      const availability = [];
      
      const dayMap = {
        'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
        'thursday': 4, 'friday': 5, 'saturday': 6
      };
      
      Object.entries(formData.availability).forEach(([dayKey, times]) => {
        if (times.length > 0) {
          const sortedTimes = times.sort();
          let startTime = null;
          let endTime = null;
          
          for (let i = 0; i < sortedTimes.length; i++) {
            const currentHour = parseInt(sortedTimes[i].split(':')[0]);
            const nextHour = i < sortedTimes.length - 1 ? parseInt(sortedTimes[i + 1].split(':')[0]) : null;
            
            if (startTime === null) {
              startTime = currentHour;
            }
            
            if (nextHour === null || nextHour !== currentHour + 1) {
              endTime = currentHour + 1;
              
              availability.push({
                dayOfWeek: dayMap[dayKey],
                startTimeMinutes: startTime * 60,
                endTimeMinutes: endTime * 60,
                isActive: true
              });
              
              startTime = null;
              endTime = null;
            }
          }
        }
      });
      
      await locationService.updateLocationAvailability(locationId, availability);
    } catch (error) {
      console.error('Erro ao salvar disponibilidade:', error);
      throw error;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validar formulário
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Preparar dados
      const payload = {
        name: formData.name,
        address: formData.address,
        phone: formData.phone ? formData.phone.replace(/\D/g, "") : undefined,
        branchId: formData.branchId || undefined,
        // Garantir que sempre tenha uma empresa
        companyId: formData.companyId || user?.companyId,
      };

      let savedLocation;
      if (location) {
        // Atualizar localização existente
        savedLocation = await locationService.updateLocation(location.id, payload);
      } else {
        // Criar nova localização
        savedLocation = await locationService.createLocation(payload);
      }

      // Salvar disponibilidade se estiver ativada
      if (formData.useAvailability) {
        await saveLocationAvailability(savedLocation.id || location.id);
      } else {
        // Limpar disponibilidade se foi desativada
        await locationService.updateLocationAvailability(savedLocation.id || location.id, []);
      }

      onSuccess();
      onClose();
    } catch (err) {
      console.error("Erro ao salvar localização:", err);
      setError(
        err.response?.data?.message ||
          err.message ||
          "Ocorreu um erro ao salvar a localização."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // CSS classes
  const inputClasses =
    "block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-neutral-900 dark:text-gray-100";
  const labelClasses = "block text-sm font-medium text-neutral-700 dark:text-gray-300 mb-1";
  const errorClasses = "mt-1 text-xs text-red-600 dark:text-red-400";

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo escuro */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="fixed left-[50%] top-[50%] z-[12050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-purple-300 dark:border-purple-600 bg-background shadow-lg duration-200 rounded-xl max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="pb-4 border-b-2 border-purple-400 dark:border-purple-500 flex-shrink-0 px-6 pt-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-600 to-violet-400 rounded-lg text-white">
              <MapPin className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-purple-800 dark:text-white border-l-4 border-purple-400 dark:border-purple-500 pl-3">
                {location ? 'Editar Localização' : 'Nova Localização'}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                {location ? 'Modifique as informações da localização' : 'Preencha as informações para criar uma nova localização'}
              </p>
            </div>
            {location && (
              <div className="ml-auto">
                <ShareButton
                  itemType="location"
                  itemId={location.id}
                  itemTitle={location.name}
                  size="sm"
                  variant="ghost"
                />
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(90vh-120px)]">
          {/* Form Content */}
          <div className="flex-1 overflow-y-auto p-6">
            <form id="location-form" onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2">
                  <AlertCircle size={16} />
                  <span>{error}</span>
                </div>
              )}

              {/* Informações Básicas */}
              <div className="mb-8">
                <div className="border-b-2 border-purple-400 dark:border-purple-500 pb-3 mb-4">
                  <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-purple-500 pl-3">
                    Informações da Localização
                  </h4>
                  <p className="text-xs text-neutral-600 dark:text-gray-300 pl-3">
                    Dados básicos da localização:
                  </p>
                </div>
              </div>

              <ModuleFormGroup
                moduleColor="scheduler"
                label="Nome *"
                htmlFor="name"
                icon={<MapPin size={16} />}
                required
              >
                <ModuleInput
                  moduleColor="scheduler"
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  placeholder="Digite o nome da localização"
                  disabled={isSubmitting}
                />
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="scheduler"
                label="Endereço *"
                htmlFor="address"
                icon={<MapPin size={16} />}
                required
              >
                <ModuleInput
                  moduleColor="scheduler"
                  type="text"
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleChange}
                  required
                  placeholder="Digite o endereço completo"
                  disabled={isSubmitting}
                />
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="scheduler"
                label="Telefone"
                htmlFor="phone"
                icon={<Phone size={16} />}
              >
                <div className="relative">
                  <MaskedInput
                    type="phone"
                    value={formData.phone}
                    onChange={(e) =>
                      handleChange({
                        target: { name: "phone", value: e.target.value },
                      })
                    }
                    placeholder="(00) 00000-0000"
                    className="w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none"
                    disabled={isSubmitting}
                  />
                </div>
              </ModuleFormGroup>

              {/* Relacionamentos */}
              <div className="mb-8 mt-12">
                <div className="border-b-2 border-purple-400 dark:border-purple-500 pb-3 mb-4">
                  <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-purple-500 pl-3">
                    Vínculo Empresarial
                  </h4>
                  <p className="text-xs text-neutral-600 dark:text-gray-300 pl-3">
                    Associação com empresa e unidade:
                  </p>
                </div>
              </div>

              {/* Empresa (apenas para admin do sistema) */}
              {isSystemAdmin && (
                <ModuleFormGroup
                  moduleColor="scheduler"
                  label="Empresa *"
                  htmlFor="companyId"
                  icon={<Building size={16} />}
                  required
                >
                  <ModuleSelect
                    moduleColor="scheduler"
                    id="companyId"
                    name="companyId"
                    value={formData.companyId}
                    onChange={handleChange}
                    required
                    disabled={loading.companies || isSubmitting}
                    placeholder="Selecione uma empresa"
                  >
                    <option value="">Selecione uma empresa</option>
                    {loading.companies ? (
                      <option value="" disabled>
                        Carregando empresas...
                      </option>
                    ) : (
                      companyOptions.map((company) => (
                        <option key={company.id} value={company.id}>
                          {company.name}
                        </option>
                      ))
                    )}
                  </ModuleSelect>
                </ModuleFormGroup>
              )}

              {/* Informação da empresa para usuários não-admin */}
              {!isSystemAdmin && user?.companyId && (
                <ModuleFormGroup
                  moduleColor="scheduler"
                  label="Empresa"
                  icon={<Building size={16} />}
                  helpText="A localização será associada à sua empresa"
                >
                  <div className="px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg bg-neutral-50 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 flex items-center">
                    <Building className="h-4 w-4 mr-2 text-neutral-500 dark:text-gray-400" />
                    <span>{user?.companyName || "Sua empresa"}</span>
                  </div>
                  <input type="hidden" name="companyId" value={user.companyId} />
                </ModuleFormGroup>
              )}

              <ModuleFormGroup
                moduleColor="scheduler"
                label="Unidade (opcional)"
                htmlFor="branchId"
                icon={<Building size={16} />}
              >
                <ModuleSelect
                  moduleColor="scheduler"
                  id="branchId"
                  name="branchId"
                  value={formData.branchId}
                  onChange={handleChange}
                  disabled={loading.branches || !formData.companyId || isSubmitting}
                  placeholder="Selecione uma unidade"
                >
                  <option value="">Selecione uma unidade</option>
                  {loading.branches ? (
                    <option value="" disabled>
                      Carregando unidades...
                    </option>
                  ) : (
                    branchOptions.map((branch) => (
                      <option key={branch.id} value={branch.id}>
                        {branch.name} {branch.code && `(${branch.code})`}
                      </option>
                    ))
                  )}
                </ModuleSelect>
              </ModuleFormGroup>

              {/* Disponibilidade */}
              <div className="mb-8 mt-12">
                <div className="border-b-2 border-purple-400 dark:border-purple-500 pb-3 mb-4">
                  <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-purple-500 pl-3">
                    Disponibilidade
                  </h4>
                  <p className="text-xs text-neutral-600 dark:text-gray-300 pl-3">
                    Configure os horários de funcionamento:
                  </p>
                </div>
              
                {/* Toggle para usar disponibilidade */}
                <div className="mb-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      name="useAvailability"
                      checked={formData.useAvailability}
                      onChange={handleChange}
                      className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                    />
                    <span className="text-sm text-neutral-700 dark:text-neutral-300">
                      Definir horários de disponibilidade para esta localização
                    </span>
                  </label>
                </div>

                {/* Grid de horários */}
                {formData.useAvailability && (
                  <div 
                    className="border border-purple-300 dark:border-purple-600 rounded-lg p-4 bg-purple-50 dark:bg-purple-900/20 select-none"
                    onMouseUp={handleMouseUp}
                    onMouseLeave={handleMouseUp}
                  >
                    <div className="grid grid-cols-8 gap-2 text-sm">
                      {/* Cabeçalho com dias da semana */}
                      <div className="font-semibold text-center py-3 text-gray-700 dark:text-gray-300">Horário</div>
                      {daysOfWeek.map(day => (
                        <div key={day.key} className="font-semibold text-center py-3 text-purple-600 dark:text-purple-400">
                          {day.label}
                        </div>
                      ))}
                      
                      {/* Linhas de horários */}
                      {timeSlots.map(time => (
                        <React.Fragment key={time}>
                          <div className="text-right py-2 pr-3 text-gray-700 dark:text-gray-300 font-medium">
                            {time.replace(':00', 'h')}
                          </div>
                          {daysOfWeek.map(day => (
                            <div key={`${day.key}-${time}`} className="text-center">
                              <button
                                type="button"
                                onMouseDown={() => handleAvailabilityToggle(day.key, time, true)}
                                onMouseEnter={() => handleMouseEnter(day.key, time)}
                                className={`w-8 h-8 rounded-md text-sm font-bold transition-colors ${
                                  formData.availability[day.key]?.includes(time)
                                    ? 'bg-purple-500 text-white hover:bg-purple-600 shadow-sm'
                                    : 'bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-600 dark:text-gray-300'
                                }`}
                              >
                                {formData.availability[day.key]?.includes(time) ? '✓' : ''}
                              </button>
                            </div>
                          ))}
                        </React.Fragment>
                      ))}
                    </div>
                    
                    <div className="mt-4 text-sm text-gray-500 dark:text-gray-400">
                      Clique e arraste para selecionar múltiplos horários de disponibilidade
                    </div>
                  </div>
                )}
              </div>
            </form>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 border-t-2 border-gray-300 dark:border-gray-600 pt-4 flex-shrink-0 px-6 pb-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              Cancelar
            </button>
            <button
              type="submit"
              form="location-form"
              className="px-4 py-2 bg-purple-600 dark:bg-purple-700 text-white rounded-lg hover:bg-purple-700 dark:hover:bg-purple-800 transition-colors flex items-center gap-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  <MapPin size={16} />
                  <span>{location ? "Atualizar" : "Salvar"}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationFormModal;