"use client";

import { useState, useEffect, Suspense } from "react";
import { InputMask } from "@react-input/mask";
import {
  UserPlus,
  Mail,
  Lock,
  User,
  Phone,
  MapPin,
  CreditCard,
  Calendar,
  Building,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  ChevronRight,
  Users,
  Briefcase,
  Copy,
  ToggleLeft,
  ToggleRight,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { api } from "@/utils/api";
import { validateCoupon } from "@/services/subscriptionService";
import AddressForm from '@/components/common/AddressForm';
import { professionsService } from '@/app/modules/admin/services/professionsService';
import ModuleFormGroup from '@/components/ui/ModuleFormGroup';
import ModuleSelect from '@/components/ui/ModuleSelect';
import ModuleRadioGroup from '@/components/ui/ModuleRadioGroup';
import { ThemeToggle } from '@/components/ThemeToggle';

function SubscriptionSignupPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentStep, setCurrentStep] = useState(1);
  const [documentType, setDocumentType] = useState("cpf");
  
  // Estados para cálculo de preços
  const [isAnnual, setIsAnnual] = useState(false);
  const [isAnnualUpfront, setIsAnnualUpfront] = useState(false);
  const [userCount, setUserCount] = useState(5);
  const [couponCode, setCouponCode] = useState('');
  const [coupon, setCoupon] = useState(null);
  const [couponError, setCouponError] = useState('');
  const [isValidatingCoupon, setIsValidatingCoupon] = useState(false);
  
  const [formData, setFormData] = useState({
    // Dados pessoais
    login: "",
    fullName: "",
    email: "",
    confirmEmail: "",
    password: "",
    confirmPassword: "",
    cpf: "",
    cnpj: "",
    birthDate: "",
    address: "",
    phone: "",
    postalCode: "",
    state: "",
    city: "",
    neighborhood: "",
    userEmail: "",
    
    // Dados da empresa
    companyName: "",
    companyTradingName: "",
    companyCnpj: "",
    companyPhone: "",
    companyAddress: "",
    companyCity: "",
    companyState: "",
    companyPostalCode: "",
    companyNeighborhood: ""
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Lógica de cálculo de preços
  const basePrice = 19.90; // Preço base por usuário

  // Função para calcular desconto baseado na quantidade de usuários
  const getDiscountByUserCount = (users) => {
    if (users >= 200) return 40;
    if (users >= 100) return 35;
    if (users >= 50) return 25;
    if (users >= 20) return 15;
    if (users >= 5) return 10;
    return 0;
  };

  // Função para calcular preços
  const calculatePrice = (users, isAnnual, isAnnualUpfront) => {
    const discount = getDiscountByUserCount(users);
    const totalWithoutDiscount = users * basePrice;
    const discountAmount = totalWithoutDiscount * (discount / 100);
    const finalPrice = totalWithoutDiscount - discountAmount;

    // Desconto adicional de 10% para pagamento anual
    const annualDiscount = 0.10;
    const yearlyPrice = finalPrice * 12;
    const yearlyPriceWithAnnualDiscount = yearlyPrice * (1 - annualDiscount);
    // Desconto adicional de 10% para pagamento à vista do anual
    const upfrontDiscount = 0.10;
    const yearlyPriceWithUpfrontDiscount = yearlyPriceWithAnnualDiscount * (1 - upfrontDiscount);

    return {
      totalWithoutDiscount,
      discountAmount,
      finalPrice,
      discount,
      monthlyPrice: finalPrice,
      yearlyPrice,
      yearlyPriceWithAnnualDiscount,
      yearlyPriceWithUpfrontDiscount,
      annualDiscount: annualDiscount * 100,
      upfrontDiscount: upfrontDiscount * 100
    };
  };

  // Função para aplicar desconto do cupom
  const applyCouponDiscount = (price) => {
    if (coupon && coupon.coupon) {
      if (coupon.coupon.type === 'PERCENT') {
        return price * (1 - coupon.coupon.value / 100);
      } else if (coupon.coupon.type === 'VALUE') {
        return Math.max(0, price - coupon.coupon.value);
      }
    }
    return price;
  };

  const pricing = calculatePrice(userCount, isAnnual, isAnnualUpfront);
  const finalPrice = isAnnual
    ? (isAnnualUpfront ? pricing.yearlyPriceWithUpfrontDiscount : pricing.yearlyPriceWithAnnualDiscount)
    : pricing.monthlyPrice;
  const finalPriceWithCoupon = applyCouponDiscount(finalPrice);

  // Função para obter informações do desconto do cupom
  const getCouponInfo = () => {
    if (!coupon || !coupon.coupon) return null;
    
    const couponData = coupon.coupon;
    const discountDuration = isAnnual ? '12 meses' : '1 mês';
    
    return {
      code: couponData.code,
      type: couponData.type,
      value: couponData.value,
      duration: discountDuration,
      description: couponData.type === 'PERCENT' 
        ? `${couponData.value}% de desconto por ${discountDuration}`
        : `R$ ${couponData.value.toFixed(2)} de desconto por ${discountDuration}`
    };
  };

  // Opções de usuários predefinidas
  const userOptions = [1, 5, 20, 50, 100, 200];

  // Detecta se está em ambiente de desenvolvimento (localhost)
  const [isDevelopment, setIsDevelopment] = useState(false);
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsDevelopment(window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1');
    }
  }, []);

  // Dados de teste para desenvolvimento
  const devData = {
    login: "teste_dev",
    fullName: "Usuário de Teste",
    email: "<EMAIL>",
    confirmEmail: "<EMAIL>",
    password: "Super@123",
    confirmPassword: "Super@123",
    cpf: "123.456.789-00",
    cnpj: "12.345.678/0001-90",
    birthDate: "1990-01-01",
    address: "Rua de Teste, 123",
    phone: "(11) 99999-9999",
    postalCode: "01310-100",
    state: "SP",
    city: "São Paulo",
    neighborhood: "Bela Vista",
    companyName: "Empresa de Teste Ltda",
    companyTradingName: "Teste Corp",
    companyCnpj: "98.765.432/0001-10",
    companyPhone: "(11) 88888-8888",
    companyAddress: "Av. Paulista, 1000",
    companyCity: "São Paulo",
    companyState: "SP",
    companyPostalCode: "01310-100",
    companyNeighborhood: "Bela Vista"
  };

  // Preencher estados iniciais com dados da query string e localStorage
  useEffect(() => {
    const loadSavedData = () => {
      // Carregar dados salvos do localStorage
      const savedFormData = localStorage.getItem('signupFormData');
      console.log('Carregando dados salvos do localStorage:', savedFormData);
      if (savedFormData) {
        try {
          const parsedData = JSON.parse(savedFormData);
          console.log('Dados parsed:', parsedData);
          setFormData(parsedData);
        } catch (error) {
          console.error('Erro ao carregar dados salvos:', error);
        }
      }
    };

    // Carregar dados salvos PRIMEIRO
    loadSavedData();

    // Carregar dados da query string (só se não houver dados salvos)
    if (searchParams) {
      const users = parseInt(searchParams.get('users'));
      const annual = searchParams.get('annual') === '1';
      const upfront = searchParams.get('upfront') === '1';
      const coupon = searchParams.get('coupon') || '';
      if (!isNaN(users) && users > 0) setUserCount(users);
      setIsAnnual(annual);
      setIsAnnualUpfront(upfront);
      if (coupon) setCouponCode(coupon);
    }
  }, [searchParams]);

  // Adicionar listeners para recarregar dados quando o usuário retornar à página
  useEffect(() => {
    const loadSavedDataFromStorage = () => {
      const savedFormData = localStorage.getItem('signupFormData');
      if (savedFormData) {
        try {
          const parsedData = JSON.parse(savedFormData);
          setFormData(parsedData);
        } catch (error) {
          console.error('Erro ao carregar dados salvos:', error);
        }
      }
    };

    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Página ficou visível, carregar dados salvos novamente
        loadSavedDataFromStorage();
      }
    };

    const handleFocus = () => {
      // Página recebeu foco, carregar dados salvos novamente
      loadSavedDataFromStorage();
    };

    const handlePageShow = (event) => {
      // Página foi mostrada (incluindo voltar pelo histórico do navegador)
      if (event.persisted) {
        // Página foi carregada do cache (bfcache)
        loadSavedDataFromStorage();
      }
    };

    // Adicionar event listeners
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('pageshow', handlePageShow);

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('pageshow', handlePageShow);
    };
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      const newFormData = {
        ...prev,
        [name]: value,
      };
      
      // Salvar dados no localStorage
      try {
        localStorage.setItem('signupFormData', JSON.stringify(newFormData));
      } catch (error) {
        console.error('Erro ao salvar dados:', error);
      }
      
      return newFormData;
    });
    
    // Limpa o erro do campo quando o usuário começa a digitar
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const removeMask = (value) => {
    return value ? value.replace(/\D/g, "") : "";
  };

  // Função para preencher dados de desenvolvimento
  const fillDevData = () => {
    if (isDevelopment) {
      setFormData(prevFormData => {
        const newFormData = { ...prevFormData, ...devData };
        // Salvar no localStorage também
        try {
          localStorage.setItem('signupFormData', JSON.stringify(newFormData));
        } catch (error) {
          console.error('Erro ao salvar dados:', error);
        }
        return newFormData;
      });
    }
  };

  // Função para pular para o próximo passo (apenas em desenvolvimento)
  const skipToStep = (step) => {
    if (isDevelopment) {
      setCurrentStep(step);
    }
  };

  const validateStep1 = () => {
    const newErrors = {};

    if (!formData.login) {
      newErrors.login = "Login é obrigatório";
    }
    if (!formData.fullName) {
      newErrors.fullName = "Nome é obrigatório";
    }
    if (!formData.email) {
      newErrors.email = "Email é obrigatório";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }
    if (!formData.confirmEmail) {
      newErrors.confirmEmail = "Confirmação de email é obrigatória";
    } else if (!/\S+@\S+\.\S+/.test(formData.confirmEmail)) {
      newErrors.confirmEmail = "Confirmação de email inválida";
    } else if (formData.email !== formData.confirmEmail) {
      newErrors.confirmEmail = "Emails não conferem";
    }
    if (!formData.password) {
      newErrors.password = "Senha é obrigatória";
    } else if (formData.password.length < 6) {
      newErrors.password = "Senha deve ter no mínimo 6 caracteres";
    }
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Confirmação de senha é obrigatória";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Senhas não conferem";
    }

    if (documentType === "cpf") {
      if (!formData.cpf) {
        newErrors.cpf = "CPF é obrigatório";
      }
    }
    if (documentType === "cnpj") {
      if (!formData.cnpj) {
        newErrors.cnpj = "CNPJ é obrigatório";
      }
    }
    if (!formData.phone) {
      newErrors.phone = "Telefone é obrigatório";
    }
    if (!formData.birthDate) {
      newErrors.birthDate = "Data de nascimento é obrigatória";
    }
    if (!formData.postalCode) {
      newErrors.postalCode = "CEP é obrigatório";
    }
    if (!formData.state) {
      newErrors.state = "Estado é obrigatório";
    }
    if (!formData.city) {
      newErrors.city = "Cidade é obrigatória";
    }
    if (!formData.address) {
      newErrors.address = "Endereço é obrigatório";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateStep2 = () => {
    const newErrors = {};

    if (!formData.companyName) {
      newErrors.companyName = "Nome da empresa é obrigatório";
    }
    if (!formData.companyCnpj) {
      newErrors.companyCnpj = "CNPJ da empresa é obrigatório";
    }
    if (!formData.companyPhone) {
      newErrors.companyPhone = "Telefone da empresa é obrigatório";
    }
    if (!formData.companyAddress) {
      newErrors.companyAddress = "Endereço da empresa é obrigatório";
    }
    if (!formData.companyCity) {
      newErrors.companyCity = "Cidade da empresa é obrigatória";
    }
    if (!formData.companyState) {
      newErrors.companyState = "Estado da empresa é obrigatório";
    }
    if (!formData.companyPostalCode) {
      newErrors.companyPostalCode = "CEP da empresa é obrigatório";
    }
    if (!formData.userEmail) {
      newErrors.userEmail = "Email do usuário é obrigatório";
    } else if (!/\S+@\S+\.\S+/.test(formData.userEmail)) {
      newErrors.userEmail = "Email do usuário inválido";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateAllSteps = () => {
    const newErrors = {};

    // Validação Passo 1
    if (!formData.login) {
      newErrors.login = "Login é obrigatório";
    }
    if (!formData.fullName) {
      newErrors.fullName = "Nome é obrigatório";
    }
    if (!formData.email) {
      newErrors.email = "Email é obrigatório";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }
    if (!formData.password) {
      newErrors.password = "Senha é obrigatória";
    }
    if (documentType === "cpf" && !formData.cpf) {
      newErrors.cpf = "CPF é obrigatório";
    }
    if (documentType === "cnpj" && !formData.cnpj) {
      newErrors.cnpj = "CNPJ é obrigatório";
    }

    // Validação Passo 2
    if (!formData.companyName) {
      newErrors.companyName = "Nome da empresa é obrigatório";
    }
    if (!formData.companyCnpj) {
      newErrors.companyCnpj = "CNPJ da empresa é obrigatório";
    }
    if (!formData.companyPhone) {
      newErrors.companyPhone = "Telefone da empresa é obrigatório";
    }
    if (!formData.companyAddress) {
      newErrors.companyAddress = "Endereço da empresa é obrigatório";
    }
    if (!formData.companyCity) {
      newErrors.companyCity = "Cidade da empresa é obrigatória";
    }
    if (!formData.companyState) {
      newErrors.companyState = "Estado da empresa é obrigatório";
    }
    if (!formData.companyPostalCode) {
      newErrors.companyPostalCode = "CEP da empresa é obrigatório";
    }
    if (!formData.userEmail) {
      newErrors.userEmail = "Email do usuário é obrigatório";
    } else if (!/\S+@\S+\.\S+/.test(formData.userEmail)) {
      newErrors.userEmail = "Email do usuário inválido";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2);
    } else if (currentStep === 2 && validateStep2()) {
      setCurrentStep(3);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // NOVA FUNÇÃO: Solicitar confirmação de email e redirecionar
  const handleSubmit = async () => {
    console.log('handleSubmit chamado - dados do formulário:', formData);
    console.log('Errors atuais:', errors);
    
    // Validar todos os dados antes de prosseguir
    if (!validateAllSteps()) {
      console.log('Validação falhou, errors:', errors);
      // Se há erros de validação, voltar para o primeiro passo com erro
      if (errors.login || errors.fullName || errors.email || errors.password || errors.cpf || errors.cnpj) {
        setCurrentStep(1);
      } else if (errors.companyName || errors.companyCnpj || errors.companyPhone || errors.userEmail) {
        setCurrentStep(2);
      }
      return;
    }
    
    console.log('Validação passou, iniciando processo de submissão...');

    setIsLoading(true);
    try {
      // 1. Criar sessão segura no backend para trial
      const sessionResponse = await api.post("/auth/create-signup-session", {
        // Dados pessoais
        login: formData.login,
        fullName: formData.fullName,
        email: formData.email,
        password: formData.password,
        cpf: documentType === "cpf" ? formData.cpf : undefined,
        cnpj: documentType === "cnpj" ? formData.cnpj : undefined,
        birthDate: formData.birthDate || undefined,
        address: formData.address || undefined,
        phone: formData.phone || undefined,

        // Dados da empresa
        companyName: formData.companyName,
        companyTradingName: formData.companyTradingName,
        companyCnpj: formData.companyCnpj,
        companyPhone: formData.companyPhone,
        companyAddress: formData.companyAddress,
        companyCity: formData.companyCity,
        companyState: formData.companyState,
        companyPostalCode: formData.companyPostalCode,

        // Tipo de assinatura
        subscriptionType: 'trial'
      });

      // 2. Solicitar confirmação de email
      await api.post("/auth/request-email-confirmation", {
        email: formData.email,
        userName: formData.fullName,
        sessionId: sessionResponse.data.sessionId
      });

      // 3. Redirecionar para confirmação com sessionId (mantendo dados salvos)
      router.push(`/subscription/signup/confirm-email?sessionId=${sessionResponse.data.sessionId}`);

    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        submit: error.response?.data?.message || "Erro ao processar solicitação",
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartPaidPlan = async () => {
    console.log('handleStartPaidPlan chamado - dados do formulário:', formData);
    console.log('Errors atuais:', errors);
    
    // Validar todos os dados antes de prosseguir
    if (!validateAllSteps()) {
      console.log('Validação falhou, errors:', errors);
      // Se há erros de validação, voltar para o primeiro passo com erro
      if (errors.login || errors.fullName || errors.email || errors.password || errors.cpf || errors.cnpj) {
        setCurrentStep(1);
      } else if (errors.companyName || errors.companyCnpj || errors.companyPhone || errors.userEmail) {
        setCurrentStep(2);
      }
      return;
    }

    console.log('Validação passou, iniciando processo de plano pago...');

    setIsLoading(true);
    try {
      // 1. Criar sessão segura no backend com dados do plano
      const sessionResponse = await api.post("/auth/create-signup-session", {
        // Dados pessoais
        login: formData.login,
        fullName: formData.fullName,
        email: formData.email,
        password: formData.password,
        cpf: documentType === "cpf" ? formData.cpf : undefined,
        cnpj: documentType === "cnpj" ? formData.cnpj : undefined,
        birthDate: formData.birthDate || undefined,
        address: formData.address || undefined,
        phone: formData.phone || undefined,

        // Dados da empresa
        companyName: formData.companyName,
        companyTradingName: formData.companyTradingName,
        companyCnpj: formData.companyCnpj,
        companyPhone: formData.companyPhone,
        companyAddress: formData.companyAddress,
        companyCity: formData.companyCity,
        companyState: formData.companyState,
        companyPostalCode: formData.companyPostalCode,

        // Tipo de assinatura
        subscriptionType: 'paid',
        
        // Configurações do plano (serão recalculadas no backend)
        planConfig: {
          users: userCount,
          annual: isAnnual,
          upfront: isAnnualUpfront,
          coupon: coupon && coupon.coupon ? coupon.coupon.code : ''
        }
      });

      // 2. Solicitar confirmação de email
      await api.post("/auth/request-email-confirmation", {
        email: formData.email,
        userName: formData.fullName,
        sessionId: sessionResponse.data.sessionId // ID da sessão segura
      });

      // 3. Redirecionar para confirmação com sessionId (mantendo dados salvos)
      router.push(`/subscription/signup/confirm-email?sessionId=${sessionResponse.data.sessionId}`);

    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        submit: error.response?.data?.message || "Erro ao processar solicitação",
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const inputClasses = "block w-full pl-10 pr-4 py-3.5 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700 transition-all duration-200 hover:border-gray-300 dark:hover:border-gray-500 shadow-sm hover:shadow-md focus:shadow-lg";
  const labelClasses = "block text-sm font-semibold text-gray-800 dark:text-gray-200 mb-2";
  const iconContainerClasses = "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10";

  // Componente para label com asterisco obrigatório
  const RequiredLabel = ({ htmlFor, children, className = labelClasses }) => (
    <label className={className} htmlFor={htmlFor}>
      {children}
      <span className="text-red-500 ml-1">*</span>
    </label>
  );

  const [selectedPlan, setSelectedPlan] = useState('completo'); // 'completo' à esquerda por padrão

  return (
    <div className="relative min-h-screen bg-orange-50 dark:bg-background transition-colors">
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>
      <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-orange-50/30 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
        <div className="bg-white/80 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/30 w-full max-w-5xl p-8 md:p-10">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex justify-center items-center mb-4">
              <UserPlus className="h-12 w-12 text-orange-500" />
              <h2 className="mx-4 text-3xl font-bold text-gray-900 dark:text-white">
                Criar conta e escolher plano
              </h2>
            </div>

            {/* Progress Steps */}
            <div className="flex justify-center items-center space-x-4 mb-6">
              {[1, 2, 3].map((step) => (
                <div key={step} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= step
                      ? 'bg-orange-500 text-white'
                      : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
                  }`}>
                    {currentStep > step ? <CheckCircle className="w-5 h-5" /> : step}
                  </div>
                  {step < 3 && (
                    <div className={`w-12 h-1 mx-2 ${
                      currentStep > step ? 'bg-orange-500' : 'bg-gray-200 dark:bg-gray-600'
                    }`} />
                  )}
                </div>
              ))}
            </div>

            <div className="text-sm text-gray-600 dark:text-gray-400">
              {currentStep === 1 && "Dados pessoais"}
              {currentStep === 2 && "Dados da empresa"}
              {currentStep === 3 && "Confirmação e planos"}
            </div>

            {/* Botões de Desenvolvimento (apenas em localhost) */}
            {isDevelopment && (
              <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <p className="text-xs text-yellow-800 dark:text-yellow-300 mb-2 text-center">
                  🚧 Modo Desenvolvimento - Atalhos para testes
                </p>
                <div className="flex justify-center gap-2">
                  <button
                    type="button"
                    onClick={fillDevData}
                    className="px-3 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600"
                  >
                    Preencher Dados
                  </button>
                  <button
                    type="button"
                    onClick={() => skipToStep(1)}
                    className={`px-3 py-1 rounded text-xs ${currentStep === 1 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                  >
                    Passo 1
                  </button>
                  <button
                    type="button"
                    onClick={() => skipToStep(2)}
                    className={`px-3 py-1 rounded text-xs ${currentStep === 2 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                  >
                    Passo 2
                  </button>
                  <button
                    type="button"
                    onClick={() => skipToStep(3)}
                    className={`px-3 py-1 rounded text-xs ${currentStep === 3 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`}
                  >
                    Passo 3
                  </button>
                </div>
              </div>
            )}
          </div>

          {errors.submit && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
              {errors.submit}
            </div>
          )}

          {/* Step 1: Dados de Assinatura do Plano */}
          {currentStep === 1 && (
            <div className="space-y-8">
              {/* Seção Dados Pessoais */}
              <div className="bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-blue-900/10 dark:to-indigo-900/10 rounded-2xl p-6 border border-blue-100 dark:border-blue-800/30">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Dados Pessoais</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Nome Completo */}
                <div>
                  <RequiredLabel htmlFor="fullName">Nome Completo</RequiredLabel>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <User className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="fullName"
                      name="fullName"
                      type="text"
                      value={formData.fullName}
                      onChange={handleChange}
                      required
                      className={cn(inputClasses, errors.fullName && "border-red-500")}
                      placeholder="Seu nome completo"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.fullName && <p className="mt-1 text-xs text-red-600">{errors.fullName}</p>}
                </div>
                {/* Email */}
                <div>
                  <RequiredLabel htmlFor="email">Email</RequiredLabel>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className={cn(inputClasses, errors.email && "border-red-500")}
                      placeholder="<EMAIL>"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.email && <p className="mt-1 text-xs text-red-600">{errors.email}</p>}
                </div>
                {/* Confirmação do Email */}
                <div>
                  <RequiredLabel htmlFor="confirmEmail">Confirmação do Email</RequiredLabel>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Mail className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="confirmEmail"
                      name="confirmEmail"
                      type="email"
                      value={formData.confirmEmail || ''}
                      onChange={handleChange}
                      required
                      className={cn(inputClasses, errors.confirmEmail && "border-red-500")}
                      placeholder="Confirme seu email"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.confirmEmail && <p className="mt-1 text-xs text-red-600">{errors.confirmEmail}</p>}
                </div>
                {/* Tipo de Documento */}
                <div>
                  <label className={labelClasses}>Tipo de Documento</label>
                  <div className="pt-2">
                    <ModuleRadioGroup
                      name="documentType"
                      value={documentType}
                      onChange={(e) => setDocumentType(e.target.value)}
                      options={[
                        { value: 'cpf', label: 'CPF' },
                        { value: 'cnpj', label: 'CNPJ' }
                      ]}
                      moduleColor="admin"
                      layout="horizontal"
                      size="md"
                    />
                  </div>
                </div>
                {/* CPF/CNPJ */}
                <div>
                  <RequiredLabel htmlFor={documentType}>{documentType === "cpf" ? "CPF" : "CNPJ"}</RequiredLabel>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <CreditCard className="h-5 w-5 text-gray-400" />
                    </div>
                    <InputMask
                      mask={documentType === "cpf" ? "___.___.___-__" : "__.___.___/____-__"}
                      replacement={{ _: /\d/ }}
                      name={documentType}
                      value={formData[documentType]}
                      onChange={handleChange}
                      required
                      className={cn(inputClasses, errors[documentType] && "border-red-500")}
                      placeholder={documentType === "cpf" ? "000.000.000-00" : "00.000.000/0000-00"}
                      disabled={isLoading}
                    />
                  </div>
                  {errors[documentType] && <p className="mt-1 text-xs text-red-600">{errors[documentType]}</p>}
                </div>
                {/* Telefone */}
                <div>
                  <RequiredLabel htmlFor="phone">Telefone</RequiredLabel>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Phone className="h-5 w-5 text-gray-400" />
                    </div>
                    <InputMask
                      mask="(__) _____-____"
                      replacement={{ _: /\d/ }}
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      required
                      className={cn(inputClasses, errors.phone && "border-red-500")}
                      placeholder="(11) 99999-9999"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.phone && <p className="mt-1 text-xs text-red-600">{errors.phone}</p>}
                </div>
                {/* Data de Nascimento */}
                <div>
                  <RequiredLabel htmlFor="birthDate">Data de Nascimento</RequiredLabel>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Calendar className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="birthDate"
                      name="birthDate"
                      type="date"
                      value={formData.birthDate}
                      onChange={handleChange}
                      required
                      className={cn(inputClasses, errors.birthDate && "border-red-500")}
                      disabled={isLoading}
                    />
                  </div>
                  {errors.birthDate && <p className="mt-1 text-xs text-red-600">{errors.birthDate}</p>}
                  <p className="text-xs text-gray-500 mt-1">É necessário ser maior de 18 anos.</p>
                </div>
                </div>
              </div>
              
              {/* Seção Endereço */}
              <div className="bg-gradient-to-r from-green-50/50 to-emerald-50/50 dark:from-green-900/10 dark:to-emerald-900/10 rounded-2xl p-6 border border-green-100 dark:border-green-800/30">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center mr-4">
                    <MapPin className="h-5 w-5 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Endereço</h3>
                </div>
                {/* Endereço/CEP/Estado/Cidade preenchidos automaticamente */}
              <AddressForm
                formData={formData}
                setFormData={setFormData}
                errors={errors}
                isLoading={isLoading}
                moduleColor="admin"
                requiredFields={{
                  postalCode: true,
                  state: true,
                  city: true,
                  address: true
                }}
              />
              </div>
              
              {/* Botões de Navegação */}
              <div className="flex justify-between pt-8">
                <Link
                  href="/landing"
                  className="inline-flex items-center px-6 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-xl shadow-sm text-sm font-semibold text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 hover:shadow-md"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Voltar
                </Link>
                <button
                  type="button"
                  onClick={handleNextStep}
                  className="inline-flex items-center px-8 py-3 border border-transparent rounded-xl shadow-sm text-sm font-semibold text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg transform hover:scale-[1.02]"
                >
                  Próximo
                  <ArrowRight className="w-4 h-4 ml-2" />
                </button>
              </div>
            </div>
          )}

          {/* Step 2: Sobre a Empresa e Usuário */}
          {currentStep === 2 && (
            <div className="space-y-8">
              {/* Seção Dados da Empresa */}
              <div className="bg-gradient-to-r from-purple-50/50 to-violet-50/50 dark:from-purple-900/10 dark:to-violet-900/10 rounded-2xl p-6 border border-purple-100 dark:border-purple-800/30">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-purple-500 rounded-xl flex items-center justify-center mr-4">
                    <Building className="h-5 w-5 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Dados da Empresa</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Nome da Empresa */}
                <div>
                  <RequiredLabel htmlFor="companyName">Nome da Empresa</RequiredLabel>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Building className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="companyName"
                      name="companyName"
                      type="text"
                      value={formData.companyName}
                      onChange={handleChange}
                      required
                      className={cn(inputClasses, errors.companyName && "border-red-500")}
                      placeholder="Razão social da empresa"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.companyName && <p className="mt-1 text-xs text-red-600">{errors.companyName}</p>}
                </div>
                {/* Nome Fantasia */}
                <div>
                  <label className={labelClasses} htmlFor="companyTradingName">Nome Fantasia (opcional)</label>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Building className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="companyTradingName"
                      name="companyTradingName"
                      type="text"
                      value={formData.companyTradingName}
                      onChange={handleChange}
                      className={inputClasses}
                      placeholder="Nome fantasia"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.companyTradingName && <p className="mt-1 text-xs text-red-600">{errors.companyTradingName}</p>}
                </div>
                {/* CNPJ da Empresa + opção usar mesmo CNPJ */}
                <div>
                  <RequiredLabel htmlFor="companyCnpj">CNPJ da Empresa</RequiredLabel>
                  <div className="flex items-center gap-2">
                    <div className="relative flex-1">
                      <div className={iconContainerClasses}>
                        <CreditCard className="h-5 w-5 text-gray-400" />
                      </div>
                      <InputMask
                        mask="__.___.___/____-__"
                        replacement={{ _: /\d/ }}
                        name="companyCnpj"
                        value={formData.companyCnpj}
                        onChange={handleChange}
                        required
                        className={cn(inputClasses, errors.companyCnpj && "border-red-500")}
                        placeholder="00.000.000/0000-00"
                        disabled={isLoading}
                      />
                    </div>
                    <button type="button" className="inline-flex items-center px-3 py-2 text-xs font-semibold text-purple-700 bg-purple-50 hover:bg-purple-100 dark:bg-purple-900/20 dark:text-purple-300 dark:hover:bg-purple-900/40 rounded-lg transition-all duration-200 border border-purple-200 dark:border-purple-800 hover:shadow-sm ml-2" onClick={() => setFormData(prev => ({ ...prev, companyCnpj: formData.cnpj }))}>
                      <Copy className="w-3 h-3 mr-1" /> Usar mesmo CNPJ
                    </button>
                  </div>
                  {errors.companyCnpj && <p className="mt-1 text-xs text-red-600">{errors.companyCnpj}</p>}
                </div>
                {/* Telefone da Empresa + opção usar mesmo telefone */}
                <div>
                  <RequiredLabel htmlFor="companyPhone">Telefone da Empresa</RequiredLabel>
                  <div className="flex items-center gap-2">
                    <div className="relative flex-1">
                      <div className={iconContainerClasses}>
                        <Phone className="h-5 w-5 text-gray-400" />
                      </div>
                      <InputMask
                        mask="(__) _____-____"
                        replacement={{ _: /\d/ }}
                        name="companyPhone"
                        value={formData.companyPhone}
                        onChange={handleChange}
                        required
                        className={cn(inputClasses, errors.companyPhone && "border-red-500")}
                        placeholder="(11) 99999-9999"
                        disabled={isLoading}
                      />
                    </div>
                    <button type="button" className="inline-flex items-center px-3 py-2 text-xs font-semibold text-purple-700 bg-purple-50 hover:bg-purple-100 dark:bg-purple-900/20 dark:text-purple-300 dark:hover:bg-purple-900/40 rounded-lg transition-all duration-200 border border-purple-200 dark:border-purple-800 hover:shadow-sm ml-2" onClick={() => setFormData(prev => ({ ...prev, companyPhone: formData.phone }))}>
                      <Copy className="w-3 h-3 mr-1" /> Usar mesmo telefone
                    </button>
                  </div>
                  {errors.companyPhone && <p className="mt-1 text-xs text-red-600">{errors.companyPhone}</p>}
                </div>
                </div>
              </div>
              
              {/* Seção Endereço da Empresa */}
              <div className="bg-gradient-to-r from-cyan-50/50 to-sky-50/50 dark:from-cyan-900/10 dark:to-sky-900/10 rounded-2xl p-6 border border-cyan-100 dark:border-cyan-800/30">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-cyan-500 rounded-xl flex items-center justify-center mr-4">
                    <MapPin className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1 flex items-center justify-between">
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white">Endereço da Empresa</h3>
                    <button type="button" className="inline-flex items-center px-3 py-2 text-xs font-semibold text-cyan-700 bg-cyan-50 hover:bg-cyan-100 dark:bg-cyan-900/20 dark:text-cyan-300 dark:hover:bg-cyan-900/40 rounded-lg transition-all duration-200 border border-cyan-200 dark:border-cyan-800 hover:shadow-sm" onClick={() => {
                      if (formData.companyPostalCode !== formData.postalCode) {
                        setFormData(prev => ({ ...prev, companyPostalCode: formData.postalCode, companyAddress: formData.address, companyCity: formData.city, companyState: formData.state }));
                      }
                    }}>
                      <Copy className="w-3 h-3 mr-1" /> Usar mesmo CEP
                    </button>
                  </div>
                </div>
              <AddressForm
                formData={formData}
                setFormData={setFormData}
                errors={errors}
                isLoading={isLoading}
                moduleColor="admin"
                fieldMapping={{
                  cep: 'companyPostalCode',
                  logradouro: 'companyAddress',
                  localidade: 'companyCity',
                  uf: 'companyState',
                }}
                requiredFields={{
                  companyPostalCode: true,
                  companyState: true,
                  companyCity: true,
                  companyAddress: true
                }}
              />
              </div>
              
              {/* Seção Usuário do Sistema */}
              <div className="bg-gradient-to-r from-orange-50/50 to-amber-50/50 dark:from-orange-900/10 dark:to-amber-900/10 rounded-2xl p-6 border border-orange-100 dark:border-orange-800/30">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-orange-500 rounded-xl flex items-center justify-center mr-4">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Usuário do Sistema</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Login */}
                <div>
                  <RequiredLabel htmlFor="login">Login</RequiredLabel>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <input
                      id="login"
                      name="login"
                      type="text"
                      value={formData.login}
                      onChange={handleChange}
                      required
                      className={cn(inputClasses, errors.login && "border-red-500")}
                      placeholder="Seu login"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.login && <p className="mt-1 text-xs text-red-600">{errors.login}</p>}
                </div>
                {/* Senha */}
                <div>
                  <RequiredLabel htmlFor="password">Senha</RequiredLabel>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="password"
                      name="password"
                      type="password"
                      value={formData.password}
                      onChange={handleChange}
                      required
                      className={cn(inputClasses, errors.password && "border-red-500")}
                      placeholder="••••••••"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.password && <p className="mt-1 text-xs text-red-600">{errors.password}</p>}
                </div>
                {/* Confirmar Senha */}
                <div>
                  <RequiredLabel htmlFor="confirmPassword">Confirmar Senha</RequiredLabel>
                  <div className="relative">
                    <div className={iconContainerClasses}>
                      <Lock className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      required
                      className={cn(inputClasses, errors.confirmPassword && "border-red-500")}
                      placeholder="••••••••"
                      disabled={isLoading}
                    />
                  </div>
                  {errors.confirmPassword && <p className="mt-1 text-xs text-red-600">{errors.confirmPassword}</p>}
                </div>
                {/* Email do Usuário + opção usar mesmo email */}
                <div>
                  <RequiredLabel htmlFor="userEmail">Email do seu Usuário</RequiredLabel>
                  <div className="flex items-center gap-2">
                    <div className="relative flex-1">
                      <div className={iconContainerClasses}>
                        <Mail className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        id="userEmail"
                        name="userEmail"
                        type="email"
                        value={formData.userEmail || ''}
                        onChange={handleChange}
                        required
                        className={cn(inputClasses, errors.userEmail && "border-red-500")}
                        placeholder="Email do usuário"
                        disabled={isLoading}
                      />
                    </div>
                    <button type="button" className="inline-flex items-center px-3 py-2 text-xs font-semibold text-orange-700 bg-orange-50 hover:bg-orange-100 dark:bg-orange-900/20 dark:text-orange-300 dark:hover:bg-orange-900/40 rounded-lg transition-all duration-200 border border-orange-200 dark:border-orange-800 hover:shadow-sm ml-2" onClick={() => setFormData(prev => ({ ...prev, userEmail: formData.email }))}>
                      <Copy className="w-3 h-3 mr-1" /> Usar mesmo email
                    </button>
                  </div>
                  {errors.userEmail && <p className="mt-1 text-xs text-red-600">{errors.userEmail}</p>}
                </div>
                </div>
              </div>
              
              {/* Botões de Navegação */}
              <div className="flex justify-between pt-8">
                <button
                  type="button"
                  onClick={handlePrevStep}
                  className="inline-flex items-center px-6 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-xl shadow-sm text-sm font-semibold text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 hover:shadow-md"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Anterior
                </button>
                <button
                  type="button"
                  onClick={handleNextStep}
                  className="inline-flex items-center px-8 py-3 border border-transparent rounded-xl shadow-sm text-sm font-semibold text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg transform hover:scale-[1.02]"
                >
                  Próximo
                  <ArrowRight className="w-4 h-4 ml-2" />
                </button>
              </div>
            </div>
          )}

          {/* Step 3: Escolha do Plano */}
          {currentStep === 3 && (
            <div className="space-y-6">
              {/* Resumo da Conta */}
              <div className="bg-gradient-to-r from-slate-50/50 to-gray-50/50 dark:from-slate-900/20 dark:to-gray-900/20 rounded-2xl border border-slate-200 dark:border-slate-700/30 p-6 shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="w-10 h-10 bg-slate-500 rounded-xl flex items-center justify-center mr-4">
                    <CheckCircle className="h-5 w-5 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 dark:text-white">
                    Resumo da sua conta
                  </h4>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Dados Pessoais */}
                  <div>
                    <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-3">Dados Pessoais</h5>
                    <div className="space-y-2 text-sm">
                      <div><span className="text-gray-500">Nome:</span> <span className="font-medium">{formData.fullName}</span></div>
                      <div><span className="text-gray-500">Email:</span> <span className="font-medium">{formData.email}</span></div>
                      <div><span className="text-gray-500">Login:</span> <span className="font-medium">{formData.login}</span></div>
                      {formData[documentType] && (
                        <div><span className="text-gray-500">{documentType.toUpperCase()}:</span> <span className="font-medium">{formData[documentType]}</span></div>
                      )}
                      <div><span className="text-gray-500">Telefone:</span> <span className="font-medium">{formData.phone}</span></div>
                      <div><span className="text-gray-500">Data de Nascimento:</span> <span className="font-medium">{formData.birthDate}</span></div>
                    </div>
                  </div>
                  {/* Endereço Pessoal */}
                  <div>
                    <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-3">Endereço</h5>
                    <div className="space-y-2 text-sm">
                      <div><span className="text-gray-500">CEP:</span> <span className="font-medium">{formData.postalCode}</span></div>
                      <div><span className="text-gray-500">Estado:</span> <span className="font-medium">{formData.state}</span></div>
                      <div><span className="text-gray-500">Cidade:</span> <span className="font-medium">{formData.city}</span></div>
                      <div><span className="text-gray-500">Endereço:</span> <span className="font-medium">{formData.address}</span></div>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  {/* Dados da Empresa */}
                  <div>
                    <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-3">Empresa</h5>
                    <div className="space-y-2 text-sm">
                      <div><span className="text-gray-500">Empresa:</span> <span className="font-medium">{formData.companyName}</span></div>
                      {formData.companyTradingName && (
                        <div><span className="text-gray-500">Nome Fantasia:</span> <span className="font-medium">{formData.companyTradingName}</span></div>
                      )}
                      <div><span className="text-gray-500">CNPJ:</span> <span className="font-medium">{formData.companyCnpj}</span></div>
                      <div><span className="text-gray-500">Telefone:</span> <span className="font-medium">{formData.companyPhone}</span></div>
                    </div>
                  </div>
                  {/* Endereço da Empresa */}
                  <div>
                    <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-3">Endereço da Empresa</h5>
                    <div className="space-y-2 text-sm">
                      <div><span className="text-gray-500">CEP:</span> <span className="font-medium">{formData.companyPostalCode}</span></div>
                      <div><span className="text-gray-500">Estado:</span> <span className="font-medium">{formData.companyState}</span></div>
                      <div><span className="text-gray-500">Cidade:</span> <span className="font-medium">{formData.companyCity}</span></div>
                      <div><span className="text-gray-500">Endereço:</span> <span className="font-medium">{formData.companyAddress}</span></div>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                  {/* Usuário do Sistema */}
                  <div>
                    <h5 className="font-medium text-gray-700 dark:text-gray-300 mb-3">Usuário do Sistema</h5>
                    <div className="space-y-2 text-sm">
                      <div><span className="text-gray-500">Login:</span> <span className="font-medium">{formData.login}</span></div>
                      <div><span className="text-gray-500">Email:</span> <span className="font-medium">{formData.userEmail || formData.email}</span></div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Alternância de Plano estilo switch menor */}
              <div className="flex justify-center items-center mb-4 gap-3">
                <span className={`text-sm font-medium ${selectedPlan === 'completo' ? 'text-teal-700' : 'text-gray-400'}`}>Plano Completo</span>
                <button
                  type="button"
                  aria-label="Alternar plano"
                  className={`relative inline-flex h-6 w-10 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 ${selectedPlan === 'gratuito' ? 'bg-gray-400' : 'bg-teal-500'}`}
                  onClick={() => setSelectedPlan(selectedPlan === 'completo' ? 'gratuito' : 'completo')}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white shadow transition ${selectedPlan === 'gratuito' ? 'translate-x-5' : 'translate-x-1'}`}
                  />
                </button>
                <span className={`text-sm font-medium ${selectedPlan === 'gratuito' ? 'text-gray-700' : 'text-gray-400'}`}>Plano Gratuito</span>
              </div>
              {/* Renderização condicional dos planos */}
              {selectedPlan === 'completo' && (
                <div className="bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 border-2 border-teal-200 dark:border-teal-800 rounded-xl p-6 relative overflow-hidden">
                  {/* Plano Completo - conteúdo completo */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-teal-100 dark:bg-teal-800 rounded-full flex items-center justify-center">
                        <CreditCard className="h-6 w-6 text-teal-600 dark:text-teal-400" />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-teal-800 dark:text-teal-300">
                          Plano Completo
                        </h4>
                        <p className="text-teal-600 dark:text-teal-400">
                          R$ {finalPriceWithCoupon.toFixed(2).replace('.', ',')} {isAnnual ? (isAnnualUpfront ? 'anual à vista' : 'anual') : 'mensal'}
                        </p>
                      </div>
                    </div>
                    <button
                      onClick={handleStartPaidPlan}
                      disabled={isLoading}
                      className="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-teal-600 hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Validando...
                        </>
                      ) : (
                        <>
                          Começar Agora
                          <ChevronRight className="w-5 h-5 ml-2" />
                        </>
                      )}
                    </button>
                  </div>
                  {/* Configurações do Plano */}
                  <div className="space-y-6">
                    {/* Toggle Anual/Mensal */}
                    <div className="flex items-center justify-center">
                      <span className={`mr-3 text-sm ${!isAnnual ? 'text-teal-800 dark:text-teal-300 font-medium' : 'text-gray-500 dark:text-gray-400'}`}>Mensal</span>
                      <button
                        onClick={() => { setIsAnnual(!isAnnual); setIsAnnualUpfront(false); }}
                        className={`relative inline-flex h-5 w-10 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 ${isAnnual ? 'bg-teal-600' : 'bg-teal-300 dark:bg-teal-600'}`}
                      >
                        <span
                          className={`inline-block h-3 w-3 transform rounded-full bg-white transition ${isAnnual ? 'translate-x-6' : 'translate-x-1'}`}
                        />
                      </button>
                      <span className={`ml-3 text-sm ${isAnnual ? 'text-teal-800 dark:text-teal-300 font-medium' : 'text-gray-500 dark:text-gray-400'}`}>Anual</span>
                    </div>
                    {/* Opção de pagamento à vista */}
                    {isAnnual && (
                      <div className="flex items-center justify-center">
                        <input
                          type="checkbox"
                          id="annual-upfront"
                          checked={isAnnualUpfront}
                          onChange={() => setIsAnnualUpfront(!isAnnualUpfront)}
                          className="mr-2 accent-teal-600"
                        />
                        <label htmlFor="annual-upfront" className="text-sm text-teal-800 dark:text-teal-300 font-medium cursor-pointer">
                          Pagar anual à vista (desconto extra de 10%)
                        </label>
                      </div>
                    )}
                    {/* Seletor de usuários */}
                    <div>
                      <h5 className="font-semibold text-teal-800 dark:text-teal-300 text-center mb-3">
                        Quantos usuários você precisa?
                      </h5>
                      <div className="grid grid-cols-3 gap-2 mb-3">
                        {userOptions.map((option) => {
                          const discount = getDiscountByUserCount(option);
                          return (
                            <button
                              key={option}
                              onClick={() => setUserCount(option)}
                              className={`p-2 rounded-lg border-2 transition-all text-center relative ${userCount === option ? 'border-teal-500 bg-teal-100 dark:bg-teal-800/30 text-teal-700 dark:text-teal-400' : 'border-teal-200 dark:border-teal-700 hover:border-teal-300 dark:hover:border-teal-600'}`}
                            >
                              {discount > 0 && (
                                <div className="absolute -top-1 -right-1 bg-green-500 text-white text-xs font-bold px-1 py-0.5 rounded-full">
                                  -{discount}%
                                </div>
                              )}
                              {option === 20 && (
                                <div className="absolute -top-1 -left-1 bg-teal-700 text-white text-xs font-bold px-1 py-0.5 rounded-full shadow-lg">
                                  RECOMENDADO
                                </div>
                              )}
                              <div className="text-sm font-bold text-teal-800 dark:text-teal-300">
                                {option}
                              </div>
                              <div className="text-xs text-teal-600 dark:text-teal-400">
                                {option === 1 ? 'usuário' : 'usuários'}
                              </div>
                            </button>
                          );
                        })}
                      </div>
                      <div className="text-center">
                        <div className="flex justify-center">
                          <div className="relative">
                            <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-teal-400" />
                            <input
                              type="number"
                              min="1"
                              max="1000"
                              value={userCount}
                              onChange={(e) => setUserCount(Math.max(1, parseInt(e.target.value) || 1))}
                              className="pl-8 pr-3 py-2 border border-teal-300 dark:border-teal-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-teal-800 dark:text-teal-300 bg-white dark:bg-teal-900/30 w-24 text-center text-sm"
                              placeholder="1"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    {/* Campo de cupom */}
                    <div>
                      <div className="flex gap-2">
                        <input
                          type="text"
                          value={couponCode}
                          onChange={e => setCouponCode(e.target.value.toUpperCase())}
                          placeholder="Código do cupom"
                          className="flex-1 px-3 py-2 border border-teal-300 dark:border-teal-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 text-teal-800 dark:text-teal-300 bg-white dark:bg-teal-900/30 text-sm"
                          disabled={isValidatingCoupon}
                        />
                        <button
                          onClick={async () => {
                            setIsValidatingCoupon(true);
                            setCouponError('');
                            setCoupon(null);
                            const result = await validateCoupon(couponCode);
                            setIsValidatingCoupon(false);
                            if (result.valid) {
                              setCoupon(result);
                            } else {
                              setCouponError(result.message || 'Cupom inválido ou expirado');
                            }
                          }}
                          className="px-3 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors disabled:opacity-50 text-sm"
                          disabled={isValidatingCoupon || !couponCode}
                        >
                          {isValidatingCoupon ? '...' : 'Aplicar'}
                        </button>
                        {coupon && (
                          <button
                            onClick={() => { setCoupon(null); setCouponCode(''); }}
                            className="px-2 py-2 bg-teal-200 text-teal-700 rounded-lg hover:bg-teal-300 transition-colors text-sm"
                          >
                            ✕
                          </button>
                        )}
                      </div>
                      {couponError && <div className="text-red-600 mt-1 text-xs">{couponError}</div>}
                      {coupon && coupon.coupon && (
                        <div className="text-green-600 mt-1 text-xs">
                          Cupom aplicado: <b>{coupon.coupon.code}</b> {coupon.coupon.type === 'PERCENT' ? `(-${coupon.coupon.value}%)` : `(-R$ ${coupon.coupon.value})`}
                          <br />
                          <span className="text-green-500">Desconto aplicado por {isAnnual ? '12 meses' : '1 mês'}</span>
                        </div>
                      )}
                    </div>
                    {/* Resumo do preço */}
                    <div className="bg-teal-100 dark:bg-teal-900/30 rounded-lg p-4">
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between items-center">
                          <span className="text-teal-700 dark:text-teal-300">Usuários:</span>
                          <span className="font-medium text-teal-800 dark:text-teal-300">{userCount}</span>
                        </div>
                        {pricing.discount > 0 && (
                          <div className="flex justify-between items-center">
                            <span className="text-teal-700 dark:text-teal-300">Desconto por quantidade:</span>
                            <span className="font-medium text-green-600 dark:text-green-400">-{pricing.discount}%</span>
                          </div>
                        )}
                        {isAnnual && (
                          <div className="flex justify-between items-center">
                            <span className="text-teal-700 dark:text-teal-300">Desconto anual:</span>
                            <span className="font-medium text-green-600 dark:text-green-400">-{pricing.annualDiscount}%</span>
                          </div>
                        )}
                        {isAnnual && isAnnualUpfront && (
                          <div className="flex justify-between items-center">
                            <span className="text-teal-700 dark:text-teal-300">Desconto extra à vista:</span>
                            <span className="font-medium text-green-600 dark:text-green-400">-{pricing.upfrontDiscount}%</span>
                          </div>
                        )}
                        <div className="border-t border-teal-200 dark:border-teal-700 pt-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium text-teal-800 dark:text-teal-300">
                              Valor {isAnnual ? (isAnnualUpfront ? 'anual à vista' : 'anual') : 'mensal'}:
                            </span>
                            <span className="text-lg font-bold text-teal-600 dark:text-teal-400">
                              R$ {finalPriceWithCoupon.toFixed(2).replace('.', ',')}
                            </span>
                          </div>
                          {isAnnual && (
                            <div className="text-center text-xs text-teal-600 dark:text-teal-400">
                              R$ {(finalPriceWithCoupon / 12).toFixed(2).replace('.', ',')} por mês
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    {/* Benefícios extras */}
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                      <div className="flex items-center"><CheckCircle className="w-4 h-4 text-gray-400 dark:text-gray-400 mr-2 flex-shrink-0" /><span className="text-gray-700 dark:text-gray-300">Agendamento Inteligente</span></div>
                      <div className="flex items-center"><CheckCircle className="w-4 h-4 text-gray-400 dark:text-gray-400 mr-2 flex-shrink-0" /><span className="text-gray-700 dark:text-gray-300">Gestão de Pacientes</span></div>
                      <div className="flex items-center"><CheckCircle className="w-4 h-4 text-gray-400 dark:text-gray-400 mr-2 flex-shrink-0" /><span className="text-gray-700 dark:text-gray-300">Administração Personalizada</span></div>
                      <div className="flex items-center"><CheckCircle className="w-4 h-4 text-gray-400 dark:text-gray-400 mr-2 flex-shrink-0" /><span className="text-gray-700 dark:text-gray-300">Relatórios e Dashboards</span></div>
                      <div className="flex items-center"><CheckCircle className="w-4 h-4 text-gray-400 dark:text-gray-400 mr-2 flex-shrink-0" /><span className="text-gray-700 dark:text-gray-300">Chat Interativo</span></div>
                      <div className="flex items-center"><CheckCircle className="w-4 h-4 text-gray-400 dark:text-gray-400 mr-2 flex-shrink-0" /><span className="text-gray-700 dark:text-gray-300">Personalização do Sistema</span></div>
                      <div className="flex items-center"><CheckCircle className="w-4 h-4 text-gray-400 dark:text-gray-400 mr-2 flex-shrink-0" /><span className="text-gray-700 dark:text-gray-300">Acesso para Pacientes</span></div>
                      <div className="flex items-center"><CheckCircle className="w-4 h-4 text-gray-400 dark:text-gray-400 mr-2 flex-shrink-0" /><span className="text-gray-700 dark:text-gray-300">Suporte e Segurança</span></div>
                      <div className="flex items-center"><CheckCircle className="w-4 h-4 text-gray-400 dark:text-gray-400 mr-2 flex-shrink-0" /><span className="text-gray-700 dark:text-gray-300">Atualizações Constantes</span></div>
                    </div>
                  </div>
                </div>
              )}
              {selectedPlan === 'gratuito' && (
                <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/20 border border-yellow-300 dark:border-yellow-700 rounded-xl p-6">
                  {/* Plano Gratuito - conteúdo completo */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-800 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-yellow-800 dark:text-yellow-300">
                          Plano Gratuito
                        </h4>
                        <p className="text-yellow-600 dark:text-yellow-400">
                          R$ 0,00 por 7 dias • Até 5 usuários
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={handleSubmit}
                      disabled={isLoading}
                      className="inline-flex items-center px-6 py-3 border border-yellow-300 dark:border-yellow-700 rounded-lg shadow-sm text-base font-medium text-yellow-800 dark:text-yellow-300 bg-white dark:bg-yellow-900 hover:bg-yellow-50 dark:hover:bg-yellow-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600 mr-2"></div>
                          Criando conta...
                        </>
                      ) : (
                        <>
                          Experimentar Grátis
                          <Mail className="w-5 h-5 ml-2" />
                        </>
                      )}
                    </button>
                  </div>
                  <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                    <div className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mr-2 flex-shrink-0" />
                      <span className="text-yellow-800 dark:text-yellow-300">Agendamento</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mr-2 flex-shrink-0" />
                      <span className="text-yellow-800 dark:text-yellow-300">Pacientes</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mr-2 flex-shrink-0" />
                      <span className="text-yellow-800 dark:text-yellow-300">Calendário</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mr-2 flex-shrink-0" />
                      <span className="text-yellow-800 dark:text-yellow-300">Até 5 usuários</span>
                    </div>
                  </div>
                </div>
              )}
              {/* Informações Adicionais */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                <h5 className="font-semibold text-blue-900 dark:text-blue-300 mb-3">Informações importantes:</h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700 dark:text-blue-300">
                  <div>
                    <p><strong>Plano Gratuito:</strong> 7 dias para experimentar todas as funcionalidades, com até 5 usuários. Após o período, você pode escolher o plano ideal.</p>
                  </div>
                  <div>
                    <p><strong>Plano Pago:</strong> Acesso imediato a todos os módulos. Descontos automáticos para equipes maiores.</p>
                  </div>
                </div>
              </div>
              <div className="flex justify-between pt-8">
                <button
                  type="button"
                  onClick={handlePrevStep}
                  className="inline-flex items-center px-6 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-xl shadow-sm text-sm font-semibold text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 hover:shadow-md"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Anterior
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function SubscriptionSignupPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SubscriptionSignupPageContent />
    </Suspense>
  );
}