// src/hooks/useCep.js
import { useState } from 'react';
import { cepService } from '@/app/modules/common/services/cepService';

/**
 * Hook para busca de CEP e preenchimento automático de endereço
 */
export function useCep() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Busca endereço a partir do CEP e atualiza o formulário
   * @param {string} cep - CEP a ser consultado
   * @param {Function} setFormData - Função para atualizar o estado do formulário
   * @param {Object} fieldMapping - Mapeamento dos campos do endereço para os campos do formulário
   * @returns {Promise<Object|null>} - Dados do endereço ou null em caso de erro
   */
  const searchAddressByCep = async (cep, setFormData, fieldMapping = {}) => {
    // Limpa erro anterior
    setError(null);

    // Verifica se o CEP tem pelo menos 8 dígitos (considerando possível máscara)
    const cleanCep = cep.replace(/\D/g, '');
    if (cleanCep.length !== 8) {
      return null;
    }

    setIsLoading(true);

    try {
      const addressData = await cepService.searchByCep(cep);
      console.log('Dados do endereço recebidos:', addressData);

      // Mapeamento padrão se não for fornecido
      const defaultMapping = {
        logradouro: 'address',
        bairro: 'neighborhood', 
        localidade: 'city',
        uf: 'state',
        cep: 'postalCode'
      };

      const mapping = { ...defaultMapping, ...fieldMapping };
      
      // Para cada campo retornado pela API, atualiza o formulário
      Object.keys(addressData).forEach(apiField => {
        const formField = mapping[apiField];
        if (formField && addressData[apiField]) {
          if (formField.includes('.')) {
            const [parent, child] = formField.split('.');
            setFormData(prev => ({
              ...prev,
              [parent]: {
                ...prev[parent],
                [child]: addressData[apiField]
              }
            }));
          } else {
            setFormData(prev => ({
              ...prev,
              [formField]: addressData[apiField]
            }));
          }
        }
      });

      return addressData;
    } catch (err) {
      setError(err.message);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    searchAddressByCep,
    isLoading,
    error
  };
}
