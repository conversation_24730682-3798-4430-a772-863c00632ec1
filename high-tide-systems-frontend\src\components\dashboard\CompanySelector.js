'use client';

import { Building2, ChevronDown } from 'lucide-react';
import { useCompanySelection } from '@/contexts/CompanySelectionContext';
import { useAuth } from '@/contexts/AuthContext';

export default function CompanySelector({ activeModule }) {
  const { isSystemAdmin } = useAuth();
  const {
    selectedCompanyId,
    companies,
    isLoading,
    selectedCompany,
    handleCompanyChange,
    canSelectCompany
  } = useCompanySelection();

  // Só renderizar para SYSTEM_ADMIN
  if (!canSelectCompany) {
    return null;
  }

  return (
    <div className="px-5 pb-4 border-b border-gray-100 dark:border-gray-700">
      <div className="space-y-2">
        <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
          Empresa Selecionada
        </label>
        
        <div className="relative">
          <select
            value={selectedCompanyId || ''}
            onChange={(e) => handleCompanyChange(e.target.value || null)}
            disabled={isLoading}
            className={`
              w-full px-3 py-2 pr-8 text-sm rounded-lg transition-all duration-200
              border border-gray-200 dark:border-gray-600
              bg-white dark:bg-gray-700 
              text-gray-900 dark:text-gray-100
              focus:outline-none focus:ring-2 focus:ring-module-${activeModule}-border dark:focus:ring-module-${activeModule}-border-dark
              focus:border-transparent
              disabled:bg-gray-100 dark:disabled:bg-gray-600 disabled:cursor-not-allowed
              appearance-none cursor-pointer
            `}
          >
            <option value="">
              {isLoading ? 'Carregando empresas...' : 'Sem Empresa'}
            </option>
            {companies.map((company) => (
              <option key={company.id} value={company.id}>
                {company.name}
              </option>
            ))}
          </select>
          
          {/* Ícone customizado do select */}
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-400" />
            )}
          </div>
        </div>

        {/* Indicador visual da empresa selecionada */}
        {selectedCompany && (
          <div className={`
            flex items-center gap-2 px-3 py-2 rounded-md text-xs
            bg-module-${activeModule}-bg/10 dark:bg-module-${activeModule}-bg-dark/10
            border border-module-${activeModule}-border/20 dark:border-module-${activeModule}-border-dark/20
          `}>
            <Building2 className={`
              h-3 w-3 
              text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark
            `} />
            <span className={`
              font-medium 
              text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark
            `}>
              {selectedCompany.name}
            </span>
          </div>
        )}

        {/* Indicador quando está operando sem empresa */}
        {!selectedCompanyId && !isLoading && (
          <div className="flex items-center gap-2 px-3 py-2 rounded-md text-xs bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800">
            <Building2 className="h-3 w-3 text-amber-600 dark:text-amber-400" />
            <span className="font-medium text-amber-700 dark:text-amber-300">
              Modo Global
            </span>
          </div>
        )}
      </div>
    </div>
  );
}