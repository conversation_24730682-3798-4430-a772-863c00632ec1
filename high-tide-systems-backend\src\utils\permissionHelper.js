// src/utils/permissionHelper.js

/**
 * Configuração das permissões do sistema
 * Esta é uma cópia da configuração do frontend para garantir consistência
 */
const PERMISSIONS_CONFIG = {
  // Módulo de Administração
  ADMIN: {
    name: 'Administração',
    permissions: [
      { id: 'admin.users.view' },
      { id: 'admin.users.create' },
      { id: 'admin.users.edit' },
      { id: 'admin.users.delete' },
      { id: 'admin.permissions.manage' },
      { id: 'admin.logs.view' },
      { id: 'admin.config.edit' },
      { id: 'admin.professions.view' },
      { id: 'admin.professions.create' },
      { id: 'admin.professions.edit' },
      { id: 'admin.professions.delete' },
      { id: 'admin.profession-groups.view' },
      { id: 'admin.profession-groups.create' },
      { id: 'admin.profession-groups.edit' },
      { id: 'admin.profession-groups.delete' },
      { id: 'admin.subscription.view' },
      { id: 'admin.subscription.manage' },
      { id: 'admin.notifications.view' },
      { id: 'admin.notifications.manage' },
      { id: 'admin.notifications.permissions.manage' },
      // Permissões específicas de notificação
      { id: 'notifications.new_registration.receive' },
      { id: 'notifications.appointment_coming.receive' },
      { id: 'notifications.new_access.receive' },
      { id: 'notifications.new_backup.receive' },
      { id: 'notifications.new_export.receive' },
      { id: 'notifications.system_alert.receive' }
    ]
  },

  // // Módulo de RH - OCULTO TEMPORARIAMENTE
  // RH: {
  //   name: 'Recursos Humanos',
  //   permissions: []
  // },

  // // Módulo Financeiro - OCULTO TEMPORARIAMENTE
  // FINANCIAL: {
  //   name: 'Financeiro',
  //   permissions: []
  // },

  // Módulo de Agendamento
  SCHEDULING: {
    name: 'Agendamento',
    permissions: [
      { id: 'scheduling.calendar.view' },
      { id: 'scheduling.calendar.create' },
      { id: 'scheduling.calendar.edit' },
      { id: 'scheduling.calendar.delete' },
      { id: 'scheduling.working-hours.view' },
      { id: 'scheduling.working-hours.manage' },
      { id: 'scheduling.service-types.view' },
      { id: 'scheduling.service-types.create' },
      { id: 'scheduling.service-types.edit' },
      { id: 'scheduling.service-types.delete' },
      { id: 'scheduling.locations.view' },
      { id: 'scheduling.locations.create' },
      { id: 'scheduling.locations.edit' },
      { id: 'scheduling.locations.delete' },
      { id: 'scheduling.occupancy.view' },
      { id: 'scheduling.appointments-report.view' },
      { id: 'scheduling.appointments-report.export' },
      { id: 'scheduling.appointments-report.edit' },
      { id: 'scheduling.appointments-report.cancel' },
      { id: 'scheduling.appointments-report.delete' },
      { id: 'scheduling.appointments-dashboard.view' },
      { id: 'scheduling.rooms.manage' },
      { id: 'scheduling.resources.manage' }
    ]
  },

  // Módulo de Pessoas
  PEOPLE: {
    name: 'Pessoas',
    permissions: [
      { id: 'people.clients.view' },
      { id: 'people.clients.create' },
      { id: 'people.clients.edit' },
      { id: 'people.clients.delete' },
      { id: 'people.persons.view' },
      { id: 'people.persons.create' },
      { id: 'people.persons.edit' },
      { id: 'people.persons.delete' },
      { id: 'people.insurances.view' },
      { id: 'people.insurances.create' },
      { id: 'people.insurances.edit' },
      { id: 'people.insurances.delete' },
      { id: 'people.insurance-limits.view' },
      { id: 'people.insurance-limits.create' },
      { id: 'people.insurance-limits.edit' },
      { id: 'people.insurance-limits.delete' },
      { id: 'people.documents.view' },
      { id: 'people.documents.create' },
      { id: 'people.documents.edit' },
      { id: 'people.documents.delete' },
      { id: 'people.documents.download' },
      { id: 'people.documents.categories.view' },
      { id: 'people.documents.categories.create' },
      { id: 'people.documents.categories.edit' },
      { id: 'people.documents.categories.delete' }
    ]
  },

  // // Módulo ABA+ - OCULTO TEMPORARIAMENTE
  // ABAPLUS: {
  //   name: 'ABA+',
  //   permissions: []
  // },

  // Módulo Básico
  BASIC: {
    name: 'Básico',
    permissions: [
      { id: 'basic.profile.view' },
      { id: 'basic.profile.edit' },
      // Permissões básicas de notificação
      { id: 'notifications.new_registration.receive' },
      { id: 'notifications.appointment_coming.receive' },
      { id: 'notifications.new_access.receive' },
      { id: 'notifications.new_backup.receive' },
      { id: 'notifications.new_export.receive' },
      { id: 'notifications.system_alert.receive' }
    ]
  }
};

/**
 * Obtém todas as permissões disponíveis no sistema
 * @returns {Array} Array com os IDs de todas as permissões
 */
const getAllPermissions = () => {
  const allPermissions = [];

  Object.keys(PERMISSIONS_CONFIG).forEach(moduleId => {
    const module = PERMISSIONS_CONFIG[moduleId];
    module.permissions.forEach(permission => {
      allPermissions.push(permission.id);
    });
  });

  return allPermissions;
};

/**
 * Obtém todos os módulos disponíveis no sistema
 * @returns {Array} Array com os IDs de todos os módulos
 */
const getAllModules = () => {
  return Object.keys(PERMISSIONS_CONFIG);
};

/**
 * Checa se o usuário tem permissão de visualização ou edição para um documento específico
 * @param {object} params - { prisma, documentId, userId, clientId, checkEdit }
 * @returns {Promise<boolean>}
 */
async function hasDocumentPermission({ prisma, documentId, userId, clientId, checkEdit = false }) {
  const where = {
    documentId,
    ...(userId ? { userId } : {}),
    ...(clientId ? { clientId } : {}),
    ...(checkEdit ? { canEdit: true } : { canView: true }),
  };
  const perm = await prisma.documentPermission.findFirst({ where });
  return !!perm;
}

/**
 * Checa se o usuário tem permissão de visualização ou edição para uma categoria de documento
 * @param {object} params - { prisma, categoryDocumentId, userId, clientId, checkEdit }
 * @returns {Promise<boolean>}
 */
async function hasCategoryDocumentPermission({ prisma, categoryDocumentId, userId, clientId, checkEdit = false }) {
  const where = {
    categoryDocumentId,
    ...(userId ? { userId } : {}),
    ...(clientId ? { clientId } : {}),
    ...(checkEdit ? { canEdit: true } : { canView: true }),
  };
  const perm = await prisma.categoryDocumentPermission.findFirst({ where });
  return !!perm;
}

module.exports = {
  getAllPermissions,
  getAllModules,
  PERMISSIONS_CONFIG,
  hasDocumentPermission,
  hasCategoryDocumentPermission,
};
