"use client";

import React, { useState, useEffect } from "react";
import {
  User,
  Heart,
  Mail,
  Phone,
  FileText
} from "lucide-react";
import { contactsService } from "@/app/modules/people/services/contactsService";
import MaskedInput from "@/components/common/MaskedInput";
import { ModuleInput, ModuleSelect, ModuleTextarea, ModuleFormGroup } from "@/components/ui";

const ContactFormModal = ({ isOpen, onClose, contact, personId, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: "",
    relationship: "",
    email: "",
    phone: "",
    notes: ""
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (contact) {
      setFormData({
        name: contact.name || "",
        relationship: contact.relationship || "",
        email: contact.email || "",
        phone: contact.phone || "",
        notes: contact.notes || ""
      });
    } else {
      setFormData({
        name: "",
        relationship: "",
        email: "",
        phone: "",
        notes: ""
      });
    }
  }, [contact]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nome é obrigatório";
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear the error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data - remove formatting from phone number
      const cleanedData = {
        ...formData,
        phone: formData.phone ? formData.phone.replace(/\D/g, '') : ''
      };

      if (contact) {
        // Update existing contact
        await contactsService.updateContact(contact.id, cleanedData);
      } else {
        // Create new contact
        await contactsService.createContact({
          ...cleanedData,
          personId
        });
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving contact:", error);

      // Handle API validation errors
      if (error.response?.data?.errors) {
        const apiErrors = {};
        error.response.data.errors.forEach(err => {
          apiErrors[err.path] = err.msg;
        });
        setErrors(apiErrors);
      } else {
        setErrors({
          submit: "Ocorreu um erro ao salvar o contato. Por favor, tente novamente."
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[13000] flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="fixed left-[50%] top-[50%] z-[13050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-orange-300 dark:border-orange-600 bg-background shadow-lg duration-200 rounded-xl max-w-2xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="pb-4 border-b-2 border-orange-400 dark:border-orange-500 flex-shrink-0 px-6 pt-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg text-white">
              <User className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-orange-800 dark:text-white border-l-4 border-orange-400 dark:border-orange-500 pl-3">
                {contact ? 'Editar Contato' : 'Adicionar Contato'}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                {contact ? 'Modifique as informações do contato' : 'Adicione um novo contato'}
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(90vh-200px)]">
          <div className="flex-1 overflow-y-auto p-6">
            <form id="contact-form" onSubmit={handleSubmit} className="space-y-6">
              <div className="mb-8">
                <div className="border-b-2 border-orange-400 dark:border-orange-500 pb-3 mb-4">
                  <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-orange-500 pl-3">
                    Informações do Contato
                  </h4>
                  <p className="text-xs text-neutral-600 dark:text-gray-300 pl-3">
                    Dados de contato da pessoa:
                  </p>
                </div>
              </div>

              <ModuleFormGroup
                moduleColor="people"
                label={<span>Nome do Contato <span className="text-red-500 ml-1">*</span></span>}
                htmlFor="name"
                icon={<User size={16} />}
                error={errors.name}
                errorMessage={errors.name}
              >
                <ModuleInput
                  moduleColor="people"
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Digite o nome completo"
                  disabled={isSubmitting}
                  error={!!errors.name}
                />
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Relacionamento"
                htmlFor="relationship"
                icon={<Heart size={16} />}
              >
                <ModuleInput
                  moduleColor="people"
                  type="text"
                  id="relationship"
                  name="relationship"
                  value={formData.relationship}
                  onChange={handleChange}
                  placeholder="Ex: Pai, Mãe, Irmão, Amigo..."
                  disabled={isSubmitting}
                />
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Email"
                htmlFor="email"
                icon={<Mail size={16} />}
                error={errors.email}
                errorMessage={errors.email}
              >
                <ModuleInput
                  moduleColor="people"
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  disabled={isSubmitting}
                  error={!!errors.email}
                />
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Telefone"
                htmlFor="phone"
                icon={<Phone size={16} />}
              >
                <MaskedInput
                  type="phone"
                  value={formData.phone}
                  onChange={(e) =>
                    handleChange({
                      target: { name: "phone", value: e.target.value },
                    })
                  }
                  placeholder="(00) 00000-0000"
                  className="w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  disabled={isSubmitting}
                />
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Observações"
                htmlFor="notes"
                icon={<FileText size={16} />}
              >
                <ModuleTextarea
                  moduleColor="people"
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  placeholder="Informações adicionais sobre este contato..."
                  rows={3}
                  disabled={isSubmitting}
                />
              </ModuleFormGroup>

              {errors.submit && (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2">
                  <User size={16} />
                  <span>{errors.submit}</span>
                </div>
              )}
            </form>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 border-t-2 border-orange-400 dark:border-orange-500 pt-4 flex-shrink-0 px-6 pb-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              Cancelar
            </button>
            <button
              type="submit"
              form="contact-form"
              className="px-4 py-2 bg-orange-500 dark:bg-orange-600 text-white rounded-lg hover:bg-orange-600 dark:hover:bg-orange-700 transition-colors flex items-center gap-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <User size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  <User size={16} />
                  <span>{contact ? "Atualizar" : "Salvar"}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactFormModal;