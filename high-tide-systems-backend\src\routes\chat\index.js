// src/routes/chat/index.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const upload = require('../../middlewares/upload');
const { DocumentController } = require('../../controllers/documentController');
const conversationRoutes = require('./conversationRoutes');
const messageRoutes = require('./messageRoutes');

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticate);

// Rota de status para verificar autenticação
router.get('/status', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Chat service is running',
    user: {
      id: req.user.id,
      name: req.user.fullName,
      role: req.user.role
    }
  });
});

// Rotas de conversas
router.use('/conversations', conversationRoutes);

// Rotas de mensagens
router.use('/messages', messageRoutes);

// Rota para download de documentos do chat
router.get('/documents/:id/download', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    const isClient = req.user.isClient || req.user.role === 'CLIENT';
    const path = require('path');
    const fs = require('fs').promises;
    const prisma = require('../../utils/prisma');
    
    const UPLOAD_PATH = process.env.NODE_ENV === "production"
      ? require('path').resolve("/data/uploads")
      : require('path').resolve("/usr/src/app/uploads");

    // Buscar o documento
    const document = await prisma.document.findUnique({
      where: { id }
    });

    if (!document) {
      return res.status(404).json({ message: "Documento não encontrado" });
    }

    // Verificar se o usuário tem acesso ao documento
    let hasAccess = false;
    if (isClient) {
      hasAccess = document.clientId === userId;
    } else {
      hasAccess = document.userId === userId || document.createdById === userId;
    }

    if (!hasAccess) {
      return res.status(403).json({ message: "Sem permissão para acessar este documento" });
    }

    // Construir o caminho completo do arquivo
    const fullPath = path.join(UPLOAD_PATH, document.path);

    // Verificar se o arquivo existe
    try {
      await fs.access(fullPath);
    } catch (error) {
      return res.status(404).json({ message: "Arquivo não encontrado" });
    }

    // Definir headers apropriados
    res.set({
      'Content-Type': document.mimeType || 'application/octet-stream',
      'Content-Disposition': `inline; filename="${document.filename}"`,
      'Cache-Control': 'private, max-age=3600'
    });

    // Servir o arquivo
    res.sendFile(fullPath);

  } catch (error) {
    console.error('Erro ao servir documento do chat:', error);
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

// Endpoint de upload específico para chat (sem verificação de permissão)
router.post('/upload', upload.array('documents', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ message: "Nenhum arquivo enviado" });
    }

    const userId = req.user.id;
    const isClient = req.user.isClient || req.user.role === 'CLIENT';
    const path = require('path');
    const prisma = require('../../utils/prisma');
    
    const UPLOAD_PATH = process.env.NODE_ENV === "production"
      ? require('path').resolve("/data/uploads")
      : require('path').resolve("/usr/src/app/uploads");

    // Criar documentos para chat
    const documents = await prisma.$transaction(
      req.files.map((file) => {
        const mimeType = file.mimetype || "application/octet-stream";
        const fileSize = file.size || 0;

        const createData = {
          filename: file.originalname,
          path: path.relative(UPLOAD_PATH, file.path),
          type: "OUTROS",
          mimeType: mimeType,
          size: fileSize,
          ownerType: isClient ? "CLIENT" : "USER"
        };

        // Conectar com User ou Client dependendo do tipo
        if (isClient) {
          createData.clientId = userId;
        } else {
          createData.userId = userId;
          createData.createdById = userId;
        }

        return prisma.document.create({
          data: createData,
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
              },
            },
            Client: {
              select: {
                id: true,
                fullName: true,
                login: true
              },
            },
            createdBy: {
              select: {
                id: true,
                fullName: true,
              },
            },
          },
        });
      })
    );

    res.status(201).json(documents);
  } catch (error) {
    console.error('Erro no upload do chat:', error);
    
    // Remover arquivos em caso de erro
    if (req.files) {
      const fs = require('fs').promises;
      await Promise.all(
        req.files.map((file) =>
          fs.unlink(file.path).catch((err) =>
            console.error(`Erro ao remover arquivo ${file.path}:`, err)
          )
        )
      );
    }
    
    res.status(500).json({ message: 'Erro interno do servidor' });
  }
});

module.exports = router;
