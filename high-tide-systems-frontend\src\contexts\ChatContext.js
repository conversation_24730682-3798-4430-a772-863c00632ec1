'use client';

import React, { createContext, useContext, useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useAuth } from './AuthContext';
import io from 'socket.io-client';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

// Cache para evitar requisições duplicadas
const requestCache = {
  conversations: { data: null, timestamp: 0 },
  unreadCount: { data: null, timestamp: 0 },
  messages: {}, // Cache para mensagens por conversa
  tokenCheck: { timestamp: 0, valid: false } // Cache para verificação de token
};

// Tempo de expiração do cache em milissegundos
const CACHE_EXPIRATION = 30000; // 30 segundos para dados normais
const TOKEN_CHECK_EXPIRATION = 60000; // 1 minuto para verificação de token

// Função de debounce para limitar a frequência de chamadas
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

const ChatContext = createContext(null);

export const ChatProvider = ({ children }) => {
  const { user, logout } = useAuth();
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [conversations, setConversations] = useState([]);
  const [activeConversation, setActiveConversation] = useState(null);
  const [messages, setMessages] = useState({});
  const [unreadCount, setUnreadCount] = useState(0);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Obter o token atual do localStorage - memoizado para evitar chamadas desnecessárias
  const getCurrentToken = useCallback(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('token');
    }
    return null;
  }, []);

  // Função para lidar com erros de autenticação
  const handleAuthError = useCallback(() => {
    console.error('Erro de autenticação detectado. Redirecionando para login...');
    // Limpar dados locais
    setConversations([]);
    setMessages({});
    setActiveConversation(null);
    setUnreadCount(0);
    setIsPanelOpen(false);
    setIsModalOpen(false);

    // Desconectar socket se existir
    if (socket) {
      try {
        socket.disconnect();
      } catch (error) {
        console.error('Erro ao desconectar socket:', error);
      }
      setIsConnected(false);
    }

    // Limpar cache
    requestCache.conversations.data = null;
    requestCache.unreadCount.data = null;
    requestCache.messages = {}; // Limpar cache de mensagens

    // Redirecionar para login
    if (logout) {
      logout();
    }
  }, [socket, logout]);

  // Usar referências para controlar estados que não devem causar re-renderizações
  const socketInitializedRef = useRef(false);
  const isLoadingRef = useRef(false);
  const lastInitAttemptRef = useRef(0);
  const reconnectAttemptsRef = useRef(0);

  // Redefinir a referência quando o usuário mudar
  useEffect(() => {
    // Se o usuário for null (logout), desconectar o socket
    if (!user && socket) {
      try {
        socket.disconnect();
        setIsConnected(false);
        setSocket(null);
      } catch (error) {
        console.error('Erro ao desconectar socket após logout:', error);
      }
    }

    // Resetar estado quando o usuário muda
    socketInitializedRef.current = false;
    lastInitAttemptRef.current = 0;
    reconnectAttemptsRef.current = 0;
  }, [user, socket]);

  // Limpar estado do chat quando o usuário muda
  useEffect(() => {
    // Se o usuário mudou (novo login), limpar todo o estado do chat
    if (user) {
      console.log('Usuário logado, limpando estado do chat para novo usuário:', user.fullName);
      // Limpar conversas e mensagens para o novo usuário
      setConversations([]);
      setMessages({});
      setActiveConversation(null);
      setUnreadCount(0);
      setIsPanelOpen(false);
      setIsModalOpen(false);
      
      // Limpar cache
      requestCache.conversations.data = null;
      requestCache.unreadCount.data = null;
      requestCache.messages = {};
      requestCache.tokenCheck.timestamp = 0;
      requestCache.tokenCheck.valid = false;
      
      console.log('Cache limpo para novo usuário');
      
      // Limpar dados residuais do localStorage relacionados ao chat
      if (typeof window !== 'undefined') {
        // Limpar qualquer cache específico do chat que possa estar no localStorage
        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && (key.startsWith('chat_') || key.startsWith('conversation_') || key.startsWith('message_'))) {
            keysToRemove.push(key);
          }
        }
        keysToRemove.forEach(key => localStorage.removeItem(key));
        console.log('Dados residuais do chat removidos do localStorage');
      }
    }
  }, [user?.id]); // Executar quando o ID do usuário mudar

  // Inicializar conexão WebSocket
  useEffect(() => {
    // Se não há usuário, não inicializar
    if (!user) {
      console.log('Usuário não logado, não inicializando WebSocket');
      return;
    }

    // Clientes agora podem usar o chat
    console.log('Inicializando chat para usuário:', user.role || 'USER');

    // Se já existe um socket conectado, não fazer nada
    if (socket && isConnected) {
      console.log('WebSocket já está conectado, não é necessário reinicializar');
      return;
    }

    // Se já está inicializado e a última tentativa foi recente, não tentar novamente
    if (socketInitializedRef.current && Date.now() - lastInitAttemptRef.current < 60000) {
      console.log('Socket já inicializado recentemente, aguardando...');
      return;
    }

    // Marcar como inicializado imediatamente para evitar múltiplas tentativas
    socketInitializedRef.current = true;
    lastInitAttemptRef.current = Date.now();

    // Função assíncrona para inicializar o WebSocket
    const initializeWebSocket = async () => {
      // Verificar se o usuário está logado antes de inicializar o WebSocket
      if (!user) return;

      // Clientes agora podem usar o chat
      console.log('Inicializando WebSocket para:', user.role || 'USER');

      const currentToken = getCurrentToken();
      if (!currentToken) return;

      // Evitar reconexões desnecessárias - verificação adicional
      if (socket) {
        if (isConnected) {
          console.log('WebSocket já conectado, ignorando inicialização');
          return socket; // Retornar o socket existente
        } else {
          // Se o socket existe mas não está conectado, tentar reconectar em vez de criar um novo
          console.log('Socket existe mas não está conectado, tentando reconectar');
          try {
            // Tentar reconectar o socket existente em vez de criar um novo
            if (!socket.connected && socket.connect) {
              socket.connect();
              console.log('Tentativa de reconexão iniciada');
              return socket;
            }
          } catch (error) {
            console.error('Erro ao reconectar socket existente:', error);
            // Só desconectar se a reconexão falhar
            try {
              socket.disconnect();
            } catch (disconnectError) {
              console.error('Erro ao desconectar socket após falha na reconexão:', disconnectError);
            }
          }
        }
      }

      // Marcar como inicializado para evitar múltiplas inicializações
      socketInitializedRef.current = true;

      // Limitar a frequência de reconexões
      const maxReconnectAttempts = 5;
      const reconnectDelay = 3000; // 3 segundos

      console.log('Inicializando WebSocket...');

      try {
        // Verificar se o token é válido antes de inicializar o WebSocket
        // Usar uma verificação mais simples para evitar múltiplas requisições
        // Assumir que o token é válido se o usuário está logado
        console.log('Token válido, inicializando WebSocket para o usuário:', user.fullName);

        console.log('Inicializando WebSocket com autenticação');

        const socketInstance = io(API_URL, {
          path: '/socket.io',
          auth: { token: currentToken },
          transports: ['websocket', 'polling'],
          reconnectionAttempts: maxReconnectAttempts,
          reconnectionDelay: reconnectDelay,
          timeout: 10000, // 10 segundos
          autoConnect: true,
          reconnection: true
        });

        socketInstance.on('connect', () => {
          console.log('WebSocket connected');
          setIsConnected(true);
          reconnectAttemptsRef.current = 0;

          // Disparar evento personalizado para notificar componentes sobre a conexão
          window.dispatchEvent(new CustomEvent('chat:websocket:update', {
            detail: { type: 'connection', status: 'connected', timestamp: Date.now() }
          }));
        });

        socketInstance.on('disconnect', () => {
          console.log('WebSocket disconnected');
          setIsConnected(false);

          // Disparar evento personalizado para notificar componentes sobre a desconexão
          window.dispatchEvent(new CustomEvent('chat:websocket:update', {
            detail: { type: 'connection', status: 'disconnected', timestamp: Date.now() }
          }));
        });

        socketInstance.on('connect_error', (error) => {
          console.error('WebSocket connection error:', error);
          reconnectAttemptsRef.current++;

          // Se o erro for de autenticação, não tentar reconectar
          if (error.message && error.message.includes('Authentication error')) {
            console.error('Erro de autenticação no WebSocket, não tentando reconectar');
            socketInstance.disconnect();
            setIsConnected(false);
            socketInitializedRef.current = false; // Permitir nova tentativa no futuro
            return;
          }

          if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
            console.error('Máximo de tentativas de reconexão atingido');
            socketInstance.disconnect();
            setIsConnected(false);
            socketInitializedRef.current = false; // Permitir nova tentativa no futuro
          }
        });

        // Evento para receber novas mensagens
        socketInstance.on('message:new', (message) => {
          if (!message || !message.conversationId) {
            console.error('Mensagem inválida recebida:', message);
            return;
          }

          // Usar uma função anônima para evitar dependências circulares
          const userId = user?.id;

          // Verificar se a mensagem tem um tempId associado (para substituir mensagem temporária)
          // O backend pode não retornar o tempId, então precisamos verificar se é uma mensagem do usuário atual
          const tempId = message.tempId;
          const isCurrentUserMessage = message.senderId === userId;


          // Atualizar mensagens da conversa
          setMessages(prev => {
            const conversationMessages = prev[message.conversationId] || [];

            // Verificar se a mensagem já existe para evitar duplicação
            if (conversationMessages.some(m => m.id === message.id)) {
              return prev;
            }

            // Se tiver um tempId, substituir a mensagem temporária pela mensagem real
            if (tempId && conversationMessages.some(m => m.id === tempId)) {
              return {
                ...prev,
                [message.conversationId]: conversationMessages.map(m =>
                  m.id === tempId ? message : m
                )
              };
            }

            // Se for uma mensagem do usuário atual, verificar se há mensagens temporárias recentes
            const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;
            
            if (isCurrentUserMessage && !tempId) {
              // Procurar por mensagens temporárias recentes (nos últimos 10 segundos)
              const now = new Date();
              const tempMessages = conversationMessages.filter(m => {
                if (!m.isTemp) return false;
                if (!(m.senderId === userId || m.senderClientId === userId)) return false;
                if ((now - new Date(m.createdAt)) >= 10000) return false; // 10 segundos
                
                // Para mensagens com anexos, verificar se ambas são ATTACHMENT
                if (message.contentType === 'ATTACHMENT' && m.contentType === 'ATTACHMENT') {
                  return true;
                }
                
                // Para mensagens de texto, verificar se o conteúdo é igual
                if (message.contentType === 'TEXT' && m.contentType === 'TEXT' && m.content === message.content) {
                  return true;
                }
                
                return false;
              });

              if (tempMessages.length > 0) {
                // Substituir a primeira mensagem temporária encontrada
                const tempMessage = tempMessages[0];

                return {
                  ...prev,
                  [message.conversationId]: conversationMessages.map(m =>
                    m.id === tempMessage.id ? message : m
                  )
                };
              }
            }

            // Caso contrário, adicionar a nova mensagem
            // Manter a ordem cronológica (mais antigas primeiro)
            const updatedMessages = [...conversationMessages, message].sort((a, b) =>
              new Date(a.createdAt) - new Date(b.createdAt)
            );

            return {
              ...prev,
              [message.conversationId]: updatedMessages
            };
          });

          // Atualizar lista de conversas (mover para o topo)
          setConversations(prev => {
            // Verificar se a conversa existe
            const conversation = prev.find(c => c.id === message.conversationId);
            if (!conversation) return prev;

            // Se a conversa já tiver uma mensagem temporária com o mesmo tempId, não mover para o topo
            if (tempId && conversation.lastMessage && conversation.lastMessage.id === tempId) {
              // Apenas atualizar a última mensagem sem reordenar
              return prev.map(c => {
                if (c.id === message.conversationId) {
                  return { ...c, lastMessage: message };
                }
                return c;
              });
            }

            // Se for uma mensagem do usuário atual e não tiver tempId, verificar se há uma mensagem temporária recente
            const isCurrentUserMessage = message.senderId === userId || message.senderClientId === userId;
            if (isCurrentUserMessage && !tempId && conversation.lastMessage && conversation.lastMessage.isTemp) {
              // Verificar se a última mensagem é temporária e tem o mesmo conteúdo
              if (conversation.lastMessage.content === message.content &&
                  (conversation.lastMessage.senderId === userId || conversation.lastMessage.senderClientId === userId)) {
                // Apenas atualizar a última mensagem sem reordenar
                return prev.map(c => {
                  if (c.id === message.conversationId) {
                    return { ...c, lastMessage: message };
                  }
                  return c;
                });
              }
            }

            // Caso contrário, mover para o topo
            const updatedConversations = prev.filter(c => c.id !== message.conversationId);
            return [
              { ...conversation, lastMessage: message },
              ...updatedConversations
            ];
          });

          // Incrementar contador de não lidas se a mensagem não for do usuário atual
          if (message.senderId !== userId && message.senderClientId !== userId) {
            setUnreadCount(prev => prev + 1);
          }

          // Disparar evento personalizado para notificar componentes sobre a nova mensagem
          window.dispatchEvent(new CustomEvent('chat:websocket:update', {
            detail: { type: 'message', conversationId: message.conversationId, timestamp: Date.now() }
          }));
        });

        // Evento para atualização de contagem de mensagens não lidas
        socketInstance.on('unread:update', (data) => {
          console.log('Atualização de mensagens não lidas recebida:', data);
          if (data && typeof data.totalUnread === 'number') {
            setUnreadCount(data.totalUnread);

            // Atualizar as conversas com as contagens de não lidas
            if (data.conversations && Array.isArray(data.conversations)) {
              setConversations(prev => {
                return prev.map(conv => {
                  // Procurar esta conversa nos dados de não lidas
                  const unreadInfo = data.conversations.find(
                    item => item.conversationId === conv.id
                  );

                  if (unreadInfo) {
                    return { ...conv, unreadCount: unreadInfo.unreadCount };
                  }
                  return conv;
                });
              });
            }
          }

          // Disparar evento personalizado para notificar componentes sobre a atualização de não lidas
          window.dispatchEvent(new CustomEvent('chat:websocket:update', {
            detail: { type: 'unread', timestamp: Date.now() }
          }));
        });

        // Evento para atualização de lista de conversas
        socketInstance.on('conversations:update', (data) => {
          console.log('Atualização de lista de conversas recebida via WebSocket:', data);
          // Verificar se os dados estão no formato esperado
          if (data) {
            // Pode vir como array direto ou como objeto com propriedade conversations
            const conversationsArray = Array.isArray(data) ? data : data.conversations || [];

            if (Array.isArray(conversationsArray) && conversationsArray.length > 0) {
              console.log(`Atualizando ${conversationsArray.length} conversas via WebSocket`);
              setConversations(conversationsArray);

              // Atualizar a flag para indicar que os dados foram carregados
              initialDataLoadedRef.current = true;

              // Atualizar o cache
              requestCache.conversations.data = conversationsArray;
              requestCache.conversations.timestamp = Date.now();

              // Disparar evento personalizado para notificar componentes sobre a atualização
              window.dispatchEvent(new CustomEvent('chat:websocket:update', {
                detail: { type: 'conversations', timestamp: Date.now() }
              }));
            } else {
              console.error('Formato inválido ou array vazio de conversas recebido via WebSocket:', data);

              // Se recebemos um array vazio, forçar carregamento das conversas
              if (Array.isArray(conversationsArray) && conversationsArray.length === 0) {
                console.log('Array vazio recebido, forçando carregamento de conversas...');
                loadConversations(true);
              }
            }
          }
        });

        // Evento para notificar que uma nova conversa foi criada
        socketInstance.on('conversation:created', (data) => {
          console.log('Nova conversa criada recebida via WebSocket:', data);
          if (data && data.id) {
            // Adicionar a nova conversa ao início da lista
            setConversations(prev => {
              // Verificar se a conversa já existe
              if (prev.some(c => c.id === data.id)) {
                return prev;
              }
              return [data, ...prev];
            });
          }
        });

        setSocket(socketInstance);

        return socketInstance;
      } catch (error) {
        console.error('Erro ao inicializar WebSocket:', error);
        setIsConnected(false);
        socketInitializedRef.current = false; // Permitir nova tentativa no futuro
        return null;
      }
    };

    // Usar uma variável para controlar se já estamos tentando inicializar
    let initializationInProgress = false;

    // Função para inicializar com segurança
    const safeInitialize = () => {
      if (initializationInProgress) {
        console.log('Já existe uma inicialização em andamento, ignorando');
        return;
      }

      initializationInProgress = true;

      // Usar um timeout para garantir que não tentamos inicializar muito frequentemente
      setTimeout(() => {
        initializeWebSocket()
          .then(result => {
            console.log('Inicialização do WebSocket concluída:', result ? 'sucesso' : 'falha');
          })
          .catch(error => {
            console.error('Erro ao inicializar WebSocket:', error);
            socketInitializedRef.current = false; // Permitir nova tentativa no futuro
          })
          .finally(() => {
            initializationInProgress = false;
          });
      }, 2000); // Esperar 2 segundos antes de inicializar
    };

    // Chamar a função de inicialização
    safeInitialize();

    // Cleanup function - só desconectar quando o componente for desmontado completamente
    return () => {
      // Verificar se estamos realmente desmontando o componente (usuário fez logout)
      if (!user) {
        console.log('Usuário fez logout, desconectando socket');
        if (socket) {
          try {
            socket.disconnect();
            socketInitializedRef.current = false; // Permitir nova tentativa após cleanup
          } catch (error) {
            console.error('Erro ao desconectar socket:', error);
          }
        }
      } else {
        console.log('Componente sendo remontado, mantendo socket conectado');
      }
    };
  // Removendo socket e isConnected das dependências para evitar loops
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, getCurrentToken, handleAuthError]);

  // Carregar conversas do usuário com cache
  const loadConversations = useCallback(async (forceRefresh = true) => {
    console.log('loadConversations chamado com forceRefresh =', forceRefresh);
    try {
      // Verificar se o usuário está logado
      if (!user) {
        console.error('Usuário não logado ao carregar conversas');
        return [];
      }

      // Clientes agora podem carregar conversas
      console.log('Carregando conversas para:', user.role || 'USER');

      // Verificar se já está carregando
      if (isLoading || isLoadingRef.current) {
        console.log('Já está carregando conversas, retornando estado atual');
        return conversations;
      }

      // Verificar se já temos conversas carregadas e não é uma atualização forçada
      if (!forceRefresh && conversations.length > 0) {
        console.log('Já temos conversas carregadas e não é uma atualização forçada, retornando estado atual');
        return conversations;
      }

      // Marcar como carregando para evitar chamadas simultâneas
      isLoadingRef.current = true;

      // Verificar cache apenas se não for refresh forçado
      const now = Date.now();
      if (!forceRefresh &&
          requestCache.conversations.data &&
          requestCache.conversations.data.length > 0 &&
          now - requestCache.conversations.timestamp < CACHE_EXPIRATION) {
        console.log('Usando cache para conversas');
        return requestCache.conversations.data;
      }
      
      console.log('Buscando conversas atualizadas da API');

      const currentToken = getCurrentToken();
      console.log('Token ao carregar conversas:', currentToken ? 'Disponível' : 'Não disponível');
      if (!currentToken) {
        console.error('Token não disponível ao carregar conversas');
        return [];
      }

      setIsLoading(true);

      try {
        console.log('Buscando conversas da API...');
        console.log('Fazendo requisição para:', `${API_URL}/chat/conversations?includeMessages=true`);
        const response = await fetch(`${API_URL}/chat/conversations?includeMessages=true`, {
          headers: { Authorization: `Bearer ${currentToken}` }
        });
        console.log('Status da resposta:', response.status, response.statusText);

        if (!response.ok) {
          if (response.status === 401) {
            console.error('Token expirado ou inválido ao carregar conversas');
            handleAuthError();
            return [];
          }
          throw new Error(`Erro ao carregar conversas: ${response.status}`);
        }

        const data = await response.json();
        console.log('Resposta da API de conversas:', data);

        if (data.success) {
          // Verificar se há dados válidos
          // A API retorna { success: true, data: { conversations: [...], total, limit, offset } }
          console.log('Estrutura completa da resposta da API:', JSON.stringify(data));

          const conversationsArray = data.data?.conversations || [];
          console.log('Array de conversas extraído:', conversationsArray);

          if (!Array.isArray(conversationsArray)) {
            console.error('Resposta da API não contém um array de conversas:', data);
            return [];
          }

          console.log(`Recebidas ${conversationsArray.length} conversas da API`);

          // Processar as últimas mensagens das conversas
          console.log('Processando últimas mensagens das conversas...');
          const processedConversations = conversationsArray.map(conversation => {
            // Verificar se a conversa tem mensagens
            if (conversation.messages && conversation.messages.length > 0) {
              console.log(`Conversa ${conversation.id} tem ${conversation.messages.length} mensagens`);
              // Extrair a última mensagem
              const lastMessage = conversation.messages[0]; // A primeira mensagem é a mais recente (ordenada por createdId desc)
              console.log('Ultima mensagem:', lastMessage);

              // IMPORTANTE: Também salvar todas as mensagens desta conversa no estado
              const sortedMessages = [...conversation.messages].sort((a, b) =>
                new Date(a.createdAt) - new Date(b.createdAt)
              );
              console.log(`DEBUG: Salvando ${sortedMessages.length} mensagens da conversa ${conversation.id}`, sortedMessages);

              // Atualizar estado das mensagens
              setMessages(prev => ({
                ...prev,
                [conversation.id]: sortedMessages
              }));

              // Remover o array de mensagens para evitar duplicação
              const { messages, ...conversationWithoutMessages } = conversation;

              // Adicionar a última mensagem como propriedade lastMessage
              return {
                ...conversationWithoutMessages,
                lastMessage
              };
            } else {
              console.log(`Conversa ${conversation.id} não tem mensagens`);
            }

            return conversation;
          });

          // Log para debug dos dados das conversas
          console.log('Conversas processadas:', processedConversations.map(c => ({
            id: c.id,
            participants: c.participants?.map(p => ({
              userId: p.userId,
              clientId: p.clientId,
              user: p.user,
              client: p.client
            }))
          })));

          // Atualizar cache com as conversas processadas
          const now = Date.now();
          requestCache.conversations.data = processedConversations;
          requestCache.conversations.timestamp = now;

          // Atualizar estado - garantir que estamos atualizando o estado mesmo se o array estiver vazio
          setConversations(processedConversations);

          // Disparar evento para notificar componentes sobre a atualização
          window.dispatchEvent(new CustomEvent('chat:websocket:update', {
            detail: { type: 'conversations', timestamp: Date.now() }
          }));

          return conversationsArray;
        } else {
          console.error('Resposta da API não foi bem-sucedida:', data);
          return [];
        }
      } catch (error) {
        console.error('Error loading conversations:', error);
        return [];
      } finally {
        setIsLoading(false);
        isLoadingRef.current = false;
      }
    } catch (error) {
      console.error('Error in loadConversations:', error);
      return [];
    }
  // Removendo conversations das dependências para evitar loops infinitos
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, getCurrentToken, handleAuthError, isLoading]);

  // Carregar mensagens de uma conversa com cache aprimorado
  const loadMessages = useCallback(async (conversationId) => {
    try {
      // Verificar se o usuário está logado
      if (!user) {
        console.error('Usuário não logado ao carregar mensagens');
        return;
      }

      // Verificar cache de mensagens
      const now = Date.now();
      if (requestCache.messages[conversationId] &&
          now - requestCache.messages[conversationId].timestamp < CACHE_EXPIRATION) {
        console.log('Usando cache para mensagens da conversa:', conversationId);
        return requestCache.messages[conversationId].data;
      }

      // Verificar se já tem mensagens completas carregadas no cache
      // Não usar o estado messages aqui pois pode conter apenas a última mensagem do loadConversations

      const currentToken = getCurrentToken();
      if (!currentToken) {
        console.error('Token não disponível ao carregar mensagens');
        return;
      }

      // Evitar múltiplas requisições simultâneas para a mesma conversa
      if (requestCache.messages[conversationId]?.loading) {
        console.log('Já existe uma requisição em andamento para esta conversa');
        return;
      }

      // Marcar como carregando
      requestCache.messages[conversationId] = { loading: true };

      try {
        console.log('Buscando mensagens da API para conversa:', conversationId);
        const response = await fetch(`${API_URL}/chat/conversations/${conversationId}/messages`, {
          headers: { Authorization: `Bearer ${currentToken}` }
        });

        if (!response.ok) {
          if (response.status === 401) {
            console.error('Token expirado ou inválido ao carregar mensagens');
            handleAuthError();
            return;
          }
          throw new Error(`Erro ao carregar mensagens: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          // Debug para verificar mensagens carregadas
          console.log('DEBUG: Mensagens carregadas para conversa', conversationId, data.data);
          
          // Atualizar cache
          requestCache.messages[conversationId] = {
            data: data.data,
            timestamp: now,
            loading: false
          };

          // Atualizar estado das mensagens
          // Garantir que as mensagens estejam na ordem correta (mais antigas primeiro)
          // para que a ordenação no componente funcione corretamente
          const sortedMessages = [...data.data].sort((a, b) =>
            new Date(a.createdAt) - new Date(b.createdAt)
          );

          console.log('DEBUG: Mensagens ordenadas', sortedMessages);

          setMessages(prev => ({
            ...prev,
            [conversationId]: sortedMessages
          }));

          // Atualizar a última mensagem da conversa
          if (data.data && data.data.length > 0) {
            const lastMessage = data.data[data.data.length - 1];
            setConversations(prev => {
              return prev.map(conv => {
                if (conv.id === conversationId && (!conv.lastMessage || new Date(lastMessage.createdAt) > new Date(conv.lastMessage.createdAt))) {
                  return { ...conv, lastMessage };
                }
                return conv;
              });
            });
          }

          return data.data;
        }
      } catch (error) {
        console.error('Error loading messages:', error);
      } finally {
        // Remover flag de carregamento em caso de erro
        if (requestCache.messages[conversationId]?.loading) {
          requestCache.messages[conversationId].loading = false;
        }
      }
    } catch (error) {
      console.error('Error in loadMessages:', error);
    }
  // Removendo messages das dependências para evitar loops infinitos
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, getCurrentToken, handleAuthError]);

  // Enviar mensagem
  const sendMessage = useCallback((conversationId, content, contentType = 'TEXT', metadata = null) => {
    if (!socket || !isConnected) return;

    // Gerar um ID temporário para a mensagem
    const tempId = `temp-${Date.now()}`;

    // Criar uma mensagem temporária para exibir imediatamente
    const tempMessage = {
      id: tempId,
      conversationId,
      content,
      contentType,
      metadata,
      senderId: user?.isClient || user?.role === 'CLIENT' ? null : user?.id,
      senderClientId: user?.isClient || user?.role === 'CLIENT' ? user?.id : null,
      createdAt: new Date().toISOString(),
      sender: user?.isClient || user?.role === 'CLIENT' ? null : { id: user?.id, fullName: user?.fullName },
      senderClient: user?.isClient || user?.role === 'CLIENT' ? { id: user?.id, fullName: user?.fullName || user?.login, login: user?.login } : null,
      // Marcar como temporária para evitar duplicação
      isTemp: true
    };

    // Atualizar mensagens localmente antes de enviar
    setMessages(prev => ({
      ...prev,
      [conversationId]: [...(prev[conversationId] || []), tempMessage]
    }));

    // Atualizar a conversa com a última mensagem
    setConversations(prev => {
      return prev.map(conv => {
        if (conv.id === conversationId) {
          return { ...conv, lastMessage: tempMessage };
        }
        return conv;
      });
    });

    // Enviar a mensagem via WebSocket
    const messageData = {
      conversationId,
      content,
      contentType,
      metadata,
      tempId // Incluir o ID temporário para poder substituir depois
    };


    socket.emit('message:send', messageData, (response) => {
      if (response.success) {
        // A mensagem real será adicionada pelo evento message:new do WebSocket
        // Não precisamos fazer nada aqui, pois o WebSocket vai atualizar a mensagem
      } else {
        console.error('Error sending message:', response.error);
        // Em caso de erro, podemos marcar a mensagem como falha
        setMessages(prev => {
          const conversationMessages = prev[conversationId] || [];
          return {
            ...prev,
            [conversationId]: conversationMessages.map(msg =>
              msg.id === tempId ? { ...msg, failed: true } : msg
            )
          };
        });
      }
    });
  }, [socket, isConnected, user]);

  // Criar nova conversa
  const createConversation = useCallback(async (participantIds, title = null) => {
    try {
      console.log('Criando conversa com participantes:', participantIds);
      console.log('Tipo de conversa:', participantIds.length > 1 ? 'GRUPO' : 'INDIVIDUAL');
      console.log('Título da conversa:', title);
      
      // Verificar se algum participante é cliente
      const hasClientParticipants = participantIds.some(id => {
        // Verificar se o ID corresponde a um cliente (assumindo que clientes têm isClient: true)
        return typeof id === 'object' && id.isClient;
      });
      
      console.log('Tem participantes clientes:', hasClientParticipants);

      // Obter o token mais recente do localStorage
      const currentToken = getCurrentToken();
      console.log('Token disponível:', currentToken ? 'Sim' : 'Não');

      if (!currentToken) {
        console.error('Token não disponível. Usuário precisa fazer login novamente.');
        return null;
      }

      const response = await fetch(`${API_URL}/chat/conversations`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${currentToken}`
        },
        body: JSON.stringify({
          type: participantIds.length > 1 ? 'GROUP' : 'INDIVIDUAL',
          title,
          participantIds: participantIds.map(id => typeof id === 'object' ? id.id : id),
          includeClients: true // Permitir incluir clientes nas conversas
        })
      });

      console.log('Resposta da API:', response.status, response.statusText);
      const data = await response.json();
      console.log('Dados da resposta:', data);

      if (data.success) {
        // Extrair os dados da conversa criada
        const conversationData = data.data;
        console.log('Conversa criada com sucesso:', conversationData);

        // Adicionar a nova conversa à lista
        setConversations(prev => {
          // Verificar se a conversa já existe na lista para evitar duplicação
          if (prev.some(c => c.id === conversationData.id)) {
            return prev;
          }
          return [conversationData, ...prev];
        });

        // Disparar evento para notificar componentes sobre a nova conversa
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('chat:websocket:update', {
            detail: {
              type: 'conversations',
              conversationId: conversationData.id,
              timestamp: Date.now()
            }
          }));
        }, 300);

        return conversationData;
      } else {
        console.error('Erro ao criar conversa:', data.error || 'Erro desconhecido');
        


        // Se o erro for de autenticação, redirecionar para login
        if (response.status === 401) {
          console.error('Token expirado ou inválido. Redirecionando para login...');
          handleAuthError();
        }
      }

      return null;
    } catch (error) {
      console.error('Error creating conversation:', error);
      return null;
    }
  }, [handleAuthError]);

  // Criar ou obter conversa com um usuário específico
  const createOrGetConversation = useCallback(async (otherUser) => {
    try {
      console.log('createOrGetConversation chamado com usuário:', otherUser);

      if (!otherUser || !otherUser.id) {
        console.error('Usuário inválido:', otherUser);
        return null;
      }

      // Primeiro, verificar se já existe uma conversa individual com este usuário
      const existingConversation = conversations.find(conv => {
        if (conv.type !== 'INDIVIDUAL') return false;

        return conv.participants?.some(p => p.userId === otherUser.id);
      });

      if (existingConversation) {
        console.log('Conversa existente encontrada:', existingConversation);
        setActiveConversation(existingConversation.id);
        return existingConversation;
      }

      console.log('Criando nova conversa com usuário:', otherUser.fullName);
      // Se não existir, criar uma nova conversa
      const newConversation = await createConversation([otherUser.id]);

      if (newConversation) {
        console.log('Nova conversa criada com sucesso:', newConversation);
        // Garantir que a conversa seja adicionada à lista antes de definir como ativa
        setConversations(prev => {
          // Verificar se a conversa já existe na lista para evitar duplicação
          if (prev.some(c => c.id === newConversation.id)) {
            return prev;
          }
          return [newConversation, ...prev];
        });

        // Definir a conversa como ativa após um pequeno delay para garantir que a lista foi atualizada
        setTimeout(() => {
          setActiveConversation(newConversation.id);

          // Disparar evento para notificar componentes sobre a nova conversa
          window.dispatchEvent(new CustomEvent('chat:websocket:update', {
            detail: {
              type: 'conversations',
              conversationId: newConversation.id,
              timestamp: Date.now()
            }
          }));
        }, 300);
      } else {
        console.error('Falha ao criar nova conversa, retorno nulo ou indefinido');
      }

      return newConversation;
    } catch (error) {
      console.error('Error creating or getting conversation:', error);
      return null;
    }
  // Removendo conversations das dependências para evitar loops infinitos
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createConversation, setActiveConversation, handleAuthError]);

  // Carregar contagem de mensagens não lidas com cache
  const loadUnreadCount = useCallback(async (forceRefresh = false) => {
    try {
      // Verificar se o usuário está logado
      if (!user) {
        console.error('Usuário não logado ao carregar contagem de mensagens não lidas');
        return;
      }

      // Verificar cache
      const now = Date.now();
      if (!forceRefresh &&
          requestCache.unreadCount.data !== null &&
          now - requestCache.unreadCount.timestamp < CACHE_EXPIRATION) {
        console.log('Usando cache para contagem de mensagens não lidas');
        return requestCache.unreadCount.data;
      }

      const currentToken = getCurrentToken();
      if (!currentToken) {
        console.error('Token não disponível ao carregar contagem de mensagens não lidas');
        return;
      }

      try {
        console.log('Buscando contagem de mensagens não lidas da API...');
        const response = await fetch(`${API_URL}/chat/messages/unread`, {
          headers: { Authorization: `Bearer ${currentToken}` }
        });

        if (!response.ok) {
          if (response.status === 401) {
            console.error('Token expirado ou inválido ao carregar contagem de mensagens não lidas');
            handleAuthError();
            return;
          }
          throw new Error(`Erro ao carregar contagem de mensagens não lidas: ${response.status}`);
        }

        const data = await response.json();
        console.log('Resposta da API de mensagens não lidas:', data);

        if (data.success) {
          console.log('Dados de mensagens não lidas recebidos:', data.data);

          // Atualizar cache com o totalUnread
          requestCache.unreadCount.data = data.data.totalUnread;
          requestCache.unreadCount.timestamp = now;

          // Atualizar estado
          setUnreadCount(data.data.totalUnread);

          // Atualizar as conversas com as contagens de não lidas
          if (data.data.conversations && Array.isArray(data.data.conversations)) {
            // Primeiro, verificar se já temos as conversas carregadas
            const conversationIds = data.data.conversations.map(c => c.conversationId);

            // Se não temos conversas carregadas ou se temos menos conversas do que as não lidas,
            // forçar uma atualização das conversas
            if (conversations.length === 0 ||
                !conversationIds.every(id => conversations.some(c => c.id === id))) {
              console.log('Forçando atualização das conversas porque há mensagens não lidas em conversas não carregadas');
              await loadConversations(true);
            } else {
              // Atualizar as conversas existentes com as contagens de não lidas
              setConversations(prev => {
                return prev.map(conv => {
                  // Procurar esta conversa nos dados de não lidas
                  const unreadInfo = data.data.conversations.find(
                    item => item.conversationId === conv.id
                  );

                  if (unreadInfo) {
                    return { ...conv, unreadCount: unreadInfo.unreadCount };
                  }
                  return conv;
                });
              });
            }
          }

          return data.data.totalUnread;
        }
      } catch (error) {
        console.error('Error loading unread count:', error);
      }
    } catch (error) {
      console.error('Error in loadUnreadCount:', error);
    }
  // Removendo conversations e loadConversations das dependências para evitar loops infinitos
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, getCurrentToken, handleAuthError]);

  // Referência para controlar se os dados iniciais já foram carregados
  const initialDataLoadedRef = useRef(false);

  // Resetar a flag quando o usuário muda
  useEffect(() => {
    if (user) {
      console.log('Usuário alterado, resetando flag de dados carregados');
      initialDataLoadedRef.current = false;
    }
  }, [user?.id]); // Dependendo apenas do ID do usuário para evitar re-renders desnecessários

  // Efeito para carregar dados iniciais e configurar atualização periódica
  useEffect(() => {
    // Verificar se o usuário está logado antes de carregar dados
    if (!user) {
      console.log('Usuário não logado, não carregando dados iniciais');
      return;
    }

    // Clientes agora podem usar o chat
    console.log('Carregando dados iniciais para:', user.role || 'USER');

    // Verificar token
    const currentToken = getCurrentToken();
    if (!currentToken) {
      console.log('Token não disponível, não carregando dados iniciais');
      return;
    }

    // Verificar se já está carregando
    if (isLoading || isLoadingRef.current) {
      console.log('Já está carregando dados, aguardando...');
      return;
    }

    // Verificar se os dados já foram carregados
    if (initialDataLoadedRef.current) {
      console.log('Dados já foram carregados anteriormente, ignorando');
      return;
    }

    // Função para carregar dados iniciais
    const loadInitialData = async () => {
      if (isLoading || isLoadingRef.current) {
        console.log('Já está carregando dados, cancelando carregamento inicial');
        return;
      }

      // Marcar como carregando
      isLoadingRef.current = true;
      console.log('Iniciando carregamento de dados do chat...');

      try {
        // Primeiro carregar as conversas
        console.log('Carregando conversas...');
        const conversationsData = await loadConversations(true);
        console.log('Conversas carregadas:', conversationsData?.length || 0, 'conversas');

        // Não precisamos carregar a contagem de não lidas separadamente
        // O WebSocket vai nos informar sobre mensagens não lidas
        console.log('Pulando carregamento manual de mensagens não lidas para evitar flood');

        // Marcar como carregado
        initialDataLoadedRef.current = true;
        console.log('Carregamento de dados concluído com sucesso');
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
      } finally {
        isLoadingRef.current = false;
      }
    };

    // Carregar dados iniciais com debounce para evitar múltiplas chamadas
    console.log('Agendando carregamento de dados iniciais...');
    const debouncedLoadData = debounce(() => {
      console.log('Carregando dados iniciais (debounced)...');
      loadInitialData();
    }, 1000); // Esperar 1 segundo antes de carregar

    // Chamar a função com debounce
    debouncedLoadData();

    // Removemos a atualização periódica via HTTP para evitar flood no backend
    // Agora dependemos apenas do WebSocket para atualizações em tempo real

    return () => {
      // Cleanup function
    };
  // Removendo loadUnreadCount e isPanelOpen, isModalOpen das dependências para evitar chamadas desnecessárias
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, getCurrentToken, loadConversations, isLoading]);

  // Efeito para verificar se activeConversation é válido
  useEffect(() => {
    if (activeConversation && conversations.length > 0) {
      const conversationExists = conversations.some(c => c.id === activeConversation);
      if (!conversationExists) {
        console.warn('Conversa ativa não encontrada na lista de conversas, resetando...', activeConversation);
        setActiveConversation(null);
      }
    }
  }, [activeConversation, conversations]);

  // Efeito para monitorar mudanças no token e reconectar quando necessário
  useEffect(() => {
    // Verificar se o usuário está logado antes de monitorar o token
    if (!user) return;

    // Verificar o token a cada 2 minutos (aumentado para reduzir a frequência)
    const tokenCheckInterval = setInterval(() => {
      // Evitar verificações desnecessárias se estiver carregando
      if (isLoading || isLoadingRef.current) return;

      const currentToken = getCurrentToken();

      // Se não há token mas há conexão, desconectar
      if (!currentToken && isConnected && socket) {
        console.log('Token não encontrado, desconectando WebSocket...');
        try {
          socket.disconnect();
        } catch (error) {
          console.error('Erro ao desconectar socket:', error);
        }
        setIsConnected(false);
      }

      // Se há token mas não há conexão, tentar reconectar (com verificação de tempo)
      if (currentToken && !isConnected && !socket) {
        const now = Date.now();
        // Limitar tentativas de reconexão (no máximo uma a cada 2 minutos)
        if (now - lastInitAttemptRef.current > 120000) {
          console.log('Token encontrado, tentando reconectar WebSocket...');
          // Permitir nova tentativa de inicialização do WebSocket
          socketInitializedRef.current = false;
          lastInitAttemptRef.current = now;
        }
      }
    }, 120000); // Verificar a cada 2 minutos

    return () => clearInterval(tokenCheckInterval);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, getCurrentToken]);

  // Abrir/fechar painel de chat
  const toggleChatPanel = useCallback(() => {
    // Verificar se o usuário está logado
    if (!user) return;

    setIsPanelOpen(prev => {
      const newState = !prev;
      // Se estiver abrindo o painel, fechar o modal
      if (newState) {
        setIsModalOpen(false);
      }
      return newState;
    });
  }, [user]);

  // Abrir/fechar modal de chat
  const toggleChatModal = useCallback(() => {
    // Verificar se o usuário está logado
    if (!user) return;

    setIsModalOpen(prev => {
      const newState = !prev;
      // Se estiver abrindo o modal, fechar o painel
      if (newState) {
        setIsPanelOpen(false);
      }
      return newState;
    });
  }, [user]);

  // Verificar se createConversation é uma função válida
  console.log('ChatContext: createConversation é uma função?', typeof createConversation === 'function');

  // Adicionar participante a um grupo
  const addParticipantToGroup = useCallback(async (conversationId, participantId) => {
    try {
      if (!conversationId || !participantId) {
        console.error('ID da conversa e ID do participante são obrigatórios');
        return null;
      }

      const currentToken = getCurrentToken();
      if (!currentToken) {
        console.error('Token não disponível ao adicionar participante');
        return null;
      }

      const response = await fetch(`${API_URL}/chat/conversations/${conversationId}/participants`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${currentToken}`
        },
        body: JSON.stringify({ participantId })
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.error('Token expirado ou inválido ao adicionar participante');
          handleAuthError();
          return null;
        }
        throw new Error(`Erro ao adicionar participante: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Atualizar a conversa com o novo participante
        setConversations(prev => {
          return prev.map(conv => {
            if (conv.id === conversationId) {
              // Verificar se o participante já existe
              const participantExists = conv.participants.some(p => p.userId === participantId);

              if (participantExists) {
                return conv;
              }

              // Adicionar o novo participante
              return {
                ...conv,
                participants: [...conv.participants, data.data]
              };
            }
            return conv;
          });
        });

        // Criar uma mensagem do sistema para notificar que um participante foi adicionado
        if (socket && isConnected) {
          const systemMessage = {
            conversationId,
            content: `${data.data.user.fullName} foi adicionado ao grupo`,
            contentType: 'SYSTEM'
          };

          socket.emit('message:send', systemMessage);
        }

        return data.data;
      }

      return null;
    } catch (error) {
      console.error('Erro ao adicionar participante:', error);
      return null;
    }
  }, [socket, isConnected, getCurrentToken, handleAuthError]);

  // Adicionar múltiplos participantes a um grupo
  const addMultipleParticipantsToGroup = useCallback(async (conversationId, participants) => {
    try {
      if (!conversationId || !participants || !participants.length) {
        console.error('ID da conversa e lista de participantes são obrigatórios');
        return null;
      }

      console.log(`Adicionando ${participants.length} participantes ao grupo ${conversationId}`);

      // Array para armazenar os resultados
      const results = [];
      const errors = [];

      // Adicionar participantes um por um
      for (const participant of participants) {
        try {
          const result = await addParticipantToGroup(conversationId, participant.id);
          if (result) {
            results.push(result);
          } else {
            errors.push({ user: participant, error: 'Falha ao adicionar participante' });
          }
        } catch (error) {
          console.error(`Erro ao adicionar participante ${participant.id}:`, error);
          errors.push({ user: participant, error: error.message || 'Erro desconhecido' });
        }
      }

      return {
        success: results.length > 0,
        added: results,
        errors: errors,
        total: participants.length,
        successCount: results.length,
        errorCount: errors.length
      };
    } catch (error) {
      console.error('Erro ao adicionar múltiplos participantes:', error);
      return {
        success: false,
        added: [],
        errors: [{ error: error.message || 'Erro desconhecido' }],
        total: participants.length,
        successCount: 0,
        errorCount: participants.length
      };
    }
  }, [addParticipantToGroup]);

  // Remover participante de um grupo (ou sair do grupo)
  const removeParticipantFromGroup = useCallback(async (conversationId, participantId) => {
    console.log('removeParticipantFromGroup chamado:', { conversationId, participantId, userId: user?.id });
    
    // Se participantId é o próprio usuário, é uma exclusão de conversa
    if (participantId === user?.id) {
      console.log('Removendo conversa da lista local...');
      // Remover da lista local imediatamente
      setConversations(prev => prev.filter(c => c.id !== conversationId));
      
      // Se era a conversa ativa, limpar
      if (activeConversation?.id === conversationId) {
        setActiveConversation(null);
      }
      
      // Limpar cache
      requestCache.conversations.data = null;
      requestCache.conversations.timestamp = 0;
      
      console.log('Conversa removida da lista local');
      
      return { success: true };
    }
    
    // Limpar cache antes da remoção
    requestCache.conversations.data = null;
    requestCache.conversations.timestamp = 0;
    try {
      if (!conversationId || !participantId) {
        console.error('ID da conversa e ID do participante são obrigatórios');
        return null;
      }

      const currentToken = getCurrentToken();
      if (!currentToken) {
        console.error('Token não disponível ao remover participante');
        return null;
      }

      const response = await fetch(`${API_URL}/chat/conversations/${conversationId}/participants/${participantId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${currentToken}`
        }
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.error('Token expirado ou inválido ao remover participante');
          handleAuthError();
          return null;
        }
        throw new Error(`Erro ao remover participante: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Se o usuário atual está saindo do grupo, remover a conversa da lista
        if (participantId === user?.id) {
          // Remover a conversa da lista
          setConversations(prev => prev.filter(conv => conv.id !== conversationId));

          // Limpar as mensagens da conversa
          setMessages(prev => {
            const updatedMessages = { ...prev };
            delete updatedMessages[conversationId];
            return updatedMessages;
          });

          // Limpar o cache de mensagens
          if (requestCache.messages[conversationId]) {
            delete requestCache.messages[conversationId];
          }

          // Se a conversa ativa é a que o usuário está saindo, resetar
          if (activeConversation === conversationId) {
            setActiveConversation(null);
          }

          // Atualizar a contagem de mensagens não lidas
          loadUnreadCount(true);

          console.log(`Conversa ${conversationId} removida com sucesso`);
        } else {
          // Atualizar a conversa removendo o participante
          setConversations(prev => {
            return prev.map(conv => {
              if (conv.id === conversationId) {
                return {
                  ...conv,
                  participants: conv.participants.filter(p => p.userId !== participantId)
                };
              }
              return conv;
            });
          });

          // Criar uma mensagem do sistema para notificar que um participante saiu
          if (socket && isConnected) {
            const systemMessage = {
              conversationId,
              content: `${data.data.user.fullName} saiu do grupo`,
              contentType: 'SYSTEM'
            };

            socket.emit('message:send', systemMessage);
          }
        }

        return data.data;
      }

      return null;
    } catch (error) {
      console.error('Erro ao remover participante:', error);
      return null;
    }
  }, [socket, isConnected, user, activeConversation, getCurrentToken, handleAuthError, loadUnreadCount]);

  // Apagar mensagens
  const deleteMessages = useCallback(async (messageIds, conversationId) => {
    try {
      if (!messageIds || messageIds.length === 0) {
        console.error('IDs das mensagens são obrigatórios');
        return null;
      }

      const currentToken = getCurrentToken();
      if (!currentToken) {
        console.error('Token não disponível ao apagar mensagens');
        return null;
      }

      const results = [];
      const errors = [];

      // Apagar mensagens uma por uma
      for (const messageId of messageIds) {
        try {
          const response = await fetch(`${API_URL}/chat/messages/${messageId}`, {
            method: 'DELETE',
            headers: {
              Authorization: `Bearer ${currentToken}`
            }
          });

          if (!response.ok) {
            if (response.status === 401) {
              console.error('Token expirado ou inválido ao apagar mensagem');
              handleAuthError();
              return null;
            }
            throw new Error(`Erro ao apagar mensagem: ${response.status}`);
          }

          const data = await response.json();

          if (data.success) {
            results.push(data.data);

            // Atualizar o estado das mensagens
            setMessages(prev => {
              const updatedMessages = { ...prev };

              // Percorrer todas as conversas
              Object.keys(updatedMessages).forEach(convId => {
                // Atualizar a mensagem na conversa correspondente
                updatedMessages[convId] = updatedMessages[convId].map(msg => {
                  if (msg.id === messageId) {
                    return {
                      ...msg,
                      ...data.data,
                      isDeleted: true
                    };
                  }
                  return msg;
                });
              });

              return updatedMessages;
            });
          } else {
            errors.push({ messageId, error: 'Falha ao apagar mensagem' });
          }
        } catch (error) {
          console.error(`Erro ao apagar mensagem ${messageId}:`, error);
          errors.push({ messageId, error: error.message || 'Erro desconhecido' });
        }
      }

      // Limpar o cache de mensagens para forçar uma nova busca
      if (conversationId && results.length > 0) {
        console.log('Limpando cache de mensagens para a conversa:', conversationId);
        if (requestCache.messages[conversationId]) {
          delete requestCache.messages[conversationId];
        }

        // Recarregar as mensagens da conversa para garantir que estamos sincronizados com o backend
        try {
          console.log('Recarregando mensagens após exclusão');
          await loadMessages(conversationId);

          // Também recarregar a lista de conversas para garantir que tudo está atualizado
          await loadConversations(true);
        } catch (reloadError) {
          console.error('Erro ao recarregar mensagens após exclusão:', reloadError);
        }
      }

      return {
        success: results.length > 0,
        deleted: results,
        errors: errors,
        total: messageIds.length,
        successCount: results.length,
        errorCount: errors.length
      };
    } catch (error) {
      console.error('Erro ao apagar mensagens:', error);
      return {
        success: false,
        deleted: [],
        errors: [{ error: error.message || 'Erro desconhecido' }],
        total: messageIds.length,
        successCount: 0,
        errorCount: messageIds.length
      };
    }
  }, [getCurrentToken, handleAuthError, loadMessages, loadConversations]);

  // Memoizar o valor do contexto para evitar re-renderizações desnecessárias
  const contextValue = useMemo(() => ({
    conversations,
    messages,
    unreadCount,
    activeConversation,
    isPanelOpen,
    isModalOpen,
    isConnected,
    isLoading,
    setActiveConversation,
    loadMessages,
    sendMessage,
    createConversation,
    createOrGetConversation,
    toggleChatPanel,
    toggleChatModal,
    loadConversations,
    loadUnreadCount,
    addParticipantToGroup,
    addMultipleParticipantsToGroup,
    removeParticipantFromGroup,
    deleteMessages
  }), [
    conversations,
    messages,
    unreadCount,
    activeConversation,
    isPanelOpen,
    isModalOpen,
    isConnected,
    isLoading,
    setActiveConversation,
    loadMessages,
    sendMessage,
    createConversation,
    createOrGetConversation,
    toggleChatPanel,
    toggleChatModal,
    loadConversations,
    loadUnreadCount,
    addParticipantToGroup,
    addMultipleParticipantsToGroup,
    removeParticipantFromGroup,
    deleteMessages
  ]);

  return (
    <ChatContext.Provider value={contextValue}>
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => {
  const context = useContext(ChatContext);

  if (!context) {
    throw new Error('useChat deve ser usado dentro de um ChatProvider');
  }

  return context;
};
