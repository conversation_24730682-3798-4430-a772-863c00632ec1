const prisma = require('../utils/prisma');

/**
 * Middleware para registrar acessos a dados sensíveis
 * Registra quando dados com configurações de privacidade são acessados
 */
function auditSensitiveDataAccess(entityType) {
  return async (req, res, next) => {
    // Interceptar o método res.json para auditar após o retorno dos dados
    const originalJson = res.json.bind(res);
    
    res.json = async function(data) {
      try {
        // Só auditar se há dados e o usuário tem empresa
        if (data && req.user?.companyId && req.user?.id) {
          // Buscar configurações de privacidade
          const preferences = await prisma.companyPreference.findUnique({
            where: { companyId: req.user.companyId },
            select: { preferences: true }
          });

          const privacySection = `${entityType}Privacy`;
          const privacySettings = preferences?.preferences?.[privacySection];

          // Se há configurações de privacidade ativas, registrar o acesso
          if (privacySettings && Object.values(privacySettings).some(value => value === true)) {
            // Determinar quantos registros foram acessados
            let recordCount = 0;
            if (Array.isArray(data)) {
              recordCount = data.length;
            } else if (data.data && Array.isArray(data.data)) {
              recordCount = data.data.length;
            } else if (data.users && Array.isArray(data.users)) {
              recordCount = data.users.length;
            } else if (data.clients && Array.isArray(data.clients)) {
              recordCount = data.clients.length;
            } else if (data.persons && Array.isArray(data.persons)) {
              recordCount = data.persons.length;
            } else if (typeof data === 'object' && data.id) {
              recordCount = 1;
            }

            // Registrar o acesso (em background para não afetar performance)
            setImmediate(async () => {
              try {
                await prisma.auditLog.create({
                  data: {
                    userId: req.user.id,
                    companyId: req.user.companyId,
                    module: 'ADMIN',
                    action: 'SENSITIVE_DATA_ACCESS',
                    entityType: entityType.toUpperCase(),
                    entityId: null,
                    details: {
                      endpoint: req.originalUrl,
                      method: req.method,
                      recordCount,
                      privacySettingsActive: Object.keys(privacySettings).filter(key => privacySettings[key] === true),
                      userAgent: req.get('User-Agent'),
                      ip: req.ip || req.connection.remoteAddress
                    },
                    ipAddress: req.ip || req.connection.remoteAddress,
                    userAgent: req.get('User-Agent')
                  }
                });
              } catch (auditError) {
                console.error('Erro ao registrar auditoria de dados sensíveis:', auditError);
              }
            });
          }
        }
      } catch (error) {
        console.error('Erro no middleware de auditoria:', error);
        // Não interromper o fluxo em caso de erro
      }

      return originalJson(data);
    };

    next();
  };
}

/**
 * Middleware específico para auditoria de usuários
 */
const auditUserDataAccess = auditSensitiveDataAccess('user');

/**
 * Middleware específico para auditoria de clientes
 */
const auditClientDataAccess = auditSensitiveDataAccess('client');

/**
 * Middleware específico para auditoria de pacientes
 */
const auditPatientDataAccess = auditSensitiveDataAccess('patient');

/**
 * Middleware para registrar mudanças nas configurações de privacidade
 */
function auditPrivacyConfigChanges(req, res, next) {
  const originalJson = res.json.bind(res);
  
  res.json = async function(data) {
    try {
      // Registrar mudanças nas configurações de privacidade
      if (res.statusCode >= 200 && res.statusCode < 300 && req.body?.preferences) {
        const companyId = req.body?.companyId || req.user?.companyId;
        
        if (companyId && req.user?.id) {
          const hasPrivacySettings = req.body.preferences.userPrivacy || 
                                   req.body.preferences.clientPrivacy || 
                                   req.body.preferences.patientPrivacy;

          if (hasPrivacySettings) {
            setImmediate(async () => {
              try {
                await prisma.auditLog.create({
                  data: {
                    userId: req.user.id,
                    companyId: companyId,
                    module: 'ADMIN',
                    action: 'PRIVACY_CONFIG_UPDATED',
                    entityType: 'COMPANY_PREFERENCE',
                    entityId: companyId,
                    details: {
                      updatedSettings: {
                        userPrivacy: req.body.preferences.userPrivacy,
                        clientPrivacy: req.body.preferences.clientPrivacy,
                        patientPrivacy: req.body.preferences.patientPrivacy
                      },
                      userAgent: req.get('User-Agent'),
                      ip: req.ip || req.connection.remoteAddress
                    },
                    ipAddress: req.ip || req.connection.remoteAddress,
                    userAgent: req.get('User-Agent')
                  }
                });
              } catch (auditError) {
                console.error('Erro ao registrar auditoria de configuração de privacidade:', auditError);
              }
            });
          }
        }
      }
    } catch (error) {
      console.error('Erro no middleware de auditoria de configuração:', error);
    }

    return originalJson(data);
  };

  next();
}

module.exports = {
  auditSensitiveDataAccess,
  auditUserDataAccess,
  auditClientDataAccess,
  auditPatientDataAccess,
  auditPrivacyConfigChanges
};