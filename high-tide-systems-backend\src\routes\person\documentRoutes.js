// src/routes/documentRoutes.js
const express = require("express");
const router = express.Router();
const { DocumentController, uploadDocumentValidation, uploadSharedDocumentValidation } = require("../../controllers/documentController");
const upload = require("../../middlewares/upload");
const { authenticate } = require('../../middlewares/auth');
const checkPermission = require('../../middlewares/permissionCheck');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rota para upload de múltiplos documentos
router.post(
  "/upload",
  checkPermission('people.documents.create'),
  upload.array("documents", 10), // 'documents' é o nome do campo, 10 é o máximo de arquivos
  uploadDocumentValidation,
  DocumentController.upload
);

// Rota para upload de documentos compartilhados (sem validação de types)
router.post(
  "/shared/upload",
  checkPermission('people.documents.create'),
  upload.array("documents", 10), // 'documents' é o nome do campo, 10 é o máximo de arquivos
  uploadSharedDocumentValidation,
  DocumentController.uploadShared
);

// CRUD de categorias de documentos (deve vir ANTES das rotas com parâmetros)
router.post("/category", checkPermission('people.documents.categories.create'), DocumentController.createCategoryDocument);
router.get("/category", checkPermission('people.documents.categories.view'), DocumentController.listCategoryDocuments);
router.put("/category/:id", checkPermission('people.documents.categories.edit'), DocumentController.updateCategoryDocument);
router.delete("/category/:id", checkPermission('people.documents.categories.delete'), DocumentController.deleteCategoryDocument);

// Endpoint de teste para verificar se a API está funcionando
router.get("/test", (req, res) => {
  res.json({ 
    message: "API de documentos funcionando", 
    timestamp: new Date().toISOString(),
    user: req.user ? { id: req.user.id, role: req.user.role } : null
  });
});

// Demais rotas de documentos
router.get("/", checkPermission('people.documents.view'), DocumentController.list);
router.get("/:id", checkPermission('people.documents.view'), DocumentController.getDocument);
router.get("/:id/download", checkPermission('people.documents.download'), DocumentController.downloadDocument); // Nova rota para servir arquivos
router.put("/:id", checkPermission('people.documents.edit'), DocumentController.update);
router.delete("/:id", checkPermission('people.documents.delete'), DocumentController.delete);

// Rota para gerenciar permissões de documentos
router.put("/:id/permissions", checkPermission('people.documents.edit'), DocumentController.updateDocumentPermissions);
router.get("/:id/permissions", checkPermission('people.documents.view'), DocumentController.getDocumentPermissions);

module.exports = router;