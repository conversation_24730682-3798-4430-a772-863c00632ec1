'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  Menu, X, LogOut, User, Calendar, Clock,
  Search, Settings, ChevronDown
} from 'lucide-react';
import { useQuickNav } from '@/contexts/QuickNavContext';
import { useRouter, usePathname } from 'next/navigation';
import { ThemeToggle } from '@/components/ThemeToggle';
import ChatButton from '@/components/chat/ChatButton';
import { APP_VERSION } from '@/config/appConfig';
import { personsService } from '@/app/modules/people/services/personsService';
import { modules } from './components';

// Header Component adaptado para clientes
const ClientHeader = ({ toggleSidebar, isSidebarOpen }) => {
  const { user, logout } = useAuth();
  const router = useRouter();
  const { openQuickNav } = useQuickNav();
  const pathname = usePathname();

  // Função para obter o módulo ativo a partir da URL
  const getActiveModule = () => {
    if (!pathname) return null;
    const path = pathname.split('/');
    if (path.length >= 3) {
      return modules.find(m => m.id === path[2]);
    }
    return null;
  };
  const activeModule = getActiveModule();

  // Pegar primeira letra de cada nome para o avatar
  const getInitials = () => {
    // Verificar se temos uma pessoa associada ao cliente
    if (user?.persons && user.persons.length > 0 && user.persons[0].fullName) {
      const fullName = user.persons[0].fullName;
      const names = fullName.split(' ');
      if (names.length === 1) return names[0].charAt(0);
      return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
    }

    // Fallback para o login do cliente
    return user?.login?.charAt(0) || 'C';
  };

  // Obter o nome completo da pessoa associada ao cliente ou o login do cliente
  const getDisplayName = () => {
    if (user?.persons && user.persons.length > 0 && user.persons[0].fullName) {
      return user.persons[0].fullName;
    }
    return user?.login || 'Cliente';
  };

  // Obter a URL da imagem de perfil da pessoa associada ao cliente
  const getProfileImage = () => {
    if (user?.persons && user.persons.length > 0) {
      // Primeiro tenta usar a URL completa se disponível
      if (user.persons[0].profileImageFullUrl) {
        return user.persons[0].profileImageFullUrl;
      }
      // Se não tiver a URL completa, mas tiver a URL relativa, usa o serviço para construir a URL
      else if (user.persons[0].profileImageUrl) {
        return personsService.getProfileImageUrl(user.persons[0].id, user.persons[0].profileImageUrl);
      }
    }
    return null;
  };

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-300 dark:border-gray-700 px-8 py-3 flex justify-between items-center sticky top-0 z-[9000]">
      {/* Lado esquerdo: Logo e Toggle */}
      <div className="flex items-center gap-3">
        <button
          onClick={toggleSidebar}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg lg:hidden text-gray-600 dark:text-gray-300 transition-colors"
          aria-label={isSidebarOpen ? "Fechar menu lateral" : "Abrir menu lateral"}
        >
          {isSidebarOpen ? <X size={22} aria-hidden="true" /> : <Menu size={22} aria-hidden="true" />}
        </button>

        <div className="flex items-center">
          {pathname !== '/dashboard' && activeModule ? (
            <div className="flex items-center gap-3">
              {/* Detalhe colorido do módulo */}
              <span className={
                activeModule.id === 'admin'
                  ? 'w-2 h-10 rounded-full bg-slate-600 dark:bg-slate-400 block mr-2'
                  : `w-2 h-10 rounded-full bg-module-${activeModule.id}-bg dark:bg-module-${activeModule.id}-bg-dark block mr-2`
              } />
              {activeModule.icon && React.createElement(activeModule.icon, { size: 28, className: `text-module-${activeModule.id}-icon dark:text-module-${activeModule.id}-icon-dark` })}
              <div className="flex flex-col">
                <span className="text-xs uppercase tracking-wider font-semibold text-gray-900 dark:text-white">Módulo</span>
                <span className="text-lg font-bold text-gray-900 dark:text-white">{activeModule.title}</span>
              </div>
            </div>
          ) : (
            <div className="relative">
              <img
                src="/logo_horizontal_sem_fundo.png"
                alt="High Tide Logo"
                className="h-10 mr-2.5 dark:invert dark:text-white"
              />
              <span className="absolute -bottom-1 right-3 text-xs text-gray-500 dark:text-gray-400 font-mono">{APP_VERSION}</span>
            </div>
          )}
        </div>
      </div>

      {/* Lado direito: Área do Cliente, Pesquisa e Perfil */}
      <div className="flex items-center gap-3">
        {/* Badge Área do Cliente */}
        <div className="hidden sm:flex items-center gap-2 px-3 py-1.5 bg-purple-50 dark:bg-purple-900/30 border border-purple-200 dark:border-purple-700 rounded-full">
          <User size={14} className="text-purple-600 dark:text-purple-400" />
          <span className="text-sm font-medium text-purple-700 dark:text-purple-300">Área do Cliente</span>
        </div>
        {/* Botão de pesquisa rápida */}
        <button
          onClick={openQuickNav}
          className="flex items-center gap-2 py-2 px-4 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 outline-none transition-colors"
          aria-label="Abrir pesquisa rápida"
        >
          <Search size={18} className="text-gray-400 dark:text-gray-500" aria-hidden="true" />
          <span className="hidden sm:inline">Pesquisar...</span>
          <div className="hidden sm:flex items-center gap-1 ml-2 px-1.5 py-0.5 bg-gray-200 dark:bg-gray-600 rounded text-xs text-gray-500 dark:text-gray-400">
            <span>Ctrl + K</span>
          </div>
        </button>

        {/* Atalhos rápidos para clientes */}
        <button
          onClick={() => router.push('/dashboard/scheduler/calendar')}
          className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          aria-label="Calendário"
          title="Ver calendário"
        >
          <Calendar size={20} aria-hidden="true" />
        </button>

        <button
          onClick={() => router.push('/dashboard/scheduler/appointments-report')}
          className="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          aria-label="Meus Agendamentos"
          title="Ver meus agendamentos"
        >
          <Clock size={20} aria-hidden="true" />
        </button>

        {/* Chat Button */}
        <ChatButton />
        
        {/* Theme Toggle Button */}
        <ThemeToggle />

        {/* Divisor vertical */}
        <div className="h-8 border-l border-gray-200 dark:border-gray-700 mx-1" aria-hidden="true"></div>

        {/* Dropdown de usuário */}
        <div className="relative group">
          <button
            className="flex items-center gap-2 py-1 px-1 rounded-full hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            aria-expanded="false"
            aria-haspopup="true"
            aria-label="Menu do usuário"
          >
            <div className="h-9 w-9 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center font-medium text-purple-600 dark:text-purple-400 overflow-hidden">
              {getProfileImage() ? (
                <img
                  src={getProfileImage()}
                  alt={`Foto de perfil de ${getDisplayName()}`}
                  className="h-10 w-10 rounded-full object-cover"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.style.display = 'none';
                    e.target.parentNode.innerHTML = getInitials();
                  }}
                />
              ) : (
                getInitials()
              )}
            </div>

            <div className="hidden md:block text-left">
              <p className="text-sm font-medium text-gray-800 dark:text-gray-200 line-clamp-1">{getDisplayName()}</p>
              <div className="text-xs text-purple-700 dark:text-purple-300 px-2 py-0.5 rounded-full inline-flex items-center mt-0.5 bg-purple-50 dark:bg-purple-900/30">
                <User size={10} className="mr-1" aria-hidden="true" />
                <span>Cliente</span>
              </div>
            </div>

            <ChevronDown size={16} className="text-gray-400 dark:text-gray-500 hidden md:block" aria-hidden="true" />
          </button>

          {/* Menu dropdown */}
          <div className="absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-md border border-gray-200 dark:border-gray-700 py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-150 origin-top-right"
               role="menu"
               aria-orientation="vertical"
               aria-labelledby="user-menu-button">
            <div className="px-4 py-2 border-b border-gray-100 dark:border-gray-700 md:hidden">
              <p className="text-sm font-medium text-gray-800 dark:text-gray-200">{getDisplayName()}</p>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{user?.email || '<EMAIL>'}</p>
            </div>

            {/* <div className="px-4 py-2">
              <p className="text-xs text-gray-500 dark:text-gray-400">Empresa</p>
              <p className="text-sm font-medium text-gray-800 dark:text-gray-200">{user?.company?.name || 'Minha Empresa'}</p>
            </div> */}

            <div className="pt-1 mt-1">
              <button
                onClick={() => router.push('/dashboard/profile')}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                role="menuitem"
              >
                Meu Perfil
              </button>
              <button
                onClick={() => router.push('/dashboard/people/persons')}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                role="menuitem"
              >
                Minhas Pessoas
              </button>
              <button
                onClick={() => router.push('/dashboard/scheduler/appointments-report')}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                role="menuitem"
              >
                Meus Agendamentos
              </button>
              <button
                onClick={logout}
                className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30 flex items-center transition-colors"
                role="menuitem"
              >
                <LogOut size={14} className="mr-2" aria-hidden="true" />
                Sair do Sistema
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default ClientHeader;
