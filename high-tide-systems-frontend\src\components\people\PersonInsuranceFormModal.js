import React, { useState, useEffect } from "react";
import { AlertCircle, Calendar, CreditCard, FileText } from "lucide-react";
import { ModuleSelect, ModuleInput, ModuleTextarea, ModuleFormGroup } from "@/components/ui";
import { insurancesService } from "@/app/modules/people/services/insurancesService";
import { format } from "date-fns";

const PersonInsuranceFormModal = ({ isOpen, onClose, personId, personInsurance, onSuccess, isCreating = false, onAddTempInsurance }) => {
  const [formData, setFormData] = useState({
    personId: personId,
    insuranceId: "",
    policyNumber: "",
    validUntil: "",
    notes: ""
  });

  const [allInsurances, setAllInsurances] = useState([]);
  const [availableInsurances, setAvailableInsurances] = useState([]);
  const [personInsurances, setPersonInsurances] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Carregar todos os convênios disponíveis no sistema
        const insurancesData = await insurancesService.getInsurances();
        setAllInsurances(insurancesData.insurances || []);

        if (!isCreating && personId) {
          // Carregar convênios já associados a esta pessoa (apenas no modo de edição)
          const personInsurancesData = await insurancesService.listPersonInsurances(personId);
          setPersonInsurances(personInsurancesData);

          // Filtrar convênios disponíveis (que ainda não estão associados à pessoa)
          filterAvailableInsurances(insurancesData.insurances || [], personInsurancesData);
        } else {
          // No modo de criação, todos os convênios estão disponíveis
          setAvailableInsurances(insurancesData.insurances || []);
          setPersonInsurances([]);
        }
      } catch (err) {
        console.error("Erro ao carregar dados:", err);
        setError("Erro ao carregar dados. Por favor, tente novamente.");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      loadData();
    }
  }, [isOpen, personId, isCreating]);

  const filterAvailableInsurances = (allIns, personIns) => {
    // Extrair IDs dos convênios da pessoa
    const personInsuranceIds = personIns.map(pi => {
      if (pi.insuranceId) return pi.insuranceId;
      if (pi.insurance && pi.insurance.id) return pi.insurance.id;
      return pi.id;
    });

    // Filtrar convênios não associados (para o modo de adição)
    const available = allIns.filter(insurance =>
      !personInsuranceIds.includes(insurance.id)
    );

    setAvailableInsurances(available);
  };

  // Preencher formulário quando editando
  useEffect(() => {
    if (personInsurance) {
      let insuranceId = personInsurance.insuranceId ||
                       (personInsurance.insurance ? personInsurance.insurance.id : personInsurance.id);

      let validUntilFormatted = "";
      try {
        if (personInsurance.validUntil) {
          validUntilFormatted = format(new Date(personInsurance.validUntil), "yyyy-MM-dd");
        }
      } catch (e) {
        console.error("Error formatting date:", e);
      }

      setFormData({
        personId: personId,
        insuranceId: insuranceId,
        policyNumber: personInsurance.policyNumber || "",
        validUntil: validUntilFormatted,
        notes: personInsurance.notes || ""
      });
    } else {
      setFormData({
        personId: personId,
        insuranceId: "",
        policyNumber: "",
        validUntil: "",
        notes: ""
      });
    }
  }, [personInsurance, personId]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Converter validUntil para formato ISO se fornecido
      const validUntilFormatted = formData.validUntil
        ? new Date(formData.validUntil + 'T00:00:00Z').toISOString()
        : null;

      // Preparar o payload
      const payload = {
        personId: formData.personId,
        insuranceId: formData.insuranceId,
        policyNumber: formData.policyNumber,
        validUntil: validUntilFormatted,
        notes: formData.notes
      };

      if (isCreating) {
        // Modo de criação - adicionar convênio temporário
        if (onAddTempInsurance) {
          // Adicionar informações do convênio selecionado
          const selectedInsurance = Array.isArray(allInsurances) ? allInsurances.find(ins => ins.id === formData.insuranceId) : null;

          onAddTempInsurance({
            ...payload,
            name: selectedInsurance?.name || "Convênio",
            isTemp: true
          });

          // Exibir toast de sucesso
          if (window.showToast) {
            window.showToast({
              type: "success",
              message: "Convênio adicionado temporariamente"
            });
          }

          // Fechar o modal
          onSuccess();
        }
      } else if (personInsurance) {
        // Modo de edição
        await insurancesService.updatePersonInsurance(personId, formData.insuranceId, {
          policyNumber: formData.policyNumber,
          validUntil: validUntilFormatted,
          notes: formData.notes
        });

        // Chamar callback de sucesso
        onSuccess();

        // Exibir toast de sucesso
        if (window.showToast) {
          window.showToast({
            type: "success",
            message: "Convênio atualizado com sucesso"
          });
        }
      } else {
        // Modo de adição normal
        await insurancesService.addPersonInsurance(payload);

        // Chamar callback de sucesso
        onSuccess();

        // Exibir toast de sucesso
        if (window.showToast) {
          window.showToast({
            type: "success",
            message: "Convênio adicionado com sucesso"
          });
        }
      }
    } catch (err) {
      console.error("Erro ao salvar convênio:", err);
      const errorMessage = err.response?.data?.message || "Ocorreu um erro ao salvar o convênio.";
      setError(errorMessage);

      // Exibir toast de erro se disponível
      if (window.showToast) {
        window.showToast({
          type: "error",
          message: errorMessage
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Função para buscar o nome de um convênio pelo ID
  const getInsuranceName = (id) => {
    if (!Array.isArray(allInsurances)) {
      console.warn("allInsurances não é um array:", allInsurances);
      return "Convênio";
    }
    const insurance = allInsurances.find(ins => ins.id === id);
    return insurance ? insurance.name : "Convênio";
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[13000] flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="fixed left-[50%] top-[50%] z-[13050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-orange-300 dark:border-orange-600 bg-background shadow-lg duration-200 rounded-xl max-w-2xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="pb-4 border-b-2 border-orange-400 dark:border-orange-500 flex-shrink-0 px-6 pt-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg text-white">
              <CreditCard className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-orange-800 dark:text-white border-l-4 border-orange-400 dark:border-orange-500 pl-3">
                {personInsurance ? `Editar ${getInsuranceName(formData.insuranceId)}` : 'Adicionar Convênio'}
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                {personInsurance ? 'Modifique as informações do convênio' : 'Adicione um novo convênio'}
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex flex-col h-full max-h-[calc(90vh-200px)]">
          <div className="flex-1 overflow-y-auto p-6">
            <form id="person-insurance-form" onSubmit={handleSubmit} className="space-y-6">
              <div className="mb-8">
                <div className="border-b-2 border-orange-400 dark:border-orange-500 pb-3 mb-4">
                  <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-orange-500 pl-3">
                    Informações do Convênio
                  </h4>
                  <p className="text-xs text-neutral-600 dark:text-gray-300 pl-3">
                    Dados do convênio médico:
                  </p>
                </div>
              </div>

              {error && (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2">
                  <AlertCircle size={16} />
                  <span>{error}</span>
                </div>
              )}

              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
                </div>
              ) : (
                <>

              {!personInsurance && (
                <ModuleFormGroup
                  moduleColor="people"
                  label="Convênio"
                  htmlFor="insuranceId"
                  icon={<CreditCard size={16} />}
                  required
                  helpText={availableInsurances.length === 0 && !isLoading ? "Todos os convênios já estão associados a esta pessoa." : ""}
                >
                  <ModuleSelect
                    moduleColor="people"
                    id="insuranceId"
                    name="insuranceId"
                    value={formData.insuranceId}
                    onChange={handleChange}
                    required
                    disabled={isSubmitting}
                    placeholder="Selecione um convênio"
                  >
                    {availableInsurances.length === 0 ? (
                      <option disabled>Nenhum convênio disponível</option>
                    ) : (
                      availableInsurances.map(insurance => (
                        <option key={insurance.id} value={insurance.id}>
                          {insurance.name}
                        </option>
                      ))
                    )}
                  </ModuleSelect>
                </ModuleFormGroup>
              )}

              <ModuleFormGroup
                moduleColor="people"
                label="Número da Carteirinha"
                htmlFor="policyNumber"
                icon={<CreditCard size={16} />}
              >
                <ModuleInput
                  moduleColor="people"
                  type="text"
                  id="policyNumber"
                  name="policyNumber"
                  value={formData.policyNumber}
                  onChange={handleChange}
                />
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Validade"
                htmlFor="validUntil"
                icon={<Calendar size={16} />}
              >
                <div className="relative">
                  <ModuleInput
                    moduleColor="people"
                    type="date"
                    id="validUntil"
                    name="validUntil"
                    value={formData.validUntil}
                    onChange={handleChange}
                  />
                  <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 dark:text-neutral-500 pointer-events-none" size={18} />
                </div>
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Observações"
                htmlFor="notes"
                icon={<FileText size={16} />}
              >
                <ModuleTextarea
                  moduleColor="people"
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                />
              </ModuleFormGroup>

                </>
              )}
            </form>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 border-t-2 border-orange-400 dark:border-orange-500 pt-4 flex-shrink-0 px-6 pb-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
              disabled={isSubmitting}
            >
              Cancelar
            </button>
            <button
              type="submit"
              form="person-insurance-form"
              className="px-4 py-2 bg-orange-500 dark:bg-orange-600 text-white rounded-lg hover:bg-orange-600 dark:hover:bg-orange-700 transition-colors flex items-center gap-2"
              disabled={isSubmitting || (!personInsurance && !formData.insuranceId)}
            >
              {isSubmitting ? (
                <>
                  <CreditCard size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  <CreditCard size={16} />
                  <span>{personInsurance ? "Atualizar" : "Adicionar"}</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PersonInsuranceFormModal;